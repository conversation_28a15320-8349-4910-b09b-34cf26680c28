/**
 * InstitutePayoutDashboard Component
 * 
 * Main dashboard for institutes to view payout summary and recent activity
 */

import React, { useState, useEffect } from 'react';
import {
  FiDollarSign,
  FiClock,
  FiTrendingUp,
  FiRefreshCw,
  FiEye,
  FiDownload,
  FiCalendar,
  FiCheck,
  FiX,
  FiAlertCircle
} from 'react-icons/fi';
import { format } from 'date-fns';
import { InstitutePayoutService } from '../../services/institutePayoutService';
import { LoadingSpinner, ErrorMessage } from '../ui';
import { useNotification } from '../../contexts/NotificationContext';

const InstitutePayoutDashboard = () => {
  const { showSuccess, showError } = useNotification();
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboard, setDashboard] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboard();
  }, []);

  const loadDashboard = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await InstitutePayoutService.getPayoutDashboard();
      setDashboard(data);
    } catch (err) {
      setError(err.message || 'Failed to load payout dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadDashboard();
      showSuccess('Dashboard refreshed successfully');
    } catch (err) {
      showError('Failed to refresh dashboard');
    } finally {
      setRefreshing(false);
    }
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <FiCheck className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <FiClock className="w-4 h-4 text-yellow-600" />;
      case 'failed':
        return <FiX className="w-4 h-4 text-red-600" />;
      default:
        return <FiAlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadDashboard} />;
  }

  if (!dashboard) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">No data available</h3>
        <p className="mt-1 text-sm text-gray-500">
          Unable to load payout dashboard data.
        </p>
      </div>
    );
  }

  const summary = InstitutePayoutService.formatRevenueSummary(dashboard);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payout Dashboard</h1>
          <p className="text-gray-600">Track your revenue and payout status</p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiDollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Received</p>
              <p className="text-2xl font-semibold text-gray-900">{summary.totalReceived}</p>
              <p className="text-sm text-gray-600">{summary.totalPayouts} payouts</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiClock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Amount</p>
              <p className="text-2xl font-semibold text-gray-900">{summary.pendingAmount}</p>
              <p className="text-sm text-gray-600">{summary.pendingPayouts} pending</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiTrendingUp className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Payout Rate</p>
              <p className="text-2xl font-semibold text-gray-900">{summary.payoutPercentage}%</p>
              <p className="text-sm text-gray-600">of total revenue</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiCalendar className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">This Month</p>
              <p className="text-2xl font-semibold text-gray-900">
                {InstitutePayoutService.formatCurrency(dashboard.monthly_revenue || 0)}
              </p>
              <p className="text-sm text-gray-600">current month</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bank Account Status */}
      {!dashboard.has_bank_details && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <FiAlertCircle className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Bank Details Required
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                Please add your bank account details to receive payouts.
              </p>
              <div className="mt-3">
                <button className="text-sm font-medium text-yellow-800 hover:text-yellow-900">
                  Add Bank Details →
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Payouts */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Payouts</h2>
          </div>
          <div className="p-6">
            {dashboard.recent_payouts && dashboard.recent_payouts.length > 0 ? (
              <div className="space-y-4">
                {dashboard.recent_payouts.map((payout) => (
                  <div key={payout.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(payout.status)}
                      <div>
                        <p className="font-medium text-gray-900">
                          {InstitutePayoutService.formatCurrency(payout.amount)}
                        </p>
                        <p className="text-sm text-gray-600">
                          {formatDate(payout.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        InstitutePayoutService.getPayoutStatusBadge(payout.status).bgColor
                      } ${InstitutePayoutService.getPayoutStatusBadge(payout.status).textColor}`}>
                        {InstitutePayoutService.getPayoutStatusBadge(payout.status).label}
                      </span>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <FiEye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiDollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No payouts yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Events Pending Payout */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Events Awaiting Payout</h2>
          </div>
          <div className="p-6">
            {dashboard.events_pending_payout && dashboard.events_pending_payout.length > 0 ? (
              <div className="space-y-4">
                {dashboard.events_pending_payout.map((event) => (
                  <div key={event.event_id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{event.event_title}</p>
                      <p className="text-sm text-gray-600">
                        Revenue: {InstitutePayoutService.formatCurrency(event.revenue)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {event.registrations_count} registrations
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <FiEye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No events pending payout</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <FiEye className="w-5 h-5 mr-2 text-gray-600" />
            View Sales Report
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <FiDownload className="w-5 h-5 mr-2 text-gray-600" />
            Download Statement
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <FiDollarSign className="w-5 h-5 mr-2 text-gray-600" />
            Manage Bank Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default InstitutePayoutDashboard;
