import React, { useState, useEffect } from 'react';
import {
  <PERSON>S<PERSON>ch,
  FiBookOpen,
  FiCheck,
  FiX,
  FiClock,
  FiUsers,
  FiPlus
} from 'react-icons/fi';
import { getInstituteExams } from '../../services/examService';
import { LoadingSpinner } from '../ui';

const InstituteExamSelector = ({ 
  isOpen, 
  onClose, 
  onSelect, 
  instituteId,
  selectedExamId = null,
  title = "Select Institute Exam",
  showCreateOption = false,
  onCreateNew = null
}) => {
  const [loading, setLoading] = useState(false);
  const [exams, setExams] = useState([]);
  const [selectedExam, setSelectedExam] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all'); // all, active

  useEffect(() => {
    if (isOpen && instituteId) {
      loadExams();
    }
  }, [isOpen, instituteId]);

  const loadExams = async () => {
    try {
      setLoading(true);
      const response = await getInstituteExams(instituteId);
      setExams(response.exams || []);
    } catch (error) {
      console.error('Error loading institute exams:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredExams = () => {
    let filtered = [...exams];
    
    // Apply status filter
    if (filter === 'active') {
      filtered = filtered.filter(exam => exam.status === 'active');
    }
    
    // Apply search filter
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(exam =>
        exam.title.toLowerCase().includes(search) ||
        exam.description.toLowerCase().includes(search) ||
        exam.created_by?.toLowerCase().includes(search) ||
        exam.tags?.some(tag => tag.toLowerCase().includes(search))
      );
    }
    
    return filtered;
  };

  const handleExamSelect = (exam) => {
    setSelectedExam(exam);
  };

  const handleConfirm = () => {
    if (selectedExam) {
      onSelect(selectedExam);
      onClose();
    }
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const getDifficultyColor = (level) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const getCreatorBadge = (exam) => {
    return <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Institute</span>;
  };

  const ExamCard = ({ exam, isSelected, onClick }) => (
    <div
      onClick={() => onClick(exam)}
      className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
        isSelected 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-medium text-gray-900">{exam.title}</h3>
            {getCreatorBadge(exam)}
          </div>
          <p className="text-sm text-gray-600 line-clamp-2">{exam.description}</p>
        </div>
        {isSelected && (
          <FiCheck className="h-5 w-5 text-blue-600 ml-2 flex-shrink-0" />
        )}
      </div>

      <div className="flex items-center space-x-4 text-xs text-gray-500 mb-3">
        <div className="flex items-center space-x-1">
          <FiBookOpen className="h-3 w-3" />
          <span>{exam.questions_count} questions</span>
        </div>
        <div className="flex items-center space-x-1">
          <FiClock className="h-3 w-3" />
          <span>{formatDuration(exam.duration_minutes)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <FiUsers className="h-3 w-3" />
          <span>Used {exam.usage_count || 0} times</span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exam.difficulty_level)}`}>
          {exam.difficulty_level}
        </span>
        <span className="text-xs text-gray-500">by {exam.created_by || 'Unknown'}</span>
      </div>

      {exam.usage_count > 0 && (
        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
          Currently used in {exam.usage_count} competition(s)
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  const filteredExams = getFilteredExams();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {/* Filters */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search exams..."
                  className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Exams</option>
              <option value="active">Active Only</option>
            </select>

            {showCreateOption && onCreateNew && (
              <button
                onClick={onCreateNew}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <FiPlus className="h-4 w-4 mr-2" />
                Create New
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4" style={{ maxHeight: 'calc(90vh - 250px)' }}>
          {loading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : filteredExams.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredExams.map((exam) => (
                <ExamCard
                  key={exam.id}
                  exam={exam}
                  isSelected={selectedExam?.id === exam.id}
                  onClick={handleExamSelect}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Exams Found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || filter !== 'all'
                  ? 'No exams match your search criteria.'
                  : 'No exams available in your institute library.'}
              </p>
              {showCreateOption && onCreateNew && (
                <button
                  onClick={onCreateNew}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <FiPlus className="h-4 w-4 mr-2" />
                  Create Your First Exam
                </button>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            {selectedExam ? (
              <span>Selected: <strong>{selectedExam.title}</strong></span>
            ) : (
              <span>Select an exam to continue</span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!selectedExam}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Select Exam
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstituteExamSelector;
