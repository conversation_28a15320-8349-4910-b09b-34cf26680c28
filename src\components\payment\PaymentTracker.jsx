/**
 * PaymentTracker Component
 * 
 * Handles real-time payment tracking via WebSocket with fallback to HTTP polling.
 * Displays payment status updates and manages the payment flow according to the
 * backend WebSocket payment pipeline implementation.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiClock,
  FiCheck,
  FiX,
  FiWifi,
  FiWifiOff,
  FiLoader,
  FiAlertCircle,
  FiCreditCard,
  FiRefreshCw
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import paymentWebSocketService from '../../services/paymentWebSocketService';
import { LoadingSpinner } from '../ui';

const PaymentTracker = ({ 
  bookingId, 
  onPaymentSuccess, 
  onPaymentFailure, 
  onTimeout,
  autoRedirect = true,
  className = '' 
}) => {
  const navigate = useNavigate();
  const { showSuccess, showError, showInfo } = useNotification();
  
  // Component state
  const [status, setStatus] = useState('connecting'); // connecting, connected, pending, success, failure, timeout
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(15 * 60); // 15 minutes in seconds
  const [usingFallback, setUsingFallback] = useState(false);

  // Timer for countdown
  useEffect(() => {
    if (status === 'pending' && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
      
      return () => clearTimeout(timer);
    } else if (timeRemaining <= 0 && status === 'pending') {
      handleTimeout();
    }
  }, [timeRemaining, status]);

  // Setup WebSocket connection
  useEffect(() => {
    if (!bookingId) {
      setError('Booking ID is required');
      return;
    }

    setupWebSocketListeners();
    connectToPaymentTracking();

    return () => {
      paymentWebSocketService.disconnect();
    };
  }, [bookingId]);

  /**
   * Setup WebSocket event listeners
   */
  const setupWebSocketListeners = useCallback(() => {
    // Connection events
    paymentWebSocketService.on('connected', () => {
      setConnectionStatus('connected');
      setStatus('pending');
      setError(null);
      showInfo('Connected to payment tracking');
    });

    paymentWebSocketService.on('disconnected', () => {
      setConnectionStatus('disconnected');
    });

    paymentWebSocketService.on('error', (error) => {
      setError(error.message || 'Connection error');
      setConnectionStatus('error');
    });

    paymentWebSocketService.on('fallback_started', () => {
      setUsingFallback(true);
      showInfo('Using backup connection for payment tracking');
    });

    // Payment events
    paymentWebSocketService.on('payment_success', handlePaymentSuccess);
    paymentWebSocketService.on('payment_failure', handlePaymentFailure);
    paymentWebSocketService.on('payment_pending', handlePaymentPending);
    paymentWebSocketService.on('payment_timeout', handleTimeout);
  }, []);

  /**
   * Connect to payment tracking
   */
  const connectToPaymentTracking = async () => {
    try {
      setStatus('connecting');
      setConnectionStatus('connecting');
      await paymentWebSocketService.connect(bookingId);
    } catch (error) {
      setError(error.message);
      setConnectionStatus('error');
    }
  };

  /**
   * Handle payment success
   */
  const handlePaymentSuccess = useCallback((data) => {
    setStatus('success');
    setPaymentData(data);
    showSuccess('Payment completed successfully!');
    
    if (onPaymentSuccess) {
      onPaymentSuccess(data);
    }
    
    if (autoRedirect) {
      setTimeout(() => {
        navigate('/events/my-tickets');
      }, 3000);
    }
  }, [onPaymentSuccess, autoRedirect, navigate, showSuccess]);

  /**
   * Handle payment failure
   */
  const handlePaymentFailure = useCallback((data) => {
    setStatus('failure');
    setPaymentData(data);
    showError(data.reason || 'Payment failed');
    
    if (onPaymentFailure) {
      onPaymentFailure(data);
    }
  }, [onPaymentFailure, showError]);

  /**
   * Handle payment pending updates
   */
  const handlePaymentPending = useCallback((data) => {
    setPaymentData(data);
    // Keep status as pending, just update data
  }, []);

  /**
   * Handle payment timeout
   */
  const handleTimeout = useCallback(() => {
    setStatus('timeout');
    showError('Payment session has timed out');
    
    if (onTimeout) {
      onTimeout();
    }
  }, [onTimeout, showError]);

  /**
   * Retry connection
   */
  const retryConnection = () => {
    setError(null);
    connectToPaymentTracking();
  };

  /**
   * Format time remaining
   */
  const formatTimeRemaining = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  /**
   * Get status icon
   */
  const getStatusIcon = () => {
    switch (status) {
      case 'connecting':
        return <FiLoader className="animate-spin" />;
      case 'pending':
        return <FiClock className="animate-pulse" />;
      case 'success':
        return <FiCheck className="text-green-500" />;
      case 'failure':
        return <FiX className="text-red-500" />;
      case 'timeout':
        return <FiAlertCircle className="text-orange-500" />;
      default:
        return <FiCreditCard />;
    }
  };

  /**
   * Get connection icon
   */
  const getConnectionIcon = () => {
    if (usingFallback) {
      return <FiRefreshCw className="text-orange-500" title="Using backup connection" />;
    }
    
    switch (connectionStatus) {
      case 'connected':
        return <FiWifi className="text-green-500" title="Connected" />;
      case 'connecting':
        return <FiLoader className="animate-spin text-blue-500" title="Connecting" />;
      case 'disconnected':
      case 'error':
        return <FiWifiOff className="text-red-500" title="Disconnected" />;
      default:
        return <FiWifiOff className="text-gray-400" />;
    }
  };

  /**
   * Get status message
   */
  const getStatusMessage = () => {
    switch (status) {
      case 'connecting':
        return 'Connecting to payment tracking...';
      case 'pending':
        return 'Waiting for payment completion...';
      case 'success':
        return 'Payment completed successfully!';
      case 'failure':
        return paymentData?.reason || 'Payment failed';
      case 'timeout':
        return 'Payment session has timed out';
      default:
        return 'Initializing payment tracking...';
    }
  };

  return (
    <div className={`max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Payment Tracking</h2>
          <div className="flex items-center space-x-2">
            {getConnectionIcon()}
            {getStatusIcon()}
          </div>
        </div>
        <p className="text-blue-100">
          {getStatusMessage()}
        </p>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Time remaining */}
        {status === 'pending' && (
          <div className="mb-6 text-center">
            <div className="text-2xl font-mono font-bold text-gray-800 mb-2">
              {formatTimeRemaining(timeRemaining)}
            </div>
            <p className="text-sm text-gray-600">Time remaining to complete payment</p>
          </div>
        )}

        {/* Status indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-center mb-4">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl ${
              status === 'success' ? 'bg-green-100' :
              status === 'failure' || status === 'timeout' ? 'bg-red-100' :
              status === 'pending' ? 'bg-blue-100' :
              'bg-gray-100'
            }`}>
              {getStatusIcon()}
            </div>
          </div>
          
          {/* Connection status */}
          <div className="text-center text-sm text-gray-600 mb-4">
            {usingFallback && (
              <div className="flex items-center justify-center space-x-1 text-orange-600">
                <FiRefreshCw className="w-4 h-4" />
                <span>Using backup connection</span>
              </div>
            )}
          </div>
        </div>

        {/* Error handling */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2 text-red-700">
              <FiAlertCircle className="w-5 h-5" />
              <span className="font-medium">Connection Error</span>
            </div>
            <p className="text-red-600 text-sm mt-1">{error}</p>
            <button
              onClick={retryConnection}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700 transition-colors"
            >
              Retry Connection
            </button>
          </div>
        )}

        {/* Success message */}
        {status === 'success' && paymentData && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-green-700 font-medium mb-2">Payment Successful!</div>
            {paymentData.ticket_data && (
              <div className="text-sm text-green-600">
                <p>Booking ID: {paymentData.booking_id}</p>
                {autoRedirect && (
                  <p className="mt-2">Redirecting to your tickets...</p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Failure message */}
        {(status === 'failure' || status === 'timeout') && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-red-700 font-medium mb-2">
              {status === 'timeout' ? 'Payment Timeout' : 'Payment Failed'}
            </div>
            <p className="text-sm text-red-600">
              {paymentData?.reason || 'Please try again or contact support if the problem persists.'}
            </p>
          </div>
        )}

        {/* Loading state */}
        {(status === 'connecting' || status === 'pending') && (
          <div className="text-center">
            <LoadingSpinner size="sm" className="mb-2" />
            <p className="text-sm text-gray-600">
              {status === 'connecting' ? 'Establishing connection...' : 'Processing payment...'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentTracker;
