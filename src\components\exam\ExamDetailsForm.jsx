import { FiBookOpen, FiEdit3, FiTarget, FiCalendar, FiClock, FiGlobe } from 'react-icons/fi';
import { DateTimeInput } from '../ui/FormComponents';
import useTimezone from '../../hooks/useTimezone';

const ExamDetailsForm = ({
  exam,
  onExamChange,
  classrooms,
  subjects,
  classes,
  classId,
  subjectId,
  classNumber,
  assignmentType,
  onClassChange,
  onSubjectChange,
  onClassNumberChange,
  themeClasses,
  examDetailsValid,
  userType = 'teacher' // Default to teacher for backward compatibility
}) => {
  const { timezoneData, loading: timezoneLoading } = useTimezone();

  return (
    <div className="space-y-8 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Basic Exam Information */}
      <div className="space-y-6">
        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <FiBookOpen className="w-5 h-5 text-blue-600" />
            {userType === 'institute' ? 'Question Bank Details' : 'Exam Information'}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {userType === 'institute' 
              ? 'Create a reusable question bank for competitions' 
              : 'Basic information about your exam'}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label className={`block mb-3 font-semibold ${themeClasses.label} flex items-center gap-2`}>
              <FiEdit3 className="w-4 h-4 text-blue-600" />
              {userType === 'institute' ? 'Question Bank Title' : 'Exam Title'} 
              <span className="text-red-500">*</span>
            </label>
            <input
              key="exam-title-input"
              type="text"
              name="title"
              value={exam.title}
              onChange={onExamChange}
              className={`w-full rounded-xl px-4 py-3 border-2 border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${themeClasses.input} shadow-sm hover:shadow-md`}
              placeholder={userType === 'institute' 
                ? "Enter question bank title (e.g., Mathematics Advanced Problems)" 
                : "Enter exam title (e.g., Mathematics Mid-term Exam)"}
              required
            />
          </div>

          <div className="md:col-span-2">
            <label className={`block mb-3 font-semibold ${themeClasses.label} flex items-center gap-2`}>
              <FiEdit3 className="w-4 h-4 text-green-600" />
              Description
            </label>
            <textarea
              key="exam-description-input"
              name="description"
              value={exam.description}
              onChange={onExamChange}
              rows={4}
              className={`w-full rounded-xl px-4 py-3 border-2 border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 resize-none ${themeClasses.input} shadow-sm hover:shadow-md`}
              placeholder={userType === 'institute' 
                ? "Describe the topics and difficulty level of this question bank..." 
                : "Provide exam instructions and description..."}
            />
          </div>
        </div>
      </div>

      {/* Class, Subject, and Class Number Selection - Only for Teachers */}
      {userType === 'teacher' && (
        <div className="space-y-6">
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <FiTarget className="w-5 h-5 text-purple-600" />
              Assignment Details
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Select the class, subject, and target students for this exam
            </p>
          </div>
          
          <div className={`grid grid-cols-1 gap-6 ${assignmentType === 'classroom' ? 'md:grid-cols-3' : 'md:grid-cols-2'}`}>
            {/* Only show Classroom field when assigning to entire classroom */}
            {assignmentType === 'classroom' && (
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                  <FiTarget className="w-4 h-4" />
                  Classroom <span className="text-red-500">*</span>
                </label>
                <select
                  value={classId}
                  onChange={onClassChange}
                  className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                  required
                >
                  <option value="">Select Classroom</option>
                  {classrooms && classrooms.map(cls => (
                    <option key={cls.id} value={cls.id}>{cls.name}</option>
                  ))}
                </select>
              </div>
            )}

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiBookOpen className="w-4 h-4" />
                Subject <span className="text-red-500">*</span>
              </label>
              <select
                value={subjectId}
                onChange={onSubjectChange}
                className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                required
              >
                <option value="">Select Subject</option>
                {subjects && subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>{subject.name}</option>
                ))}
              </select>
            </div>

            {/* Class Number field - always visible and required */}
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiTarget className="w-4 h-4" />
                Class Number <span className="text-red-500">*</span>
              </label>
              <select
                value={classNumber}
                onChange={onClassNumberChange}
                className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                required
              >
                <option value="">Select Class Number</option>
                {classes && classes.map(cls => (
                  <option key={cls.id} value={cls.ClassNo}>
                    {cls.ClassNo}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Timing Information - Only for Teachers */}
      {userType === 'teacher' && (
        <div className="space-y-6">
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <FiClock className="w-5 h-5 text-orange-600" />
              Exam Timing
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Set the schedule and duration for this exam
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiCalendar className="w-4 h-4" />
                Start Date & Time <span className="text-red-500">*</span>
              </label>
              <DateTimeInput
                name="start_time"
                value={exam.start_time}
                onChange={onExamChange}
                className="w-full"
                inputClassName={`rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                required
              />
              {/* Timezone Information */}
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
                <FiGlobe className="w-4 h-4" />
                {timezoneLoading ? (
                  <span>🌍 Detecting your location...</span>
                ) : timezoneData && timezoneData.detected ? (
                  <span>Your timezone: {timezoneData.city}, {timezoneData.country} ({timezoneData.timezone})</span>
                ) : (
                  <span>Your timezone: {timezoneData?.timezone || 'Unknown'}</span>
                )}
              </div>
              <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                Times are automatically converted to your local timezone
              </div>
            </div>

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiClock className="w-4 h-4" />
                Duration (minutes) <span className="text-red-500">*</span>
              </label>
              <input
                key="exam-duration-input"
                type="number"
                name="total_duration"
                value={exam.total_duration}
                onChange={onExamChange}
                min={1}
                max={480}
                className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                placeholder="e.g., 60"
                required
              />
            </div>
          </div>
        </div>
      )}

      {/* Class and Subject Selection - Only for Institutes */}
      {userType === 'institute' && (
        <div className="space-y-6">
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <FiTarget className="w-5 h-5 text-purple-600" />
              Question Bank Classification
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Categorize your question bank by class and subject
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiTarget className="w-4 h-4" />
                Class <span className="text-red-500">*</span>
              </label>
              <select
                value={classNumber}
                onChange={onClassNumberChange}
                className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                required
              >
                <option value="">Select Class</option>
                {classes && classes.map(cls => (
                  <option key={cls.id} value={cls.ClassNo}>
                    {cls.ClassNo}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiBookOpen className="w-4 h-4" />
                Subject <span className="text-red-500">*</span>
              </label>
              <select
                value={subjectId}
                onChange={onSubjectChange}
                className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                required
              >
                <option value="">Select Subject</option>
                {subjects && subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>{subject.name}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Institute-specific note */}
      {userType === 'institute' && (
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 p-6">
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-100 dark:bg-blue-800/30 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
          <div className="relative flex items-start gap-4">
            <div className="flex-shrink-0 w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <FiBookOpen className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Question Bank Mode
              </h4>
              <p className="text-blue-700 dark:text-blue-200 text-sm leading-relaxed">
                This exam will be used as a reusable question bank for competitions.
                Timing, scheduling, and student assignment will be handled at the competition level,
                giving you maximum flexibility for different events.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Validation Status */}
      {examDetailsValid && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
          <FiBookOpen className="w-4 h-4" />
          <span className="text-sm font-medium">Exam details are complete!</span>
        </div>
      )}
    </div>
  );
};

export default ExamDetailsForm;
