# 🎯 Event Registration & Ticket Buying API Design

## Overview
Complete API design for event management, registration, and ticket purchasing system. This design follows REST principles and integrates with PayFast for payment processing.

## 🚨 Current Status
- ❌ **Backend Event Endpoints**: Not implemented
- ✅ **Frontend Event Components**: Exist but use mock data
- 🔄 **Required**: Complete backend implementation

## 📋 Core API Endpoints

### 1. Event Management

#### Get All Events (Public)
```
GET /api/events
Query Parameters:
- category: string (optional) - Filter by event category
- status: string (optional) - Filter by event status
- featured: boolean (optional) - Show only featured events
- upcoming: boolean (optional) - Show only upcoming events
- search: string (optional) - Search in title/description
- page: integer (default: 1) - Pagination
- limit: integer (default: 20) - Items per page
```

**Response:**
```json
{
  "events": [
    {
      "id": "uuid",
      "title": "Event Title",
      "description": "Event description",
      "short_description": "Brief description",
      "category": "WORKSHOP|SEMINAR|CONFERENCE|COMPETITION|NETWORKING",
      "status": "DRAFT|PUBLISHED|CANCELLED|COMPLETED",
      "start_datetime": "2024-12-01T10:00:00Z",
      "end_datetime": "2024-12-01T18:00:00Z",
      "location": "Event Location",
      "banner_image_url": "https://...",
      "is_featured": true,
      "is_public": true,
      "max_attendees": 100,
      "current_registrations": 45,
      "registration_start": "2024-11-01T00:00:00Z",
      "registration_end": "2024-11-30T23:59:59Z",
      "organizer": {
        "id": "uuid",
        "name": "Organizer Name",
        "email": "<EMAIL>"
      },
      "tickets": [
        {
          "id": "uuid",
          "name": "General Admission",
          "description": "Standard ticket",
          "price": 150.00,
          "currency": "ZAR",
          "available_quantity": 50,
          "max_per_user": 5,
          "status": "ACTIVE|SOLD_OUT|INACTIVE"
        }
      ],
      "created_at": "2024-10-01T10:00:00Z",
      "updated_at": "2024-10-15T14:30:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 20,
  "has_next": true,
  "has_prev": false
}
```

#### Get Event Details
```
GET /api/events/{event_id}
```

**Response:**
```json
{
  "id": "uuid",
  "title": "Event Title",
  "description": "Full event description",
  "short_description": "Brief description",
  "category": "WORKSHOP",
  "status": "PUBLISHED",
  "start_datetime": "2024-12-01T10:00:00Z",
  "end_datetime": "2024-12-01T18:00:00Z",
  "location": "Event Location",
  "banner_image_url": "https://...",
  "gallery_images": ["https://...", "https://..."],
  "is_featured": true,
  "is_public": true,
  "requires_approval": false,
  "max_attendees": 100,
  "min_attendees": 10,
  "current_registrations": 45,
  "registration_start": "2024-11-01T00:00:00Z",
  "registration_end": "2024-11-30T23:59:59Z",
  "requirements": "Bring laptop and notebook",
  "agenda": [
    {
      "time": "10:00",
      "title": "Opening Session",
      "description": "Welcome and introduction"
    }
  ],
  "organizer": {
    "id": "uuid",
    "name": "Organizer Name",
    "email": "<EMAIL>",
    "profile_picture": "https://..."
  },
  "tickets": [
    {
      "id": "uuid",
      "name": "General Admission",
      "description": "Standard ticket with all access",
      "price": 150.00,
      "currency": "ZAR",
      "available_quantity": 50,
      "max_per_user": 5,
      "status": "ACTIVE",
      "features": ["Access to all sessions", "Lunch included", "Certificate"]
    },
    {
      "id": "uuid",
      "name": "VIP Pass",
      "description": "Premium ticket with extras",
      "price": 300.00,
      "currency": "ZAR",
      "available_quantity": 20,
      "max_per_user": 2,
      "status": "ACTIVE",
      "features": ["All General features", "VIP seating", "Meet & greet"]
    }
  ],
  "user_registration": {
    "is_registered": false,
    "registration_id": null,
    "ticket_name": null,
    "quantity": 0,
    "status": null
  },
  "created_at": "2024-10-01T10:00:00Z",
  "updated_at": "2024-10-15T14:30:00Z"
}
```

#### Create Event (Organizers/Admins)
```
POST /api/events
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "title": "Event Title",
  "description": "Full event description",
  "short_description": "Brief description",
  "category": "WORKSHOP",
  "start_datetime": "2024-12-01T10:00:00Z",
  "end_datetime": "2024-12-01T18:00:00Z",
  "location": "Event Location",
  "banner_image_url": "https://...",
  "gallery_images": ["https://...", "https://..."],
  "is_featured": false,
  "is_public": true,
  "requires_approval": false,
  "max_attendees": 100,
  "min_attendees": 10,
  "registration_start": "2024-11-01T00:00:00Z",
  "registration_end": "2024-11-30T23:59:59Z",
  "requirements": "Bring laptop and notebook",
  "agenda": [
    {
      "time": "10:00",
      "title": "Opening Session",
      "description": "Welcome and introduction"
    }
  ],
  "tickets": [
    {
      "name": "General Admission",
      "description": "Standard ticket",
      "price": 150.00,
      "currency": "ZAR",
      "available_quantity": 50,
      "max_per_user": 5
    }
  ]
}
```

### 2. Event Registration & Ticket Purchase

#### Register for Event (Both Free and Paid)
```
POST /api/events/{event_id}/register
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "ticket_id": "uuid",
  "quantity": 1,
  "attendee_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+27123456789",
    "dietary_requirements": "Vegetarian",
    "special_needs": "Wheelchair access",
    "emergency_contact": "+27987654321"
  }
}
```

**Response (Free Ticket):**
```json
{
  "success": true,
  "registration_id": "uuid",
  "registration_number": "REG-2024-001",
  "status": "CONFIRMED",
  "payment_status": "COMPLETED",
  "event": {
    "id": "uuid",
    "title": "Event Title",
    "start_datetime": "2024-12-01T10:00:00Z",
    "location": "Event Location"
  },
  "ticket": {
    "id": "uuid",
    "name": "Free Entry",
    "price": 0.00,
    "quantity": 1,
    "total_amount": 0.00,
    "currency": "ZAR"
  },
  "attendee": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "qr_code": "base64_encoded_qr_code",
  "check_in_code": "CHK-123456",
  "message": "Registration confirmed successfully"
}
```

**Response (Paid Ticket):**
```json
{
  "success": true,
  "registration_id": "uuid",
  "registration_number": "REG-2024-002",
  "status": "PENDING_PAYMENT",
  "payment_status": "PENDING",
  "event": {
    "id": "uuid",
    "title": "Event Title",
    "start_datetime": "2024-12-01T10:00:00Z",
    "location": "Event Location"
  },
  "ticket": {
    "id": "uuid",
    "name": "General Admission",
    "price": 150.00,
    "quantity": 2,
    "total_amount": 300.00,
    "currency": "ZAR"
  },
  "attendee": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "qr_code": null,
  "check_in_code": null,
  "message": "Registration created - payment required to complete"
}
```

#### Pay for Registration
```
POST /api/events/registrations/{registration_id}/pay
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "registration_id": "uuid",
  "payment_required": true,
  "payment_url": "https://sandbox.payfast.co.za/eng/process?...",
  "payment_reference": "PAY-2024-002",
  "amount": 300.00,
  "currency": "ZAR",
  "expires_at": "2024-11-01T11:00:00Z",
  "message": "Payment link created - complete payment to confirm registration"
}
```

### 3. Registration Management

#### Get User Registrations
```
GET /api/events/my-registrations
Authorization: Bearer {token}
Query Parameters:
- status: string (optional) - Filter by registration status
- upcoming: boolean (optional) - Show only upcoming events
- page: integer (default: 1)
- limit: integer (default: 20)
```

**Response:**
```json
{
  "registrations": [
    {
      "registration_id": "uuid",
      "registration_number": "REG-2024-001",
      "status": "CONFIRMED|PENDING_PAYMENT|CANCELLED",
      "registered_at": "2024-11-01T10:00:00Z",
      "event": {
        "id": "uuid",
        "title": "Event Title",
        "start_datetime": "2024-12-01T10:00:00Z",
        "location": "Event Location",
        "banner_image_url": "https://..."
      },
      "ticket": {
        "name": "General Admission",
        "price": 150.00,
        "quantity": 1,
        "total_amount": 150.00
      },
      "payment": {
        "status": "COMPLETED|PENDING|FAILED",
        "reference": "PAY-2024-001",
        "amount": 150.00,
        "currency": "ZAR"
      },
      "qr_code": "base64_encoded_qr_code",
      "check_in_code": "CHK-123456",
      "can_cancel": true,
      "can_modify": false
    }
  ],
  "total": 5,
  "page": 1,
  "limit": 20
}
```

#### Get Registration Details
```
GET /api/events/registrations/{registration_id}
Authorization: Bearer {token}
```

#### Cancel Registration
```
DELETE /api/events/registrations/{registration_id}
Authorization: Bearer {token}
```

### 4. Payment Integration

#### PayFast Webhook
```
POST /api/events/payments/payfast/webhook
Content-Type: application/x-www-form-urlencoded
```

**Webhook Data:**
```
m_payment_id=12345
pf_payment_id=67890
payment_status=COMPLETE
item_name=Event Registration
amount_gross=150.00
amount_fee=3.45
amount_net=146.55
custom_str1=registration_id
custom_str2=user_id
signature=generated_signature
```

### 5. Event Analytics (Organizers)

#### Get Event Statistics
```
GET /api/events/{event_id}/stats
Authorization: Bearer {token}
```

**Response:**
```json
{
  "event_id": "uuid",
  "total_registrations": 45,
  "confirmed_registrations": 40,
  "pending_payments": 3,
  "cancelled_registrations": 2,
  "total_revenue": 6750.00,
  "currency": "ZAR",
  "ticket_sales": [
    {
      "ticket_name": "General Admission",
      "sold": 35,
      "revenue": 5250.00
    },
    {
      "ticket_name": "VIP Pass",
      "sold": 5,
      "revenue": 1500.00
    }
  ],
  "registration_timeline": [
    {
      "date": "2024-11-01",
      "registrations": 10
    }
  ]
}
```

## 🔐 Authentication & Authorization

### Required Permissions
- **Public Events**: No authentication required for viewing
- **Event Registration**: Authenticated users only
- **Event Creation**: Organizers, Institutes, Admins only
- **Event Management**: Event owners and Admins only
- **Analytics**: Event owners and Admins only

### User Types & Permissions
```json
{
  "student": ["register", "view_own_registrations"],
  "teacher": ["register", "view_own_registrations"],
  "institute": ["create_events", "manage_own_events", "view_analytics"],
  "sponsor": ["create_events", "manage_own_events", "view_analytics"],
  "admin": ["all_permissions"]
}
```

## 📊 Database Schema Requirements

### Events Table
```sql
CREATE TABLE events (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    category VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'DRAFT',
    start_datetime TIMESTAMP NOT NULL,
    end_datetime TIMESTAMP NOT NULL,
    location VARCHAR(255),
    banner_image_url VARCHAR(500),
    gallery_images JSON,
    is_featured BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    max_attendees INTEGER,
    min_attendees INTEGER DEFAULT 1,
    registration_start TIMESTAMP,
    registration_end TIMESTAMP,
    requirements TEXT,
    agenda JSON,
    organizer_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (organizer_id) REFERENCES users(id)
);
```

### Tickets Table
```sql
CREATE TABLE tickets (
    id UUID PRIMARY KEY,
    event_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'ZAR',
    available_quantity INTEGER NOT NULL,
    max_per_user INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'ACTIVE',
    features JSON,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);
```

### Registrations Table
```sql
CREATE TABLE registrations (
    id UUID PRIMARY KEY,
    registration_number VARCHAR(50) UNIQUE NOT NULL,
    event_id UUID NOT NULL,
    user_id UUID NOT NULL,
    ticket_id UUID NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    status VARCHAR(50) DEFAULT 'PENDING',
    attendee_info JSON NOT NULL,
    payment_reference VARCHAR(255),
    payment_status VARCHAR(50) DEFAULT 'PENDING',
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    qr_code TEXT,
    check_in_code VARCHAR(20),
    registered_at TIMESTAMP DEFAULT NOW(),
    confirmed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (event_id) REFERENCES events(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (ticket_id) REFERENCES tickets(id),
    UNIQUE(event_id, user_id, ticket_id)
);
```

## 🚀 Implementation Priority

### Phase 1: Core Event Management
1. Events CRUD operations
2. Ticket management
3. Basic event listing and details

### Phase 2: Registration System
1. Free event registration
2. User registration management
3. QR code generation

### Phase 3: Payment Integration
1. PayFast integration
2. Paid event registration
3. Payment webhook handling

### Phase 4: Advanced Features
1. Event analytics
2. Registration management
3. Email notifications

This API design provides a complete foundation for event management and ticket purchasing that the frontend can implement against.
