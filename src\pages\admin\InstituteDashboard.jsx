import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  fetchInstituteDashboardSummary,
  selectSummary,
  selectSummaryLoading,
} from '../../store/slices/InstituteDashboardSlice';
import { PageContainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiUsers,
  FiUserCheck,
  FiCalendar,
  FiAward,
  FiPlus,
  FiEye,
  FiSettings,
  FiBookOpen
} from 'react-icons/fi';

function InstituteDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const summary = useSelector(selectSummary);
  const loading = useSelector(selectSummaryLoading);

  useEffect(() => {
    dispatch(fetchInstituteDashboardSummary());
  }, [dispatch]);

  const stats = useMemo(() => [
    {
      key: 'mentors',
      title: 'Total Mentors',
      value: summary?.totalMentors || 0,
      icon: FiUsers,
      color: 'blue',
      onClick: () => navigate('/institute/mentors')
    },
    {
      key: 'active-mentors',
      title: 'Active Mentors',
      value: summary?.activeMentors || 0,
      icon: FiUserCheck,
      color: 'green'
    },
    {
      key: 'events',
      title: 'Total Events',
      value: summary?.totalEvents || 0,
      icon: FiCalendar,
      color: 'purple',
      onClick: () => navigate('/institute/events')
    },
    {
      key: 'attendees',
      title: 'Event Attendees',
      value: summary?.totalEventAttendees || 0,
      icon: FiAward,
      color: 'indigo'
    }
  ], [summary, navigate]);

  const quickActions = useMemo(() => [
    {
      key: 'create-event',
      title: 'Create Event',
      description: 'Organize a new institute event',
      icon: FiPlus,
      color: 'green',
      onClick: () => navigate('/institute/events/create')
    },
    {
      key: 'manage-exams',
      title: 'Manage Exams',
      description: 'Create and manage exam library',
      icon: FiBookOpen,
      color: 'indigo',
      onClick: () => navigate('/institute/exams')
    },
    {
      key: 'view-mentors',
      title: 'View Mentors',
      description: 'Manage institute mentors',
      icon: FiEye,
      color: 'blue',
      onClick: () => navigate('/institute/mentors')
    },
    {
      key: 'institute-settings',
      title: 'Settings',
      description: 'Configure institute preferences',
      icon: FiSettings,
      color: 'purple',
      onClick: () => navigate('/institute/settings')
    }
  ], [navigate]);

  return (
    <PageContainer>
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
          Institute Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage your institute's mentors and events
        </p>
      </div>

      <Stack gap="lg">
        <DashboardGrid stats={stats} loading={loading} />
        <QuickActions actions={quickActions} />
      </Stack>
    </PageContainer>
  );
}

export default InstituteDashboard;
