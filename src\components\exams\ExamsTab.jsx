import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Fi<PERSON>earch,
  Fi<PERSON>ilter,
  FiBook<PERSON>pen,
  FiGrid,
  FiList,
  FiInfo
} from 'react-icons/fi';
import { 
  getMyExams, 
  deleteExam, 
  copyExam, 
  getExamUsageInfo 
} from '../../services/examService';
import { LoadingSpinner, ErrorMessage } from '../ui';
import ExamCard from './ExamCard';
import ExamForm from './ExamForm';

const ExamsTab = ({ onExamSelect = null, selectionMode = false }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [selectedExam, setSelectedExam] = useState(null);
  const [showExamForm, setShowExamForm] = useState(false);
  const [editingExam, setEditingExam] = useState(null);
  const [showUsageInfo, setShowUsageInfo] = useState(false);
  const [usageInfo, setUsageInfo] = useState(null);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [filters, setFilters] = useState({
    search: '',
    difficulty: 'all',
    status: 'all',
    tags: []
  });

  useEffect(() => {
    loadExams();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [exams, filters]);

  const loadExams = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getMyExams();
      setExams(response.exams || []);
    } catch (err) {
      console.error('Error loading exams:', err);
      setError('Failed to load exams');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...exams];

    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(exam =>
        exam.title.toLowerCase().includes(searchTerm) ||
        exam.description.toLowerCase().includes(searchTerm) ||
        exam.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Difficulty filter
    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(exam => exam.difficulty_level === filters.difficulty);
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(exam => exam.status === filters.status);
    }

    setFilteredExams(filtered);
  };

  const handleCreateExam = () => {
    setEditingExam(null);
    setShowExamForm(true);
  };

  const handleEditExam = (exam) => {
    setEditingExam(exam);
    setShowExamForm(true);
  };

  const handleViewExam = (exam) => {
    // Navigate to exam details page or show exam preview
    console.log('View exam:', exam);
  };

  const handleDeleteExam = async (exam) => {
    if (!window.confirm(`Are you sure you want to delete "${exam.title}"?`)) {
      return;
    }

    try {
      await deleteExam(exam.id);
      await loadExams();
    } catch (err) {
      console.error('Error deleting exam:', err);
      setError('Failed to delete exam');
    }
  };

  const handleCopyExam = async (exam) => {
    try {
      const copiedExam = await copyExam(exam.id, {
        title: `${exam.title} (Copy)`,
        description: `Copy of ${exam.description}`
      });
      
      await loadExams();
      
      // Optionally edit the copied exam immediately
      const newExam = exams.find(e => e.id === copiedExam.id);
      if (newExam) {
        handleEditExam(newExam);
      }
    } catch (err) {
      console.error('Error copying exam:', err);
      setError('Failed to copy exam');
    }
  };

  const handleViewUsage = async (exam) => {
    try {
      setSelectedExam(exam);
      const usage = await getExamUsageInfo(exam.id);
      setUsageInfo(usage);
      setShowUsageInfo(true);
    } catch (err) {
      console.error('Error fetching usage info:', err);
      setError('Failed to fetch usage information');
    }
  };

  const handleExamFormSuccess = () => {
    setShowExamForm(false);
    setEditingExam(null);
    loadExams();
  };

  const handleExamSelection = (exam) => {
    if (selectionMode && onExamSelect) {
      onExamSelect(exam);
    }
  };

  const StatCard = ({ icon: Icon, title, value, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-2 rounded-lg bg-${color}-100`}>
          <Icon className={`h-5 w-5 text-${color}-600`} />
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-lg font-semibold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Calculate statistics
  const totalExams = exams.length;
  const activeExams = exams.filter(e => e.status === 'active').length;
  const draftExams = exams.filter(e => e.status === 'draft').length;
  const totalUsage = exams.reduce((sum, e) => sum + (e.usage_count || 0), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Exams</h2>
          <p className="text-gray-600 mt-1">Create and manage your exam library</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
            >
              <FiGrid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
            >
              <FiList className="h-4 w-4" />
            </button>
          </div>
          
          <button
            onClick={handleCreateExam}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Exam
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard
          icon={FiBookOpen}
          title="Total Exams"
          value={totalExams}
          color="blue"
        />
        <StatCard
          icon={FiBookOpen}
          title="Active Exams"
          value={activeExams}
          color="green"
        />
        <StatCard
          icon={FiBookOpen}
          title="Draft Exams"
          value={draftExams}
          color="yellow"
        />
        <StatCard
          icon={FiBookOpen}
          title="Total Usage"
          value={totalUsage}
          color="purple"
        />
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage message={error} />
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                placeholder="Search exams..."
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <select
            value={filters.difficulty}
            onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {/* Exams List */}
      {filteredExams.length > 0 ? (
        <div className={viewMode === 'grid' 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
          : "space-y-4"
        }>
          {filteredExams.map((exam) => (
            <div
              key={exam.id}
              onClick={() => handleExamSelection(exam)}
              className={selectionMode ? 'cursor-pointer' : ''}
            >
              <ExamCard
                exam={exam}
                onEdit={handleEditExam}
                onDelete={handleDeleteExam}
                onCopy={handleCopyExam}
                onView={handleViewExam}
                onViewUsage={handleViewUsage}
                variant={viewMode === 'list' ? 'compact' : 'default'}
                showActions={!selectionMode}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Exams Found</h3>
          <p className="text-gray-600 mb-6">
            {filters.search || filters.difficulty !== 'all' || filters.status !== 'all'
              ? 'No exams match your current filters.'
              : 'Create your first exam to get started.'}
          </p>
          <button
            onClick={handleCreateExam}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Your First Exam
          </button>
        </div>
      )}

      {/* Exam Form Modal */}
      <ExamForm
        isOpen={showExamForm}
        onClose={() => setShowExamForm(false)}
        onSuccess={handleExamFormSuccess}
        exam={editingExam}
        mode={editingExam ? 'edit' : 'create'}
      />

      {/* Usage Info Modal */}
      {showUsageInfo && selectedExam && usageInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Usage Information: {selectedExam.title}
            </h3>
            
            <div className="space-y-4">
              <div>
                <span className="text-sm font-medium text-gray-700">Total Usage:</span>
                <p className="text-sm text-gray-600">{usageInfo.usage_count} times</p>
              </div>
              
              {usageInfo.active_competitions?.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-gray-700">Active Competitions:</span>
                  <ul className="text-sm text-gray-600 mt-1">
                    {usageInfo.active_competitions.map((comp, index) => (
                      <li key={index}>• {comp.title} ({comp.participants} participants)</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {usageInfo.past_competitions?.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-gray-700">Past Competitions:</span>
                  <ul className="text-sm text-gray-600 mt-1">
                    {usageInfo.past_competitions.slice(0, 3).map((comp, index) => (
                      <li key={index}>• {comp.title} ({comp.participants} participants)</li>
                    ))}
                    {usageInfo.past_competitions.length > 3 && (
                      <li className="text-gray-500">... and {usageInfo.past_competitions.length - 3} more</li>
                    )}
                  </ul>
                </div>
              )}
              
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <p className="text-sm text-blue-800">
                  <strong>Recommendation:</strong> {usageInfo.reason}
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-end space-x-4 mt-6">
              <button
                onClick={() => setShowUsageInfo(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamsTab;
