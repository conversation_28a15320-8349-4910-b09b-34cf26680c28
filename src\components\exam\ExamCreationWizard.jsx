import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import { useNotification } from '../../contexts/NotificationContext';
import { createExamWithAssignment, updateExam } from '../../store/slices/ExamSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';
import { fetchChaptersBySubject } from '../../store/slices/ChapterSlice';
import { fetchTopicsByChapter } from '../../store/slices/TopicSlice';
import { fetchSubtopicsByTopic } from '../../store/slices/SubtopicSlice';
import { fetchClasses } from '../../store/slices/ClassesSlice';
import { aiGenerateQuestions } from '../../store/slices/QuestionSlice';
import { PageContainer, Stack, Card } from '../ui/layout';
import StepIndicator from './StepIndicator';
import ExamDetailsForm from './ExamDetailsForm';
import QuestionForm from './QuestionForm';
import QuestionList from './QuestionList';
import StudentAssignmentSelector from './StudentAssignmentSelector';
import { FiSave, FiArrowRight, FiArrowLeft, FiCheck } from 'react-icons/fi';

const getStepsForUserType = (userType) => {
  const baseSteps = [
    { id: 1, title: 'Exam Details', description: 'Basic exam information' },
    { id: 2, title: 'Questions', description: 'Add exam questions' },
    { id: 3, title: 'Review', description: 'Review and publish' }
  ];

  if (userType === 'teacher') {
    // Teachers need student assignment step
    return [
      { id: 1, title: 'Exam Details', description: 'Basic exam information' },
      { id: 2, title: 'Questions', description: 'Add exam questions' },
      { id: 3, title: 'Assignment', description: 'Assign to students' },
      { id: 4, title: 'Review', description: 'Review and publish' }
    ];
  }

  // Institutes don't need assignment step
  return baseSteps;
};

const ExamCreationWizard = ({
  examId,
  isEditing = false,
  onSuccess = null,
  userType = 'teacher'
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  const { showWarning } = useNotification();

  // Get steps based on user type
  const STEPS = useMemo(() => getStepsForUserType(userType), [userType]);
  const maxSteps = STEPS.length;

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-white",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-gray-50 text-gray-900 border-gray-300",
    label: currentTheme === "dark" ? "text-gray-300" : "text-gray-700",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    button: currentTheme === "dark" ? "bg-blue-600 hover:bg-blue-700" : "bg-blue-600 hover:bg-blue-700"
  }), [currentTheme]);

  const [currentStep, setCurrentStep] = useState(1);
  const [examData, setExamData] = useState({
    title: '',
    description: '',
    total_marks: 0,
    total_duration: 0,
    start_time: '',
    question_ids: [],
    classNumber: '',
    subjectId: ''
  });
  const [questions, setQuestions] = useState([]);
  const [selectedStudents, setSelectedStudents] = useState([]);

  // Hierarchical data state
  const [subjectId, setSubjectId] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [topicId, setTopicId] = useState('');
  const [subtopicId, setSubtopicId] = useState('');

  // AI Generation state
  const [aiNoOfQuestions, setAiNoOfQuestions] = useState(5);
  const [aiDifficultyMode, setAiDifficultyMode] = useState('balanced');
  const [aiNoOfEasy, setAiNoOfEasy] = useState(2);
  const [aiNoOfMedium, setAiNoOfMedium] = useState(2);
  const [aiNoOfHard, setAiNoOfHard] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Manual question form state
  const [questionType, setQuestionType] = useState('MCQS');
  const [questionForm, setQuestionForm] = useState({
    text: '',
    options: ['', '', '', ''],
    correct_answer: '',
    marks: 1
  });

  // Redux selectors
  const { classes = [], loading: classesLoading } = useSelector(state => state.classes);
  const { subjects = [], loading: subjectsLoading } = useSelector(state => state.subjects);
  const { chaptersBySubject = [], loading: chaptersLoading } = useSelector(state => state.chapters);
  const { topicsByChapter = [], loading: topicsLoading } = useSelector(state => state.topics);
  const { subtopicsByTopic = [], loading: subtopicsLoading } = useSelector(state => state.subtopics);
  const { aiGeneratedQuestions, loading: aiLoading, error: aiError } = useSelector(state => state.questions);

  const handleExamDataChange = useCallback((eventOrData) => {
    if (eventOrData.target) {
      // Handle form event
      const { name, value, type, checked } = eventOrData.target;
      const fieldValue = type === 'checkbox' ? checked : value;
      setExamData(prev => ({ ...prev, [name]: fieldValue }));
    } else {
      // Handle data object
      setExamData(prev => ({ ...prev, ...eventOrData }));
    }
  }, []);

  const handleQuestionsChange = useCallback((newQuestions) => {
    setQuestions(newQuestions);
    const totalMarks = newQuestions.reduce((sum, q) => sum + (q.marks || 0), 0);
    setExamData(prev => ({ ...prev, total_marks: totalMarks }));
  }, []);

  // Handle class number change
  const handleClassNumberChange = useCallback((e) => {
    const classNumber = e.target.value;
    setExamData(prev => ({ ...prev, classNumber }));
  }, []);

  // Handle subject change
  const handleSubjectChange = useCallback((e) => {
    const subjectId = e.target.value;
    setExamData(prev => ({ ...prev, subjectId }));
    setSubjectId(subjectId); // Also update the hierarchical state for questions
  }, []);

  // AI Generation handler
  const handleAIGenerate = useCallback(async () => {
    if (!subjectId || !chapterId) {
      console.log('Please select a subject and chapter first');
      return;
    }

    setIsSubmitting(true);
    try {
      // Find the selected subject and chapter names
      const selectedSubject = subjects.find(s => s.id === subjectId);
      const selectedChapter = chaptersBySubject.find(c => c.id === chapterId);
      const selectedTopic = topicsByChapter.find(t => t.id === topicId);
      const selectedSubtopic = subtopicsByTopic.find(st => st.id === subtopicId);

      // Prepare difficulty payload
      let difficultyPayload = {};
      if (aiDifficultyMode === "custom") {
        difficultyPayload = {
          no_of_easy: aiNoOfEasy,
          no_of_medium: aiNoOfMedium,
          no_of_hard: aiNoOfHard
        };
      }

      const payload = {
        class: examData.classNumber || "10", // Use class from exam details
        subject: selectedSubject?.name || "",
        chapter: selectedChapter?.name || "",
        no_of_questions: aiNoOfQuestions,
        topic: selectedTopic?.name || "",
        subtopic: selectedSubtopic?.name || "",
        ...difficultyPayload
      };

      console.log('AI Generation Payload:', payload);

      const result = await dispatch(aiGenerateQuestions(payload)).unwrap();

      if (result && result.questions && result.questions.length > 0) {
        // Add generated questions to the exam
        const newQuestions = result.questions.map((question, index) => ({
          ...question,
          id: Date.now() + index,
          class_number: examData.classNumber,
          subject_id: subjectId,
          chapter_id: chapterId,
          topic_id: topicId || null,
          subtopic_id: subtopicId || null,
        }));

        setQuestions(prev => [...prev, ...newQuestions]);
        console.log(`Generated ${result.questions.length} questions successfully!`);
      } else {
        console.log('No questions were generated. Please try again.');
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      console.error(error || 'Failed to generate questions. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, examData.classNumber, subjectId, chapterId, topicId, subtopicId, subjects, chaptersBySubject, topicsByChapter, subtopicsByTopic, aiNoOfQuestions, aiDifficultyMode, aiNoOfEasy, aiNoOfMedium, aiNoOfHard]);

  // Manual question handlers
  const handleQuestionChange = useCallback((e) => {
    const { name, value } = e.target;
    setQuestionForm(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleOptionChange = useCallback((index, value) => {
    setQuestionForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => i === index ? value : opt)
    }));
  }, []);

  const handleAddQuestion = useCallback((e) => {
    e.preventDefault();

    if (!questionForm.text.trim()) {
      console.log('Please enter question text');
      return;
    }

    if (questionType === 'MCQS') {
      const filledOptions = questionForm.options.filter(opt => opt.trim() !== '');
      if (filledOptions.length < 2) {
        console.log('Please provide at least 2 options for multiple choice questions');
        return;
      }
      if (!questionForm.correct_answer.trim()) {
        console.log('Please specify the correct answer');
        return;
      }
    }

    const newQuestion = {
      id: Date.now(),
      text: questionForm.text,
      type: questionType,
      marks: parseInt(questionForm.marks) || 1,
      class_number: examData.classNumber,
      subject_id: subjectId,
      chapter_id: chapterId,
      topic_id: topicId || null,
      subtopic_id: subtopicId || null,
      ...(questionType === 'MCQS' && {
        options: questionForm.options.filter(opt => opt.trim() !== ''),
        correct_answer: questionForm.correct_answer
      })
    };

    setQuestions(prev => [...prev, newQuestion]);

    // Reset form
    setQuestionForm({
      text: '',
      options: ['', '', '', ''],
      correct_answer: '',
      marks: 1
    });

    console.log('Question added successfully!');
  }, [questionForm, questionType, examData.classNumber, subjectId, chapterId, topicId, subtopicId]);

  // Data fetching effects
  useEffect(() => {
    // Fetch classes and subjects on mount
    dispatch(fetchClasses({ skip: 0, limit: 100 }));
    dispatch(fetchSubjects({ skip: 0, limit: 100 }));
  }, [dispatch]);

  useEffect(() => {
    // Fetch chapters when subject changes
    if (subjectId) {
      dispatch(fetchChaptersBySubject({ subjectId, skip: 0, limit: 100 }));
      setChapterId(''); // Reset chapter selection
      setTopicId(''); // Reset topic selection
      setSubtopicId(''); // Reset subtopic selection
    }
  }, [dispatch, subjectId]);

  useEffect(() => {
    // Fetch topics when chapter changes
    if (chapterId) {
      dispatch(fetchTopicsByChapter({ chapterId, skip: 0, limit: 100 }));
      setTopicId(''); // Reset topic selection
      setSubtopicId(''); // Reset subtopic selection
    }
  }, [dispatch, chapterId]);

  useEffect(() => {
    // Fetch subtopics when topic changes
    if (topicId) {
      dispatch(fetchSubtopicsByTopic({ topicId, skip: 0, limit: 100 }));
      setSubtopicId(''); // Reset subtopic selection
    }
  }, [dispatch, topicId]);

  // Auto-calculate total marks and duration when questions change
  useEffect(() => {
    const totalMarks = questions.reduce((sum, question) => sum + (question.marks || 1), 0);

    // For institutes, use manual duration if set, otherwise calculate estimated duration (2 minutes per question)
    // For teachers, keep their manually set duration
    let calculatedDuration = examData.total_duration;
    if (userType === 'institute') {
      // If no manual duration is set (empty string or 0), auto-calculate
      if (!examData.total_duration || examData.total_duration === '' || examData.total_duration === 0) {
        calculatedDuration = questions.length * 2;
      }
    }

    setExamData(prev => ({
      ...prev,
      total_marks: totalMarks,
      ...(userType === 'institute' && { total_duration: calculatedDuration })
    }));
  }, [questions, userType, examData.total_duration]);

  // Validation logic
  const examDetailsValid = useMemo(() => {
    if (userType === 'institute') {
      // For institutes, title, description, class, and subject are required
      return examData.title && examData.title.trim() !== '' &&
             examData.description && examData.description.trim() !== '' &&
             examData.classNumber && examData.classNumber.trim() !== '' &&
             examData.subjectId && examData.subjectId.trim() !== '';
    } else {
      // For teachers, additional fields are required
      return examData.title && examData.title.trim() !== '' &&
             examData.description && examData.description.trim() !== '' &&
             examData.total_duration && examData.total_duration > 0 &&
             examData.start_time && examData.start_time.trim() !== '';
    }
  }, [examData, userType]);

  const handleNext = () => {
    // Validate current step before proceeding
    if (currentStep === 1) {
      // Validate exam details before moving to questions
      if (!examDetailsValid) {
        showWarning('Please fill in all required exam details before proceeding to questions.');
        return;
      }
    }

    if (currentStep < maxSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSave = async () => {
    try {
      const examPayload = {
        ...examData,
        question_ids: questions.map(q => q.id),
        assigned_students: selectedStudents
      };

      if (isEditing) {
        await dispatch(updateExam({ examId, examData: examPayload })).unwrap();
      } else {
        await dispatch(createExamWithAssignment(examPayload)).unwrap();
      }

      // Use custom success handler or default navigation
      if (onSuccess) {
        onSuccess();
      } else {
        // Default navigation based on user type
        const defaultPath = userType === 'institute' ? '/institute/exams' : '/teacher/exams';
        navigate(defaultPath);
      }
    } catch (error) {
      console.error('Failed to save exam:', error);
    }
  };

  const renderStepContent = () => {
    const currentStepData = STEPS[currentStep - 1];

    switch (currentStepData.title) {
      case 'Exam Details':
        return (
          <ExamDetailsForm
            exam={examData}
            onExamChange={handleExamDataChange}
            themeClasses={themeClasses}
            classrooms={[]}
            subjects={subjects}
            classes={classes}
            classId=""
            subjectId={examData.subjectId}
            classNumber={examData.classNumber}
            assignmentType="individual"
            onClassChange={() => {}}
            onSubjectChange={handleSubjectChange}
            onClassNumberChange={handleClassNumberChange}
            examDetailsValid={examDetailsValid}
            userType={userType}
          />
        );
      case 'Questions':
        return (
          <Stack gap="lg">
            <QuestionForm
              onQuestionAdd={handleAddQuestion}
              themeClasses={themeClasses}
              userType={userType}
              questionType={questionType}
              setQuestionType={setQuestionType}
              descType="text"
              setDescType={() => {}}
              questionForm={questionForm}
              onQuestionChange={handleQuestionChange}
              onOptionChange={handleOptionChange}
              onAIGenerate={handleAIGenerate}
              subjects={subjects}
              subjectId={subjectId}
              setSubjectId={setSubjectId}
              chaptersBySubject={chaptersBySubject}
              topicsByChapter={topicsByChapter}
              subtopicsByTopic={subtopicsByTopic}
              chapterId={chapterId}
              topicId={topicId}
              subtopicId={subtopicId}
              setChapterId={setChapterId}
              setTopicId={setTopicId}
              setSubtopicId={setSubtopicId}
              chaptersLoading={chaptersLoading}
              topicsLoading={topicsLoading}
              subtopicsLoading={subtopicsLoading}
              isSubmitting={isSubmitting}
              aiNoOfQuestions={aiNoOfQuestions}
              setAiNoOfQuestions={setAiNoOfQuestions}
              aiDifficultyMode={aiDifficultyMode}
              setAiDifficultyMode={setAiDifficultyMode}
              aiNoOfEasy={aiNoOfEasy}
              setAiNoOfEasy={setAiNoOfEasy}
              aiNoOfMedium={aiNoOfMedium}
              setAiNoOfMedium={setAiNoOfMedium}
              aiNoOfHard={aiNoOfHard}
              setAiNoOfHard={setAiNoOfHard}
              gradeClasses={classes}
              classNumber=""
              classNumberId=""
              onClassNumberChange={() => {}}
            />
            <QuestionList
              questions={questions}
              onQuestionsChange={handleQuestionsChange}
              themeClasses={themeClasses}
            />
          </Stack>
        );
      case 'Assignment':
        return (
          <StudentAssignmentSelector
            selectedStudents={selectedStudents}
            onSelectionChange={setSelectedStudents}
          />
        );
      case 'Review':
        return (
          <Card>
            <h3 className="text-lg font-semibold mb-4">Review Exam</h3>
            <Stack gap="md">
              <div>
                <strong>Title:</strong> {examData.title}
              </div>
              <div>
                <strong>Duration:</strong> {examData.total_duration} minutes
              </div>
              <div>
                <strong>Total Marks:</strong> {examData.total_marks}
              </div>
              <div>
                <strong>Questions:</strong> {questions.length}
              </div>
              {userType === 'teacher' && (
                <div>
                  <strong>Students:</strong> {selectedStudents.length}
                </div>
              )}
              {userType === 'institute' && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-blue-800 text-sm">
                    <strong>Note:</strong> This exam will be available to all students in your institute.
                  </p>
                </div>
              )}
            </Stack>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <PageContainer>
      <Stack gap="lg">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-8 border border-blue-100 dark:border-gray-700">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
              <FiCheck className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                {isEditing
                  ? (userType === 'institute' ? 'Edit Question Bank' : 'Edit Exam')
                  : (userType === 'institute' ? 'Create Question Bank' : 'Create New Exam')
                }
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2 text-lg">
                {userType === 'institute'
                  ? 'Build a reusable question bank for competitions'
                  : 'Follow the steps to create your exam'
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <StepIndicator
            steps={STEPS}
            currentStep={currentStep}
            examDetailsValid={examDetailsValid}
            questionsCount={questions.length}
          />
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 min-h-96">
          {renderStepContent()}
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex justify-between items-center">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <FiArrowLeft className="mr-2 w-4 h-4" />
              Previous
            </button>

            <div className="flex space-x-4">
              <button
                onClick={handleSave}
                className="flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FiSave className="mr-2 w-4 h-4" />
                Save Draft
              </button>

              {currentStep < maxSteps ? (
                <div className="relative">
                  <button
                    onClick={handleNext}
                    disabled={currentStep === 1 && !examDetailsValid}
                    className={`flex items-center px-6 py-3 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                      currentStep === 1 && !examDetailsValid
                        ? 'bg-gray-400 cursor-not-allowed text-gray-200'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                    title={currentStep === 1 && !examDetailsValid ? 'Please complete all required exam details first' : ''}
                  >
                    Next
                    <FiArrowRight className="ml-2 w-4 h-4" />
                  </button>
                  {currentStep === 1 && !examDetailsValid && (
                    <div className="absolute -top-8 right-0 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      Complete exam details first
                    </div>
                  )}
                </div>
              ) : (
                <button
                  onClick={handleSave}
                  className="flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <FiCheck className="mr-2 w-4 h-4" />
                  {userType === 'institute' ? 'Create Question Bank' : 'Publish Exam'}
                </button>
              )}
            </div>
          </div>
        </div>
      </Stack>
    </PageContainer>
  );
};

export default ExamCreationWizard;
