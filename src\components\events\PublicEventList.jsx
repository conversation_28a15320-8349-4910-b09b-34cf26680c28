import React from 'react';
import PublicEventCard from './PublicEventCard';
import { LoadingSpinner } from '../ui';
import { Card } from '../ui/layout';
import { FiCalendar } from 'react-icons/fi';

const PublicEventList = ({
  events = [],
  loading = false,
  title = "Events",
  onViewDetails,
  onRegister,
  viewMode = 'grid'
}) => {
  if (loading) {
    return (
      <Card>
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
        <div className="p-8">
          <div className="flex flex-col justify-center items-center h-64">
            <LoadingSpinner size="lg" />
            <p className="text-sm text-gray-600 mt-4">Loading events...</p>
          </div>
        </div>
      </Card>
    );
  }

  if (events.length === 0) {
    return (
      <Card>
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title} (0)</h3>
        </div>
        <div className="p-12 text-center">
          <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <FiCalendar className="h-8 w-8 text-gray-500" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No events found</h3>
          <p className="text-sm text-gray-600 max-w-md mx-auto">
            No events match your current criteria. Try adjusting your search or filters.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          {title}
          <span className="ml-3 px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm font-medium">
            {events.length}
          </span>
        </h3>
      </div>
      <div className="p-6">
        <div className={
          viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
            : "space-y-4"
        }>
          {events.map((event) => (
            <PublicEventCard
              key={event.id}
              event={event}
              onViewDetails={onViewDetails}
              onRegister={onRegister}
              viewMode={viewMode}
            />
          ))}
        </div>
      </div>
    </Card>
  );
};

export default PublicEventList;
