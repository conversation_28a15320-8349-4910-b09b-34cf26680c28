/**
 * Event Booking Service
 * 
 * Handles event booking and PayFast payment integration
 */

import axios from 'axios';
import { getAuthToken } from '../utils/helpers/authHelpers';
import API_BASE_URL from '../utils/api/API_URL';

// Use the correct backend API endpoints
const API_BASE = `${API_BASE_URL}/api/events`;

export class EventBookingService {
  
  /**
   * Book an event with ticket selection
   * @param {Object} bookingData - Booking details
   * @returns {Promise} Booking response
   */
  static async bookEvent(bookingData) {
    try {
      // Use the correct registration endpoint
      const response = await axios.post(`${API_BASE}/registrations/`, bookingData, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to book event');
    }
  }

  /**
   * Get registration status
   * @param {string} registrationId - Registration ID
   * @returns {Promise} Registration status
   */
  static async getRegistrationStatus(registrationId) {
    try {
      // Note: This endpoint may not be implemented yet in backend
      // Will fall back to mock data if needed
      const response = await axios.get(`${API_BASE}/registrations/${registrationId}/status`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.warn('Events API not available, may need to use mock data:', error.message);
      throw new Error(error.response?.data?.message || 'Failed to get registration status');
    }
  }

  /**
   * Get user's event dashboard
   * @returns {Promise} User event dashboard data
   */
  static async getUserEventDashboard() {
    try {
      // Note: This endpoint may not be implemented yet
      const response = await axios.get(`${API_BASE}/my/events/dashboard`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.warn('Event dashboard API not available, using mock data:', error.message);
      // Return mock data if endpoint doesn't exist yet
      return {
        total_registrations: 3,
        confirmed_registrations: 2,
        upcoming_events: 2,
        total_tickets: 3
      };
    }
  }

  /**
   * Get user's event registrations
   * @param {Object} params - Query parameters
   * @returns {Promise} User registrations
   */
  static async getUserRegistrations(params = {}) {
    try {
      const queryParams = new URLSearchParams(params).toString();
      // Use the correct working backend endpoint
      const url = queryParams ?
        `${API_BASE}/registrations/my-registrations?${queryParams}` :
        `${API_BASE}/registrations/my-registrations`;

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('EventBookingService: Error fetching registrations:', error);
      throw new Error(error.response?.data?.message || 'Failed to get user registrations');
    }
  }

  /**
   * Get specific registration details
   * @param {string} registrationId - Registration ID
   * @returns {Promise} Registration details
   */
  static async getRegistrationDetails(registrationId) {
    try {
      // Use correct backend endpoint
      const response = await axios.get(`${API_BASE}/registrations/${registrationId}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      console.warn('Registration details API not available:', error.message);
      throw new Error(error.response?.data?.message || 'Failed to get registration details');
    }
  }

  /**
   * Redirect to PayFast payment
   * @param {Object} paymentData - PayFast payment form data
   */
  static redirectToPayFast(paymentData) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = paymentData.payment_url;

    // Add all payment fields
    Object.keys(paymentData.payment_data).forEach(key => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = key;
      input.value = paymentData.payment_data[key];
      form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
  }

  /**
   * Poll registration status until completion
   * @param {string} registrationId - Registration ID
   * @param {number} maxAttempts - Maximum polling attempts
   * @param {number} interval - Polling interval in ms
   * @returns {Promise} Final registration status
   */
  static async pollRegistrationStatus(registrationId, maxAttempts = 30, interval = 2000) {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const status = await this.getRegistrationStatus(registrationId);
          
          // Check if registration is complete
          if (status.status === 'confirmed' || status.payment_status === 'completed') {
            resolve(status);
            return;
          }
          
          // Check if registration failed
          if (status.status === 'cancelled' || status.payment_status === 'failed') {
            reject(new Error('Registration failed or was cancelled'));
            return;
          }
          
          // Continue polling if not complete and within max attempts
          if (attempts < maxAttempts) {
            setTimeout(poll, interval);
          } else {
            reject(new Error('Registration status polling timeout'));
          }
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }

  /**
   * Format currency amount
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted currency string
   */
  static formatCurrency(amount, currency = 'PKR') {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Validate booking data
   * @param {Object} bookingData - Booking data to validate
   * @returns {Object} Validation result
   */
  static validateBookingData(bookingData) {
    const errors = [];

    if (!bookingData.event_id) {
      errors.push('Event ID is required');
    }

    if (!bookingData.ticket_id) {
      errors.push('Ticket selection is required');
    }

    if (!bookingData.quantity || bookingData.quantity < 1) {
      errors.push('Valid quantity is required');
    }

    if (!bookingData.attendee_info) {
      errors.push('Attendee information is required');
    } else {
      if (!bookingData.attendee_info.name?.trim()) {
        errors.push('Attendee name is required');
      }
      if (!bookingData.attendee_info.email?.trim()) {
        errors.push('Attendee email is required');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate return URLs for PayFast
   * @param {string} baseUrl - Base URL of the application
   * @param {string} registrationId - Registration ID
   * @returns {Object} Return URLs
   */
  static generateReturnUrls(baseUrl, registrationId) {
    return {
      return_url: `${baseUrl}/payment/success?registration_id=${registrationId}`,
      cancel_url: `${baseUrl}/payment/cancel?registration_id=${registrationId}`,
      notify_url: `${baseUrl}/api/payments/payfast/notify`
    };
  }

  /**
   * Handle payment return from PayFast
   * @param {URLSearchParams} searchParams - URL search parameters
   * @returns {Object} Payment return data
   */
  static handlePaymentReturn(searchParams) {
    return {
      registrationId: searchParams.get('registration_id'),
      paymentId: searchParams.get('payment_id'),
      status: searchParams.get('status'),
      paymentStatus: searchParams.get('payment_status')
    };
  }

  /**
   * Get payment status info for display
   * @param {string} status - Payment status
   * @returns {Object} Status display info
   */
  static getPaymentStatusInfo(status) {
    const statusMap = {
      'pending': {
        color: 'yellow',
        icon: 'clock',
        message: 'Payment is being processed'
      },
      'completed': {
        color: 'green',
        icon: 'check',
        message: 'Payment completed successfully'
      },
      'failed': {
        color: 'red',
        icon: 'x',
        message: 'Payment failed'
      },
      'cancelled': {
        color: 'gray',
        icon: 'x',
        message: 'Payment was cancelled'
      }
    };

    return statusMap[status] || {
      color: 'gray',
      icon: 'info',
      message: 'Unknown payment status'
    };
  }
}

// Create and export service instance
const eventBookingService = new EventBookingService();
export default eventBookingService;

// Export individual methods for convenience
export const {
  bookEvent,
  getRegistrationStatus,
  getUserEventDashboard,
  getUserRegistrations,
  getRegistrationDetails,
  redirectToPayFast,
  pollRegistrationStatus,
  formatCurrency,
  validateBookingData,
  generateReturnUrls,
  handlePaymentReturn,
  getPaymentStatusInfo
} = EventBookingService;
