import React, { useState, useEffect } from 'react';
import {
  FiCal<PERSON>tor,
  FiEye,
  FiSend,
  FiDownload,
  FiUsers,
  FiAward,
  FiCheckCircle,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';
import {
  calculateCompetitionResults,
  publishCompetitionResults,
  getCompetitionResults
} from '../../services/competitionService';
import { LoadingSpinner } from '../ui';

const ResultsCalculationPanel = ({ competitionId, onResultsUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [calculating, setCalculating] = useState(false);
  const [publishing, setPublishing] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState(null);
  const [calculationStatus, setCalculationStatus] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    loadResults();
  }, [competitionId]);

  const loadResults = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getCompetitionResults(competitionId);
      setResults(data);
      setCalculationStatus(data.status);
    } catch (err) {
      if (err.response?.status !== 404) {
        console.error('Error loading results:', err);
        setError('Failed to load results');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCalculateResults = async () => {
    try {
      setCalculating(true);
      setError(null);
      
      const response = await calculateCompetitionResults(competitionId, {
        auto_evaluate: true,
        include_partial_scores: true
      });
      
      setCalculationStatus('calculated');
      await loadResults();
      onResultsUpdated && onResultsUpdated();
      
    } catch (err) {
      console.error('Error calculating results:', err);
      setError(err.response?.data?.message || 'Failed to calculate results');
    } finally {
      setCalculating(false);
    }
  };

  const handlePublishResults = async () => {
    try {
      setPublishing(true);
      setError(null);
      
      await publishCompetitionResults(competitionId, {
        notify_participants: true,
        generate_certificates: true
      });
      
      setCalculationStatus('published');
      await loadResults();
      onResultsUpdated && onResultsUpdated();
      
    } catch (err) {
      console.error('Error publishing results:', err);
      setError(err.response?.data?.message || 'Failed to publish results');
    } finally {
      setPublishing(false);
    }
  };

  const exportResults = () => {
    if (!results?.rankings) return;
    
    const csvContent = [
      ['Rank', 'Name', 'Score', 'Percentage', 'Time Taken', 'Status'].join(','),
      ...results.rankings.map(entry => [
        entry.rank,
        entry.participant_name,
        entry.total_score,
        entry.percentage,
        entry.time_taken_minutes || 0,
        entry.status || 'completed'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `competition-results-${competitionId}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'calculated':
        return <FiCheckCircle className="h-5 w-5 text-green-600" />;
      case 'published':
        return <FiSend className="h-5 w-5 text-blue-600" />;
      case 'calculating':
        return <FiClock className="h-5 w-5 text-yellow-600" />;
      default:
        return <FiAlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'calculated':
        return 'Results calculated - ready to publish';
      case 'published':
        return 'Results published and visible to participants';
      case 'calculating':
        return 'Calculation in progress...';
      default:
        return 'Results not calculated yet';
    }
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600`} />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-semibold text-gray-900">{value}</h3>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Results Management</h2>
          <p className="text-gray-600 mt-1">Calculate and publish competition results</p>
        </div>
        
        {results && (
          <button
            onClick={exportResults}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FiDownload className="h-4 w-4 mr-2" />
            Export Results
          </button>
        )}
      </div>

      {/* Status Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-4">
          {getStatusIcon(calculationStatus)}
          <div>
            <h3 className="text-lg font-medium text-gray-900">Calculation Status</h3>
            <p className="text-sm text-gray-600">{getStatusText(calculationStatus)}</p>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Statistics */}
      {results && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            icon={FiUsers}
            title="Total Participants"
            value={results.total_participants || 0}
            subtitle="Submitted attempts"
            color="blue"
          />
          <StatCard
            icon={FiAward}
            title="Average Score"
            value={`${results.average_score || 0}%`}
            subtitle="Overall performance"
            color="green"
          />
          <StatCard
            icon={FiCheckCircle}
            title="Completion Rate"
            value={`${results.completion_rate || 0}%`}
            subtitle="Finished vs started"
            color="purple"
          />
          <StatCard
            icon={FiClock}
            title="Average Time"
            value={`${results.average_time || 0}m`}
            subtitle="Time taken"
            color="yellow"
          />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center space-x-4">
        {calculationStatus !== 'calculated' && calculationStatus !== 'published' && (
          <button
            onClick={handleCalculateResults}
            disabled={calculating}
            className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {calculating && <LoadingSpinner size="sm" className="mr-2" />}
            <FiCalculator className="h-4 w-4 mr-2" />
            {calculating ? 'Calculating...' : 'Calculate Results'}
          </button>
        )}

        {calculationStatus === 'calculated' && (
          <>
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <FiEye className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide Preview' : 'Preview Results'}
            </button>
            
            <button
              onClick={handlePublishResults}
              disabled={publishing}
              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {publishing && <LoadingSpinner size="sm" className="mr-2" />}
              <FiSend className="h-4 w-4 mr-2" />
              {publishing ? 'Publishing...' : 'Publish Results'}
            </button>
          </>
        )}

        {calculationStatus === 'published' && (
          <div className="flex items-center space-x-2 text-green-600">
            <FiCheckCircle className="h-5 w-5" />
            <span className="font-medium">Results Published Successfully</span>
          </div>
        )}
      </div>

      {/* Results Preview */}
      {showPreview && results?.rankings && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Results Preview</h3>
            <p className="text-sm text-gray-600 mt-1">Top 10 participants</p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Participant
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Percentage
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time Taken
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {results.rankings.slice(0, 10).map((entry, index) => (
                  <tr key={entry.participant_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full ${
                        entry.rank === 1 ? 'bg-yellow-100 text-yellow-800' :
                        entry.rank === 2 ? 'bg-gray-100 text-gray-800' :
                        entry.rank === 3 ? 'bg-amber-100 text-amber-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        <span className="text-sm font-bold">#{entry.rank}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {entry.participant_name}
                      </div>
                      <div className="text-sm text-gray-500">{entry.participant_email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className="text-sm font-medium text-gray-900">
                        {entry.total_score}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className={`text-sm font-medium ${
                        entry.percentage >= 90 ? 'text-green-600' :
                        entry.percentage >= 80 ? 'text-blue-600' :
                        entry.percentage >= 70 ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {entry.percentage}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className="text-sm text-gray-900">
                        {entry.time_taken_minutes || 0}m
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Calculation Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-sm font-medium text-blue-900 mb-3">Calculation Process</h3>
        <ul className="text-sm text-blue-800 space-y-2">
          <li>• All mentor evaluations are aggregated and averaged</li>
          <li>• Partial scores are included for incomplete evaluations</li>
          <li>• Rankings are determined by total score, then by time taken</li>
          <li>• Results can be recalculated if needed before publishing</li>
          <li>• Once published, participants will be notified automatically</li>
        </ul>
      </div>
    </div>
  );
};

export default ResultsCalculationPanel;
