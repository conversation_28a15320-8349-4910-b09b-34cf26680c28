# 🎯 Complete Backend Requirements for Event System

## Overview
This document outlines the complete backend implementation requirements for the redesigned event registration and ticket purchasing system. The frontend has been completely redesigned and is ready for backend integration.

## 🚨 Current Status
- ✅ **Frontend Implementation**: Complete and production-ready
- ❌ **Backend Implementation**: Not started
- 🔄 **Mock Data**: Frontend uses comprehensive mock implementations
- 📋 **API Design**: Fully documented and tested

## 📋 Priority Implementation Order

### Phase 1: Core Event Management (Week 1-2)
**Priority: CRITICAL**

#### 1.1 Database Schema
```sql
-- Events table
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    category VARCHAR(50) NOT NULL CHECK (category IN ('WORKSHOP', 'SEMINAR', 'CONFERENCE', 'COMPETITION', 'NETWORKING')),
    status VARCHAR(50) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'PUBLISHED', 'CANCELLED', 'COMPLETED')),
    start_datetime TIMESTAMP NOT NULL,
    end_datetime TIMESTAMP NOT NULL,
    location VARCHAR(255),
    banner_image_url VARCHAR(500),
    gallery_images JSON,
    is_featured BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    max_attendees INTEGER,
    min_attendees INTEGER DEFAULT 1,
    registration_start TIMESTAMP,
    registration_end TIMESTAMP,
    requirements TEXT,
    agenda JSON,
    organizer_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Tickets table
CREATE TABLE tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'ZAR',
    available_quantity INTEGER NOT NULL,
    max_per_user INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'SOLD_OUT', 'INACTIVE')),
    features JSON,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Registrations table
CREATE TABLE registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    registration_number VARCHAR(50) UNIQUE NOT NULL,
    event_id UUID NOT NULL REFERENCES events(id),
    user_id UUID NOT NULL REFERENCES users(id),
    ticket_id UUID NOT NULL REFERENCES tickets(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    status VARCHAR(50) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'PENDING_PAYMENT', 'CANCELLED')),
    attendee_info JSON NOT NULL,
    payment_reference VARCHAR(255),
    payment_status VARCHAR(50) DEFAULT 'PENDING' CHECK (payment_status IN ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')),
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    qr_code TEXT,
    check_in_code VARCHAR(20),
    registered_at TIMESTAMP DEFAULT NOW(),
    confirmed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(event_id, user_id, ticket_id)
);

-- Indexes for performance
CREATE INDEX idx_events_status_category ON events(status, category);
CREATE INDEX idx_events_start_datetime ON events(start_datetime);
CREATE INDEX idx_events_organizer ON events(organizer_id);
CREATE INDEX idx_tickets_event ON tickets(event_id);
CREATE INDEX idx_registrations_user ON registrations(user_id);
CREATE INDEX idx_registrations_event ON registrations(event_id);
CREATE INDEX idx_registrations_status ON registrations(status);
```

#### 1.2 Core API Endpoints
```python
# Event Management Endpoints
GET    /api/events                    # List public events with filtering
GET    /api/events/{event_id}         # Get event details
POST   /api/events                    # Create event (organizers/admins)
PUT    /api/events/{event_id}         # Update event (owners/admins)
DELETE /api/events/{event_id}         # Delete event (owners/admins)

# Event Statistics
GET    /api/events/{event_id}/stats   # Get event statistics (owners/admins)
```

### Phase 2: Registration System (Week 3)
**Priority: HIGH**

#### 2.1 Registration Endpoints
```python
# Free Event Registration
POST   /api/events/{event_id}/register
# Request: { ticket_id, quantity, attendee_info }
# Response: { registration_id, status: "CONFIRMED", qr_code, ... }

# Paid Event Registration  
POST   /api/events/{event_id}/purchase
# Request: { ticket_id, quantity, attendee_info }
# Response: { registration_id, status: "PENDING_PAYMENT", payment_url, ... }

# Registration Management
GET    /api/events/my-registrations   # User's registrations
GET    /api/events/registrations/{registration_id}  # Registration details
DELETE /api/events/registrations/{registration_id}  # Cancel registration
```

#### 2.2 Business Logic Implementation
```python
class EventRegistrationService:
    def register_for_event(self, event_id, user_id, registration_data):
        # 1. Validate event exists and is active
        # 2. Check registration is open
        # 3. Validate ticket availability
        # 4. Check user hasn't already registered
        # 5. Create registration record
        # 6. Generate QR code and check-in code
        # 7. Send confirmation email
        # 8. Return registration details
        
    def purchase_ticket(self, event_id, user_id, purchase_data):
        # 1. Validate event and ticket
        # 2. Create pending registration
        # 3. Generate PayFast payment URL
        # 4. Set expiration time (1 hour)
        # 5. Return payment details
```

### Phase 3: PayFast Integration (Week 4)
**Priority: HIGH**

#### 3.1 PayFast Configuration
```python
# settings.py
PAYFAST_MERCHANT_ID = "10000100"  # Sandbox
PAYFAST_MERCHANT_KEY = "46f0cd694581a"  # Sandbox
PAYFAST_PASSPHRASE = "your_secure_passphrase"
PAYFAST_SANDBOX = True  # Set to False for production

PAYFAST_SANDBOX_URL = "https://sandbox.payfast.co.za/eng/process"
PAYFAST_PRODUCTION_URL = "https://www.payfast.co.za/eng/process"
```

#### 3.2 PayFast Webhook
```python
POST   /api/events/payments/payfast/webhook
# Handles PayFast payment notifications
# Updates registration status based on payment result
# Sends confirmation emails
# Generates QR codes for successful payments
```

#### 3.3 Payment Processing
```python
class PayFastService:
    def create_payment_url(self, registration):
        payment_data = {
            "merchant_id": settings.PAYFAST_MERCHANT_ID,
            "merchant_key": settings.PAYFAST_MERCHANT_KEY,
            "return_url": f"{settings.FRONTEND_URL}/payment/success?registration_id={registration.id}",
            "cancel_url": f"{settings.FRONTEND_URL}/payment/cancel?registration_id={registration.id}",
            "notify_url": f"{settings.BACKEND_URL}/api/events/payments/payfast/webhook",
            "amount": f"{registration.total_amount:.2f}",
            "item_name": f"{registration.event.title} - {registration.ticket.name}",
            "custom_str1": str(registration.id),
            "custom_str2": str(registration.user_id)
        }
        
        # Generate signature
        signature = self.generate_signature(payment_data)
        payment_data["signature"] = signature
        
        return f"{self.get_payfast_url()}?{urlencode(payment_data)}"
    
    def handle_webhook(self, webhook_data):
        # 1. Validate signature
        # 2. Extract registration info
        # 3. Update registration status
        # 4. Send confirmation email
        # 5. Generate QR code
```

### Phase 4: Advanced Features (Week 5-6)
**Priority: MEDIUM**

#### 4.1 Event Analytics
```python
GET    /api/events/{event_id}/analytics
# Returns detailed analytics for event organizers
# Registration trends, revenue, attendee demographics
```

#### 4.2 Bulk Operations
```python
POST   /api/events/{event_id}/registrations/bulk-import
POST   /api/events/{event_id}/registrations/bulk-export
```

#### 4.3 Email Notifications
```python
# Automated emails for:
# - Registration confirmation
# - Payment confirmation  
# - Event reminders
# - Cancellation confirmations
```

## 🔧 Implementation Guidelines

### Authentication & Authorization
```python
# Required permissions
PERMISSIONS = {
    "student": ["register", "view_own_registrations"],
    "teacher": ["register", "view_own_registrations"],
    "institute": ["create_events", "manage_own_events", "view_analytics"],
    "sponsor": ["create_events", "manage_own_events", "view_analytics"],
    "admin": ["all_permissions"]
}
```

### Error Handling
```python
# Standard error responses
{
    "error": {
        "code": "EVENT_NOT_FOUND",
        "message": "Event not found or no longer available",
        "details": {}
    }
}

# Common error codes:
# - EVENT_NOT_FOUND
# - REGISTRATION_CLOSED
# - TICKET_SOLD_OUT
# - ALREADY_REGISTERED
# - PAYMENT_REQUIRED
# - INVALID_PAYMENT
```

### Data Validation
```python
# Event validation
class EventCreateSchema:
    title: str = Field(min_length=1, max_length=255)
    start_datetime: datetime = Field(gt=datetime.now())
    end_datetime: datetime = Field(gt=start_datetime)
    max_attendees: int = Field(gt=0)
    
# Registration validation  
class RegistrationSchema:
    ticket_id: UUID
    quantity: int = Field(ge=1, le=10)
    attendee_info: AttendeeInfo
```

## 🧪 Testing Requirements

### Unit Tests
```python
# Test coverage required for:
# - Event CRUD operations
# - Registration logic
# - Payment processing
# - Email notifications
# - Data validation
```

### Integration Tests
```python
# Test scenarios:
# - Complete registration flow (free events)
# - Complete purchase flow (paid events)
# - PayFast webhook processing
# - Email delivery
# - Error handling
```

### API Tests
```python
# Test all endpoints with:
# - Valid requests
# - Invalid requests
# - Authentication scenarios
# - Permission scenarios
```

## 🚀 Deployment Checklist

### Environment Variables
```bash
# Production settings
PAYFAST_MERCHANT_ID=your_production_merchant_id
PAYFAST_MERCHANT_KEY=your_production_merchant_key
PAYFAST_PASSPHRASE=your_secure_passphrase
PAYFAST_SANDBOX=false

# URLs
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-backend-domain.com

# Email settings
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
```

### Database Migration
```bash
# Run migrations
python manage.py makemigrations events
python manage.py migrate

# Create indexes
python manage.py dbshell < create_indexes.sql
```

### Monitoring
```python
# Set up monitoring for:
# - API response times
# - Payment success rates
# - Email delivery rates
# - Registration conversion rates
```

## 📞 Frontend Integration

### Automatic Detection
The frontend automatically detects when backend endpoints are available and switches from mock to real API calls.

### Testing
Use the test page at `/test/new-events` to verify:
- API endpoint functionality
- Registration flows
- Payment integration
- Error handling

### Success Criteria
✅ **Backend Implementation Complete When:**
- All API endpoints return expected responses
- PayFast integration works end-to-end
- Free event registration works
- Paid event registration with PayFast works
- Email notifications are sent
- Frontend test page shows all green checkmarks

## 📋 Implementation Timeline

**Week 1-2**: Database schema + Core event endpoints
**Week 3**: Registration system + Free events
**Week 4**: PayFast integration + Paid events  
**Week 5**: Email notifications + Analytics
**Week 6**: Testing + Deployment

**Total Estimated Time**: 6 weeks for complete implementation

The frontend is ready and waiting for these backend endpoints!
