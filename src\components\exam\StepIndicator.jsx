import React from 'react';
import { <PERSON><PERSON>ileText, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, <PERSON><PERSON><PERSON><PERSON>, FiEye } from 'react-icons/fi';

const StepIndicator = ({ steps, currentStep, examDetailsValid, questionsCount }) => {
  // Map step titles to icons
  const getStepIcon = (title) => {
    switch (title) {
      case 'Exam Details':
        return FiFileText;
      case 'Questions':
        return FiPlus;
      case 'Assignment':
        return FiUsers;
      case 'Review':
        return FiEye;
      default:
        return FiFileText;
    }
  };

  // Map step completion status
  const getStepStatus = (step, index) => {
    const stepNumber = index + 1;
    switch (step.title) {
      case 'Exam Details':
        return {
          isComplete: examDetailsValid,
          isActive: currentStep === stepNumber
        };
      case 'Questions':
        return {
          isComplete: questionsCount > 0,
          isActive: currentStep === stepNumber
        };
      case 'Assignment':
      case 'Review':
        return {
          isComplete: false, // These steps don't have completion validation yet
          isActive: currentStep === stepN<PERSON>ber
        };
      default:
        return {
          isComplete: false,
          isActive: currentStep === stepNumber
        };
    }
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const IconComponent = getStepIcon(step.title);
          const status = getStepStatus(step, index);

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-200
                  ${status.isComplete
                    ? "bg-green-600 border-green-600 text-white"
                    : status.isActive
                    ? "bg-blue-600 border-blue-600 text-white"
                    : "bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500"
                  }
                `}>
                  {status.isComplete ? (
                    <FiCheck className="w-5 h-5" />
                  ) : (
                    <IconComponent className="w-5 h-5" />
                  )}
                </div>

                <div className="mt-3 text-center">
                  <h3 className={`text-sm font-medium ${
                    status.isActive || status.isComplete
                      ? "text-gray-900 dark:text-gray-100"
                      : "text-gray-500 dark:text-gray-400"
                  }`}>
                    {step.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-24">
                    {step.description}
                  </p>
                </div>
              </div>

              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 transition-all duration-200 ${
                  status.isComplete
                    ? "bg-green-600"
                    : "bg-gray-300 dark:bg-gray-600"
                }`} />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;
