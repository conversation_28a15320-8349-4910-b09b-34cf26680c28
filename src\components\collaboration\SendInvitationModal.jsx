import React, { useState, useEffect, useRef } from 'react';
import { FiX, FiSend, <PERSON>User, FiHome, FiChevronDown, FiCheck } from 'react-icons/fi';
import { FormField, TextInput, TextArea } from '../ui/FormComponents';
import LoadingSpinner from '../ui/LoadingSpinner';
import instituteService from '../../services/instituteService';
import { useDispatch, useSelector } from 'react-redux';
import { fetchSubjects } from '../../store/slices/SubjectSlice';

const SendInvitationModal = ({
  isOpen,
  onClose,
  onSend,
  loading = false,
  userRole = 'mentor', // 'mentor' or 'institute'
  recipientType = 'mentor' // 'mentor' or 'institute'
}) => {
  const dispatch = useDispatch();
  const subjects = useSelector(state => state.subjects.subjects);
  const [formData, setFormData] = useState({
    receiver_id: '',
    proposed_hourly_rate: '',
    proposed_hours_per_week: '',
    invitation_message: '',
    expertise_areas_needed: [],
    contract_terms: '',
    received_by: recipientType
  });

  const [errors, setErrors] = useState({});

  // Institute dropdown state
  const [institutes, setInstitutes] = useState([]);
  const [institutesLoading, setInstitutesLoading] = useState(false);

  // Multi-select dropdown state
  const [isSubjectsOpen, setIsSubjectsOpen] = useState(false);
  const subjectsDropdownRef = useRef(null);

  // Load data when modal opens
  useEffect(() => {
    if (isOpen) {
      // Load subjects for expertise areas
      dispatch(fetchSubjects({ skip: 0, limit: 100 }));

      // Load institutes for institute invitations
      if (recipientType === 'institute') {
        loadInstitutes();
      }
    }
  }, [isOpen, recipientType, dispatch]);

  const loadInstitutes = async () => {
    setInstitutesLoading(true);
    try {
      const response = await instituteService.getPublicInstitutes({
        verified_only: true,
        page: 1,
        size: 100
      });
      setInstitutes(response.institutes || []);
    } catch (error) {
      console.error('Error loading institutes:', error);
      setInstitutes([]);
    } finally {
      setInstitutesLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSubjectToggle = (subjectId) => {
    setFormData(prev => ({
      ...prev,
      expertise_areas_needed: prev.expertise_areas_needed.includes(subjectId)
        ? prev.expertise_areas_needed.filter(id => id !== subjectId)
        : [...prev.expertise_areas_needed, subjectId]
    }));
  };

  // Click outside handler for subjects dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (subjectsDropdownRef.current && !subjectsDropdownRef.current.contains(event.target)) {
        setIsSubjectsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.receiver_id.trim()) {
      newErrors.receiver_id = `${recipientType === 'mentor' ? 'Mentor' : 'Institute'} selection is required`;
    }

    if (!formData.proposed_hourly_rate || formData.proposed_hourly_rate <= 0) {
      newErrors.proposed_hourly_rate = 'Valid hourly rate is required';
    }

    if (!formData.proposed_hours_per_week || formData.proposed_hours_per_week <= 0) {
      newErrors.proposed_hours_per_week = 'Valid hours per week is required';
    }

    if (!formData.invitation_message.trim()) {
      newErrors.invitation_message = 'Invitation message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    const submitData = {
      ...formData,
      proposed_hourly_rate: parseFloat(formData.proposed_hourly_rate),
      proposed_hours_per_week: parseInt(formData.proposed_hours_per_week),
      expertise_areas_needed: formData.expertise_areas_needed || []
    };
    
    try {
      await onSend(submitData);
      handleClose();
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const handleClose = () => {
    setFormData({
      receiver_id: '',
      proposed_hourly_rate: '',
      proposed_hours_per_week: '',
      invitation_message: '',
      expertise_areas_needed: [],
      contract_terms: '',
      received_by: recipientType
    });
    setErrors({});
    setInstitutes([]);
    setIsSubjectsOpen(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-violet-100 dark:bg-violet-900/30 rounded-lg">
              {recipientType === 'mentor' ? (
                <FiUser className="w-5 h-5 text-violet-600 dark:text-violet-400" />
              ) : (
                <FiHome className="w-5 h-5 text-violet-600 dark:text-violet-400" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Send Invitation to {recipientType === 'mentor' ? 'Mentor' : 'Institute'}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Create a collaboration invitation
              </p>
            </div>
          </div>
          
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Recipient Selection */}
          {recipientType === 'institute' ? (
            <FormField
              label="Select Institute"
              error={errors.receiver_id}
              required
            >
              <div className="relative">
                <select
                  name="receiver_id"
                  value={formData.receiver_id}
                  onChange={handleChange}
                  disabled={institutesLoading}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-colors ${
                    errors.receiver_id
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 dark:border-gray-600'
                  } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                >
                  <option value="">
                    {institutesLoading ? 'Loading institutes...' : 'Select an institute'}
                  </option>
                  {institutes.map((institute) => (
                    <option key={institute.id} value={institute.id}>
                      {institute.institute_name} - {institute.city}, {institute.state}
                      {institute.is_verified ? ' ✓' : ''}
                    </option>
                  ))}
                </select>
                {institutesLoading && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <LoadingSpinner size="sm" />
                  </div>
                )}
              </div>
            </FormField>
          ) : (
            <FormField
              label={`${recipientType === 'mentor' ? 'Mentor' : 'Institute'} ID`}
              error={errors.receiver_id}
              required
            >
              <TextInput
                name="receiver_id"
                placeholder={`Enter ${recipientType} ID`}
                value={formData.receiver_id}
                onChange={handleChange}
                error={errors.receiver_id}
              />
            </FormField>
          )}

          {/* Rate and Hours */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Proposed Hourly Rate ($)" error={errors.proposed_hourly_rate} required>
              <TextInput
                type="number"
                name="proposed_hourly_rate"
                placeholder="0.00"
                value={formData.proposed_hourly_rate}
                onChange={handleChange}
                error={errors.proposed_hourly_rate}
                min="0"
                step="0.01"
              />
            </FormField>

            <FormField label="Proposed Hours per Week" error={errors.proposed_hours_per_week} required>
              <TextInput
                type="number"
                name="proposed_hours_per_week"
                placeholder="0"
                value={formData.proposed_hours_per_week}
                onChange={handleChange}
                error={errors.proposed_hours_per_week}
                min="1"
              />
            </FormField>
          </div>

          {/* Expertise Areas */}
          <FormField
            label="Expertise Areas Needed"
            error={errors.expertise_areas_needed}
            hint="Select multiple subjects you need expertise in"
          >
            <div className="relative" ref={subjectsDropdownRef}>
              {/* Dropdown Button */}
              <button
                type="button"
                onClick={() => setIsSubjectsOpen(!isSubjectsOpen)}
                className={`w-full px-3 py-2 text-left border rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-colors ${
                  errors.expertise_areas_needed
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              >
                <div className="flex items-center justify-between">
                  <span className="block truncate">
                    {formData.expertise_areas_needed.length === 0
                      ? 'Select subjects...'
                      : `${formData.expertise_areas_needed.length} subject${formData.expertise_areas_needed.length === 1 ? '' : 's'} selected`
                    }
                  </span>
                  <FiChevronDown className={`w-4 h-4 transition-transform ${isSubjectsOpen ? 'rotate-180' : ''}`} />
                </div>
              </button>

              {/* Selected Items Display */}
              {formData.expertise_areas_needed.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-1">
                  {formData.expertise_areas_needed.map((subjectId) => {
                    const subject = subjects.find(s => s.id === subjectId);
                    return subject ? (
                      <span
                        key={subjectId}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-violet-100 text-violet-800 dark:bg-violet-800 dark:text-violet-100"
                      >
                        {subject.name}
                        <button
                          type="button"
                          onClick={() => handleSubjectToggle(subjectId)}
                          className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-violet-200 dark:hover:bg-violet-700"
                        >
                          <FiX className="w-3 h-3" />
                        </button>
                      </span>
                    ) : null;
                  })}
                </div>
              )}

              {/* Dropdown Menu */}
              {isSubjectsOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {subjects.length > 0 ? (
                    subjects.map((subject) => (
                      <button
                        key={subject.id}
                        type="button"
                        onClick={() => handleSubjectToggle(subject.id)}
                        className="w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-gray-900 dark:text-white">{subject.name}</span>
                          {formData.expertise_areas_needed.includes(subject.id) && (
                            <FiCheck className="w-4 h-4 text-violet-600 dark:text-violet-400" />
                          )}
                        </div>
                      </button>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-gray-500 dark:text-gray-400">
                      Loading subjects...
                    </div>
                  )}
                </div>
              )}
            </div>
          </FormField>

          {/* Invitation Message */}
          <FormField label="Invitation Message" error={errors.invitation_message} required>
            <TextArea
              name="invitation_message"
              placeholder="Write a personalized message explaining the collaboration opportunity..."
              value={formData.invitation_message}
              onChange={handleChange}
              error={errors.invitation_message}
              rows={4}
            />
          </FormField>

          {/* Contract Terms */}
          <FormField label="Contract Terms" error={errors.contract_terms}>
            <TextArea
              name="contract_terms"
              placeholder="Specify any contract terms, conditions, or expectations..."
              value={formData.contract_terms}
              onChange={handleChange}
              error={errors.contract_terms}
              rows={3}
            />
          </FormField>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-2 bg-violet-600 hover:bg-violet-700 disabled:bg-violet-400 text-white rounded-lg transition-colors"
            >
              {loading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Sending...</span>
                </>
              ) : (
                <>
                  <FiSend className="w-4 h-4" />
                  <span>Send Invitation</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SendInvitationModal;
