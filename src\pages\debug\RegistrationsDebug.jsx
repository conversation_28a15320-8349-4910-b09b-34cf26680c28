/**
 * Debug page for testing registrations API
 */

import React, { useState } from 'react';
import { EventBookingService } from '../../services/eventBookingService';
import newEventService from '../../services/newEventService';

const RegistrationsDebug = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);

  const testEventBookingService = async () => {
    setLoading(true);
    try {
      console.log('Testing EventBookingService...');
      const data = await EventBookingService.getUserRegistrations();
      setResults(prev => ({ ...prev, eventBookingService: data }));
      console.log('EventBookingService result:', data);
    } catch (error) {
      console.error('EventBookingService error:', error);
      setResults(prev => ({ ...prev, eventBookingServiceError: error.message }));
    }
    setLoading(false);
  };

  const testNewEventService = async () => {
    setLoading(true);
    try {
      console.log('Testing newEventService...');
      const data = await newEventService.getMyRegistrations();
      setResults(prev => ({ ...prev, newEventService: data }));
      console.log('newEventService result:', data);
    } catch (error) {
      console.error('newEventService error:', error);
      setResults(prev => ({ ...prev, newEventServiceError: error.message }));
    }
    setLoading(false);
  };

  const testDirectAPI = async () => {
    setLoading(true);
    try {
      console.log('Testing direct API call...');
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/events/registrations/my-registrations', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setResults(prev => ({ ...prev, directAPI: data }));
      console.log('Direct API result:', data);
    } catch (error) {
      console.error('Direct API error:', error);
      setResults(prev => ({ ...prev, directAPIError: error.message }));
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Registrations API Debug</h1>
        
        <div className="space-y-6">
          {/* Test Buttons */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">API Tests</h2>
            <div className="space-x-4">
              <button
                onClick={testEventBookingService}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Test EventBookingService
              </button>
              <button
                onClick={testNewEventService}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Test newEventService
              </button>
              <button
                onClick={testDirectAPI}
                disabled={loading}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Test Direct API
              </button>
            </div>
          </div>

          {/* Results */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>

          {/* Auth Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Auth Info</h2>
            <div className="space-y-2">
              <p><strong>Token:</strong> {localStorage.getItem('token') ? 'Present' : 'Missing'}</p>
              <p><strong>User Role:</strong> {localStorage.getItem('userRole') || 'Not set'}</p>
              <p><strong>User ID:</strong> {localStorage.getItem('userId') || 'Not set'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegistrationsDebug;
