/**
 * UserEventDashboard Component
 * 
 * Dashboard for users to view their registered events and tickets
 */

import React, { useState, useEffect } from 'react';
import {
  FiCalendar,
  FiMapPin,
  FiClock,
  FiDownload,
  FiEye,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiFilter,
  FiSearch,
  FiDollarSign
} from 'react-icons/fi';
import { format } from 'date-fns';
import { EventBookingService } from '../../services/eventBookingService';
import newEventService from '../../services/newEventService';
import { LoadingSpinner, ErrorMessage } from '../ui';
import { useNotification } from '../../contexts/NotificationContext';

const UserEventDashboard = ({ embedded = false }) => {
  const { showSuccess, showError, showInfo } = useNotification();
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [dashboard, setDashboard] = useState(null);
  const [registrations, setRegistrations] = useState([]);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: 'all',
    search: '',
    upcoming: false
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load dashboard summary
      const dashboardData = await EventBookingService.getUserEventDashboard();

      // Ensure dashboard data is a valid object
      if (dashboardData && typeof dashboardData === 'object' && !Array.isArray(dashboardData)) {
        setDashboard(dashboardData);
      } else {
        console.warn('Invalid dashboard data received:', dashboardData);
        setDashboard({
          total_registrations: 0,
          confirmed_registrations: 0,
          upcoming_events: 0,
          total_tickets: 0
        });
      }

      // Load user registrations
      const registrationsData = await EventBookingService.getUserRegistrations({
        skip: 0,
        limit: 50
      });
      // API returns array directly, not wrapped in object
      const registrationsArray = Array.isArray(registrationsData) ? registrationsData : registrationsData.registrations || [];

      // Ensure all registrations are valid objects with required nested structure
      const validRegistrations = registrationsArray.filter(reg =>
        reg &&
        typeof reg === 'object' &&
        reg.registration_id &&
        reg.event &&
        typeof reg.event === 'object'
      );

      console.log('Loaded registrations:', validRegistrations.length, 'valid out of', registrationsArray.length);
      setRegistrations(validRegistrations);

    } catch (err) {
      setError(err.message || 'Failed to load event dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadDashboardData();
      showSuccess('Dashboard refreshed successfully');
    } catch (err) {
      showError('Failed to refresh dashboard');
    } finally {
      setRefreshing(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handlePayment = async (registration) => {
    try {
      showInfo('Creating payment link...');

      // Use the same payment service as MyRegistrations page
      const paymentResult = await newEventService.payForRegistration(registration.registration_id);

      if (paymentResult.success) {
        showInfo('Redirecting to payment...');
        // The service will automatically handle the PayFast redirect
      } else {
        showError('Failed to create payment link');
      }

    } catch (error) {
      console.error('Payment error:', error);
      showError(error.message || 'Failed to process payment');
    }
  };

  // Filter registrations based on current filters
  const filteredRegistrations = registrations.filter(registration => {
    // Status filter
    if (filters.status !== 'all' && registration.status?.toLowerCase() !== filters.status.toLowerCase()) {
      return false;
    }

    // Search filter
    if (filters.search && !registration.event?.title?.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    // Upcoming filter
    if (filters.upcoming && new Date(registration.event?.start_datetime) <= new Date()) {
      return false;
    }

    return true;
  });

  const formatDate = (dateString) => {
    if (!dateString) return 'Date TBD';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return format(date, 'MMM dd, yyyy');
    } catch (error) {
      console.warn('Date formatting error:', error, 'for date:', dateString);
      return 'Date TBD';
    }
  };

  const formatTime = (dateString) => {
    if (!dateString) return 'Time TBD';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Time TBD';
      return format(date, 'h:mm a');
    } catch (error) {
      console.warn('Time formatting error:', error, 'for date:', dateString);
      return 'Time TBD';
    }
  };

  const getStatusBadge = (status, paymentStatus) => {
    // Handle API status values (uppercase)
    const normalizedStatus = status?.toLowerCase();
    const normalizedPaymentStatus = paymentStatus?.toLowerCase();

    if (normalizedStatus === 'confirmed') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <FiCheck className="w-3 h-3 mr-1" />
          Confirmed
        </span>
      );
    } else if (normalizedStatus === 'pending') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <FiClock className="w-3 h-3 mr-1" />
          {normalizedPaymentStatus === 'pending' ? 'Payment Pending' : 'Pending'}
        </span>
      );
    } else if (normalizedStatus === 'cancelled') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <FiX className="w-3 h-3 mr-1" />
          Cancelled
        </span>
      );
    }

    // Fallback for unknown status
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {status || 'Unknown'}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadDashboardData} />;
  }

  return (
    <div className="space-y-6">
      {/* Header - only show if not embedded */}
      {!embedded && (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Events</h1>
            <p className="text-gray-600">Manage your event registrations and tickets</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      )}

      {/* Refresh button for embedded mode */}
      {embedded && (
        <div className="flex justify-end">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      )}

      {/* Dashboard Stats */}
      {dashboard && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCalendar className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Events</p>
                <p className="text-2xl font-semibold text-gray-900">{Number(dashboard.total_registrations) || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCheck className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Confirmed</p>
                <p className="text-2xl font-semibold text-gray-900">{Number(dashboard.confirmed_registrations) || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiClock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Upcoming</p>
                <p className="text-2xl font-semibold text-gray-900">{Number(dashboard.upcoming_events) || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiDownload className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Tickets</p>
                <p className="text-2xl font-semibold text-gray-900">{Number(dashboard.total_tickets) || 0}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search events..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="confirmed">Confirmed</option>
            <option value="pending">Pending</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.upcoming}
              onChange={(e) => handleFilterChange('upcoming', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Upcoming only</span>
          </label>
        </div>
      </div>

      {/* Registrations List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Event Registrations ({filteredRegistrations.length})
          </h2>
        </div>

        {filteredRegistrations.length === 0 ? (
          <div className="text-center py-12">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
            <p className="text-gray-500">
              {filters.search || filters.status !== 'all' || filters.upcoming
                ? 'Try adjusting your filters'
                : 'You haven\'t registered for any events yet'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredRegistrations.map((registration) => (
              <div key={registration.registration_id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <h3 className="text-lg font-medium text-gray-900">
                        {String(registration.event?.title || 'Untitled Event')}
                      </h3>
                      {getStatusBadge(registration.status, registration.payment_status)}
                    </div>

                    {/* Event Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center">
                        <FiCalendar className="w-4 h-4 mr-2" />
                        <span>
                          {formatDate(registration.event?.start_datetime)} at {formatTime(registration.event?.start_datetime)}
                        </span>
                      </div>

                      {registration.event?.location && (
                        <div className="flex items-center">
                          <FiMapPin className="w-4 h-4 mr-2" />
                          <span>{String(registration.event.location)}</span>
                        </div>
                      )}
                    </div>

                    {/* Ticket & Payment Info */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <div className="font-medium text-blue-900">{String(registration.ticket?.name || 'Standard Ticket')}</div>
                        <div className="text-blue-700 text-xs">{String(registration.ticket?.description || 'Ticket')}</div>
                      </div>

                      <div className="bg-green-50 p-3 rounded-lg">
                        <div className="font-medium text-green-900">
                          {String(registration.total_amount || 0)} {String(registration.currency || 'ZAR')}
                        </div>
                        <div className="text-green-700 text-xs">
                          Quantity: {String(registration.quantity || 1)}
                        </div>
                      </div>

                      <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="font-medium text-gray-900 text-xs">
                          {String(registration.registration_number || 'N/A')}
                        </div>
                        <div className="text-gray-600 text-xs">
                          Registered: {formatDate(registration.registered_at)}
                        </div>
                      </div>
                    </div>

                    {/* Attendee Info */}
                    <div className="bg-gray-50 p-3 rounded-lg text-sm">
                      <div className="font-medium text-gray-900 mb-1">Attendee Information</div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-gray-600">
                        <div><strong>Name:</strong> {String(registration.attendee_info?.name || 'N/A')}</div>
                        {registration.attendee_info?.email && (
                          <div><strong>Email:</strong> {String(registration.attendee_info.email)}</div>
                        )}
                        {registration.attendee_info?.phone && (
                          <div><strong>Phone:</strong> {String(registration.attendee_info.phone)}</div>
                        )}
                        {registration.attendee_info?.company && (
                          <div><strong>Company:</strong> {String(registration.attendee_info.company)}</div>
                        )}
                        {registration.attendee_info?.position && (
                          <div><strong>Position:</strong> {String(registration.attendee_info.position)}</div>
                        )}
                        <div><strong>Registration #:</strong> {String(registration.registration_number || 'N/A')}</div>
                      </div>
                      {registration.special_requirements && (
                        <div className="mt-2 text-xs text-orange-600">
                          <strong>Special Requirements:</strong> {String(registration.special_requirements)}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {/* Payment button for pending registrations */}
                    {registration.status?.toLowerCase() === 'pending' && registration.payment_status?.toLowerCase() === 'pending' && (
                      <button
                        onClick={() => handlePayment(registration)}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                        title="Complete Payment"
                      >
                        Pay Now
                      </button>
                    )}

                    {/* Download ticket for confirmed registrations */}
                    {registration.status?.toLowerCase() === 'confirmed' && (
                      <button
                        onClick={() => {/* Handle download ticket */}}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                        title="Download Ticket"
                      >
                        <FiDownload className="w-4 h-4 mr-1" />
                        Ticket
                      </button>
                    )}

                    {/* View details button */}
                    <button
                      onClick={() => {/* Handle view details */}}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="View Event Details"
                    >
                      <FiEye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserEventDashboard;
