import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';
import competitionService from '../../services/competitionService';

// Base URL for competitions endpoints
const API_BASE = `${API_BASE_URL}/api/competitions`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Create Competition from Exam
export const createCompetitionFromExam = createAsyncThunk(
  'competitions/createFromExam',
  async ({ examId, queryParams }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.post(`${API_BASE}/from-exam/${examId}?${params}`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get All Competitions
export const fetchCompetitions = createAsyncThunk(
  'competitions/fetchCompetitions',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${API_BASE}?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Assign Mentor to Competition (Organizers Only)
export const assignMentorToCompetition = createAsyncThunk(
  'competitions/assignMentor',
  async ({ competitionId, queryParams }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(item => params.append(key, item));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const res = await axios.post(`${API_BASE}/${competitionId}/assign-mentor?${params}`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// New Competition Service Thunks

// Create Competition
export const createCompetition = createAsyncThunk(
  'competitions/create',
  async (competitionData, { rejectWithValue }) => {
    try {
      return await competitionService.createCompetition(competitionData);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get Competition by ID
export const getCompetitionById = createAsyncThunk(
  'competitions/getById',
  async (competitionId, { rejectWithValue }) => {
    try {
      return await competitionService.getCompetition(competitionId);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update Competition
export const updateCompetition = createAsyncThunk(
  'competitions/update',
  async ({ competitionId, updateData }, { rejectWithValue }) => {
    try {
      return await competitionService.updateCompetition(competitionId, updateData);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get Institute Competitions
export const getInstituteCompetitions = createAsyncThunk(
  'competitions/getInstituteCompetitions',
  async ({ instituteId, params }, { rejectWithValue }) => {
    try {
      return await competitionService.getInstituteCompetitions(instituteId, params);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Register for Competition
export const registerForCompetition = createAsyncThunk(
  'competitions/register',
  async ({ competitionId, registrationData }, { rejectWithValue }) => {
    try {
      return await competitionService.registerForCompetition(competitionId, registrationData);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get Competition Leaderboard
export const getCompetitionLeaderboard = createAsyncThunk(
  'competitions/getLeaderboard',
  async ({ competitionId, params }, { rejectWithValue }) => {
    try {
      return await competitionService.getCompetitionLeaderboard(competitionId, params);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get Competition Statistics
export const getCompetitionStatistics = createAsyncThunk(
  'competitions/getStatistics',
  async (competitionId, { rejectWithValue }) => {
    try {
      return await competitionService.getCompetitionStatistics(competitionId);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get My Mentor Assignments
export const getMyMentorAssignments = createAsyncThunk(
  'competitions/getMyMentorAssignments',
  async (params, { rejectWithValue }) => {
    try {
      return await competitionService.getMyMentorAssignments(params);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Submit Mentor Evaluation
export const submitMentorEvaluation = createAsyncThunk(
  'competitions/submitMentorEvaluation',
  async ({ attemptId, evaluationData }, { rejectWithValue }) => {
    try {
      return await competitionService.submitMentorEvaluation(attemptId, evaluationData);
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Competitions list
  competitions: [],
  competitionsLoading: false,
  competitionsError: null,
  competitionsPagination: {
    total: 0,
    skip: 0,
    limit: 20
  },

  // Competition creation
  createLoading: false,
  createError: null,
  createSuccess: false,

  // Mentor assignment
  assignMentorLoading: false,
  assignMentorError: null,
  assignMentorSuccess: false,

  // Filters
  filters: {
    skip: 0,
    limit: 20,
    category_id: null,
    status: null
  },

  // UI state
  selectedCompetition: null,
  showCompetitionDetails: false,

  // New state for competition service
  currentCompetition: null,
  currentCompetitionLoading: false,
  currentCompetitionError: null,

  // Institute competitions
  instituteCompetitions: [],
  instituteCompetitionsLoading: false,
  instituteCompetitionsError: null,

  // Registration
  registrationLoading: false,
  registrationError: null,
  registrationSuccess: false,

  // Leaderboard
  leaderboard: [],
  leaderboardLoading: false,
  leaderboardError: null,

  // Statistics
  statistics: null,
  statisticsLoading: false,
  statisticsError: null,

  // Mentor assignments
  mentorAssignments: [],
  mentorAssignmentsLoading: false,
  mentorAssignmentsError: null,

  // Evaluation
  evaluationLoading: false,
  evaluationError: null,
  evaluationSuccess: false
};

// Competitions Slice
const competitionsSlice = createSlice({
  name: 'competitions',
  initialState,
  reducers: {
    // Update filters
    updateFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Reset filters
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },

    // Select competition for details view
    selectCompetition: (state, action) => {
      state.selectedCompetition = action.payload;
      state.showCompetitionDetails = true;
    },

    // Close competition details
    closeCompetitionDetails: (state) => {
      state.selectedCompetition = null;
      state.showCompetitionDetails = false;
    },

    // Clear errors
    clearErrors: (state) => {
      state.competitionsError = null;
      state.createError = null;
      state.assignMentorError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.createSuccess = false;
      state.assignMentorSuccess = false;
    },

    // Reset competitions state
    resetCompetitionsState: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    builder
      // Create Competition from Exam
      .addCase(createCompetitionFromExam.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createCompetitionFromExam.fulfilled, (state, action) => {
        state.createLoading = false;
        state.createSuccess = true;
        // Add the new competition to the list
        // Ensure competitions is an array before adding
        if (!Array.isArray(state.competitions)) {
          state.competitions = [];
        }
        state.competitions.unshift(action.payload);
      })
      .addCase(createCompetitionFromExam.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Fetch Competitions
      .addCase(fetchCompetitions.pending, (state) => {
        state.competitionsLoading = true;
        state.competitionsError = null;
      })
      .addCase(fetchCompetitions.fulfilled, (state, action) => {
        state.competitionsLoading = false;
        // Handle different possible response structures
        const competitions = action.payload?.competitions || action.payload || [];
        // Ensure competitions is an array
        state.competitions = Array.isArray(competitions) ? competitions : [];

        // Update pagination if provided
        if (action.payload?.total !== undefined) {
          state.competitionsPagination = {
            total: action.payload.total,
            skip: action.payload.skip || 0,
            limit: action.payload.limit || 20
          };
        }
      })
      .addCase(fetchCompetitions.rejected, (state, action) => {
        state.competitionsLoading = false;
        state.competitionsError = action.payload;
      })

      // Assign Mentor to Competition
      .addCase(assignMentorToCompetition.pending, (state) => {
        state.assignMentorLoading = true;
        state.assignMentorError = null;
        state.assignMentorSuccess = false;
      })
      .addCase(assignMentorToCompetition.fulfilled, (state, action) => {
        state.assignMentorLoading = false;
        state.assignMentorSuccess = true;
      })
      .addCase(assignMentorToCompetition.rejected, (state, action) => {
        state.assignMentorLoading = false;
        state.assignMentorError = action.payload;
      })

      // Create Competition
      .addCase(createCompetition.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createCompetition.fulfilled, (state, action) => {
        state.createLoading = false;
        state.createSuccess = true;
        // Add to competitions list if it exists
        if (Array.isArray(state.competitions)) {
          state.competitions.unshift(action.payload);
        }
      })
      .addCase(createCompetition.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Get Competition by ID
      .addCase(getCompetitionById.pending, (state) => {
        state.currentCompetitionLoading = true;
        state.currentCompetitionError = null;
      })
      .addCase(getCompetitionById.fulfilled, (state, action) => {
        state.currentCompetitionLoading = false;
        state.currentCompetition = action.payload;
      })
      .addCase(getCompetitionById.rejected, (state, action) => {
        state.currentCompetitionLoading = false;
        state.currentCompetitionError = action.payload;
      })

      // Update Competition
      .addCase(updateCompetition.pending, (state) => {
        state.currentCompetitionLoading = true;
        state.currentCompetitionError = null;
      })
      .addCase(updateCompetition.fulfilled, (state, action) => {
        state.currentCompetitionLoading = false;
        state.currentCompetition = action.payload;
        // Update in competitions list if it exists
        if (Array.isArray(state.competitions)) {
          const index = state.competitions.findIndex(c => c.id === action.payload.id);
          if (index !== -1) {
            state.competitions[index] = action.payload;
          }
        }
      })
      .addCase(updateCompetition.rejected, (state, action) => {
        state.currentCompetitionLoading = false;
        state.currentCompetitionError = action.payload;
      })

      // Get Institute Competitions
      .addCase(getInstituteCompetitions.pending, (state) => {
        state.instituteCompetitionsLoading = true;
        state.instituteCompetitionsError = null;
      })
      .addCase(getInstituteCompetitions.fulfilled, (state, action) => {
        state.instituteCompetitionsLoading = false;
        state.instituteCompetitions = action.payload.competitions || [];
      })
      .addCase(getInstituteCompetitions.rejected, (state, action) => {
        state.instituteCompetitionsLoading = false;
        state.instituteCompetitionsError = action.payload;
      })

      // Register for Competition
      .addCase(registerForCompetition.pending, (state) => {
        state.registrationLoading = true;
        state.registrationError = null;
        state.registrationSuccess = false;
      })
      .addCase(registerForCompetition.fulfilled, (state, action) => {
        state.registrationLoading = false;
        state.registrationSuccess = true;
      })
      .addCase(registerForCompetition.rejected, (state, action) => {
        state.registrationLoading = false;
        state.registrationError = action.payload;
      })

      // Get Competition Leaderboard
      .addCase(getCompetitionLeaderboard.pending, (state) => {
        state.leaderboardLoading = true;
        state.leaderboardError = null;
      })
      .addCase(getCompetitionLeaderboard.fulfilled, (state, action) => {
        state.leaderboardLoading = false;
        state.leaderboard = action.payload.rankings || [];
      })
      .addCase(getCompetitionLeaderboard.rejected, (state, action) => {
        state.leaderboardLoading = false;
        state.leaderboardError = action.payload;
      })

      // Get Competition Statistics
      .addCase(getCompetitionStatistics.pending, (state) => {
        state.statisticsLoading = true;
        state.statisticsError = null;
      })
      .addCase(getCompetitionStatistics.fulfilled, (state, action) => {
        state.statisticsLoading = false;
        state.statistics = action.payload;
      })
      .addCase(getCompetitionStatistics.rejected, (state, action) => {
        state.statisticsLoading = false;
        state.statisticsError = action.payload;
      })

      // Get My Mentor Assignments
      .addCase(getMyMentorAssignments.pending, (state) => {
        state.mentorAssignmentsLoading = true;
        state.mentorAssignmentsError = null;
      })
      .addCase(getMyMentorAssignments.fulfilled, (state, action) => {
        state.mentorAssignmentsLoading = false;
        state.mentorAssignments = action.payload.assignments || [];
      })
      .addCase(getMyMentorAssignments.rejected, (state, action) => {
        state.mentorAssignmentsLoading = false;
        state.mentorAssignmentsError = action.payload;
      })

      // Submit Mentor Evaluation
      .addCase(submitMentorEvaluation.pending, (state) => {
        state.evaluationLoading = true;
        state.evaluationError = null;
        state.evaluationSuccess = false;
      })
      .addCase(submitMentorEvaluation.fulfilled, (state, action) => {
        state.evaluationLoading = false;
        state.evaluationSuccess = true;
      })
      .addCase(submitMentorEvaluation.rejected, (state, action) => {
        state.evaluationLoading = false;
        state.evaluationError = action.payload;
      });
  }
});

// Actions
export const {
  updateFilters,
  resetFilters,
  selectCompetition,
  closeCompetitionDetails,
  clearErrors,
  clearSuccessStates,
  resetCompetitionsState
} = competitionsSlice.actions;

// Selectors
export const selectCompetitions = (state) => {
  const competitions = state.competitions.competitions;
  // Ensure competitions is always an array
  return Array.isArray(competitions) ? competitions : [];
};
export const selectCompetitionsLoading = (state) => state.competitions.competitionsLoading;
export const selectCompetitionsError = (state) => state.competitions.competitionsError;
export const selectCompetitionsPagination = (state) => state.competitions.competitionsPagination;

export const selectCreateLoading = (state) => state.competitions.createLoading;
export const selectCreateError = (state) => state.competitions.createError;
export const selectCreateSuccess = (state) => state.competitions.createSuccess;

export const selectAssignMentorLoading = (state) => state.competitions.assignMentorLoading;
export const selectAssignMentorError = (state) => state.competitions.assignMentorError;
export const selectAssignMentorSuccess = (state) => state.competitions.assignMentorSuccess;

export const selectFilters = (state) => state.competitions.filters;
export const selectSelectedCompetition = (state) => state.competitions.selectedCompetition;
export const selectShowCompetitionDetails = (state) => state.competitions.showCompetitionDetails;

// Memoized helper selectors
export const selectUpcomingCompetitions = createSelector(
  [selectCompetitions],
  (competitions) => {
    // Ensure competitions is an array before filtering
    if (!Array.isArray(competitions)) {
      return [];
    }
    return competitions.filter(comp => comp.status === 'upcoming');
  }
);

export const selectOngoingCompetitions = createSelector(
  [selectCompetitions],
  (competitions) => {
    // Ensure competitions is an array before filtering
    if (!Array.isArray(competitions)) {
      return [];
    }
    return competitions.filter(comp => comp.status === 'ongoing');
  }
);

export const selectCompletedCompetitions = createSelector(
  [selectCompetitions],
  (competitions) => {
    // Ensure competitions is an array before filtering
    if (!Array.isArray(competitions)) {
      return [];
    }
    return competitions.filter(comp => comp.status === 'completed');
  }
);

export const selectCompetitionsByCategory = (categoryId) => createSelector(
  [selectCompetitions],
  (competitions) => {
    // Ensure competitions is an array before filtering
    if (!Array.isArray(competitions)) {
      return [];
    }
    return competitions.filter(comp => comp.category?.id === categoryId);
  }
);

// New selectors for competition service
export const selectCurrentCompetition = (state) => state.competitions.currentCompetition;
export const selectCurrentCompetitionLoading = (state) => state.competitions.currentCompetitionLoading;
export const selectCurrentCompetitionError = (state) => state.competitions.currentCompetitionError;

export const selectInstituteCompetitions = (state) => state.competitions.instituteCompetitions;
export const selectInstituteCompetitionsLoading = (state) => state.competitions.instituteCompetitionsLoading;
export const selectInstituteCompetitionsError = (state) => state.competitions.instituteCompetitionsError;

export const selectRegistrationLoading = (state) => state.competitions.registrationLoading;
export const selectRegistrationError = (state) => state.competitions.registrationError;
export const selectRegistrationSuccess = (state) => state.competitions.registrationSuccess;

export const selectLeaderboard = (state) => state.competitions.leaderboard;
export const selectLeaderboardLoading = (state) => state.competitions.leaderboardLoading;
export const selectLeaderboardError = (state) => state.competitions.leaderboardError;

export const selectStatistics = (state) => state.competitions.statistics;
export const selectStatisticsLoading = (state) => state.competitions.statisticsLoading;
export const selectStatisticsError = (state) => state.competitions.statisticsError;

export const selectMentorAssignments = (state) => state.competitions.mentorAssignments;
export const selectMentorAssignmentsLoading = (state) => state.competitions.mentorAssignmentsLoading;
export const selectMentorAssignmentsError = (state) => state.competitions.mentorAssignmentsError;

export const selectEvaluationLoading = (state) => state.competitions.evaluationLoading;
export const selectEvaluationError = (state) => state.competitions.evaluationError;
export const selectEvaluationSuccess = (state) => state.competitions.evaluationSuccess;

export default competitionsSlice.reducer;
