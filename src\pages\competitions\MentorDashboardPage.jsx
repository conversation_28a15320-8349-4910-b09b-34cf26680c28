import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  <PERSON><PERSON><PERSON>,
  FiUsers,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiBarChart3,
  FiEye,
  FiBookOpen
} from 'react-icons/fi';
import {
  getMyMentorAssignments,
  selectMentorAssignments,
  selectMentorAssignmentsLoading,
  selectMentorAssignmentsError
} from '../../store/slices/CompetitionsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import MentorEvaluation from '../../components/competitions/MentorEvaluation';
import ExamsTab from '../../components/exams/ExamsTab';

const MentorDashboardPage = () => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedAssignment, setSelectedAssignment] = useState(null);

  // Redux state
  const assignments = useSelector(selectMentorAssignments);
  const loading = useSelector(selectMentorAssignmentsLoading);
  const error = useSelector(selectMentorAssignmentsError);

  useEffect(() => {
    dispatch(getMyMentorAssignments());
  }, [dispatch]);

  const handleViewEvaluations = (assignment) => {
    setSelectedAssignment(assignment);
    setActiveTab('evaluation');
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'green', label: 'Active', icon: FiCheckCircle },
      pending: { color: 'yellow', label: 'Pending', icon: FiClock },
      completed: { color: 'blue', label: 'Completed', icon: FiCheckCircle },
      cancelled: { color: 'red', label: 'Cancelled', icon: FiAlertCircle }
    };

    const config = statusConfig[status] || { color: 'gray', label: status, icon: FiAlertCircle };
    const Icon = config.icon;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${config.color}-100 text-${config.color}-800`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </span>
    );
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600`} />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-semibold text-gray-900">{value}</h3>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
      </div>
    </div>
  );

  const TabButton = ({ id, label, isActive, onClick }) => (
    <button
      onClick={() => onClick(id)}
      className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      {label}
    </button>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  // Calculate statistics
  const totalAssignments = assignments.length;
  const activeAssignments = assignments.filter(a => a.status === 'active').length;
  const pendingEvaluations = assignments.reduce((sum, a) => sum + (a.pending_evaluations || 0), 0);
  const completedEvaluations = assignments.reduce((sum, a) => sum + (a.completed_evaluations || 0), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mentor Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage your competition mentoring assignments</p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-2 border-b border-gray-200 pb-4">
        <TabButton
          id="overview"
          label="Overview"
          isActive={activeTab === 'overview'}
          onClick={setActiveTab}
        />
        <TabButton
          id="exams"
          label="My Exams"
          isActive={activeTab === 'exams'}
          onClick={setActiveTab}
        />
        {selectedAssignment && (
          <TabButton
            id="evaluation"
            label={`Evaluate - ${selectedAssignment.competition_title}`}
            isActive={activeTab === 'evaluation'}
            onClick={setActiveTab}
          />
        )}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              icon={FiAward}
              title="Total Assignments"
              value={totalAssignments}
              subtitle="Competition mentoring roles"
              color="blue"
            />
            <StatCard
              icon={FiUsers}
              title="Active Competitions"
              value={activeAssignments}
              subtitle="Currently mentoring"
              color="green"
            />
            <StatCard
              icon={FiClock}
              title="Pending Evaluations"
              value={pendingEvaluations}
              subtitle="Awaiting your review"
              color="yellow"
            />
            <StatCard
              icon={FiCheckCircle}
              title="Completed Evaluations"
              value={completedEvaluations}
              subtitle="Successfully reviewed"
              color="purple"
            />
          </div>

          {/* Assignments List */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">My Assignments</h2>
            </div>
            
            {assignments.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {assignments.map((assignment) => (
                  <div key={assignment.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-gray-900">
                            {assignment.competition_title}
                          </h3>
                          {getStatusBadge(assignment.status)}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">
                          {assignment.competition_description}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Start Date:</span>
                            <p className="text-gray-600">
                              {new Date(assignment.competition_start_date).toLocaleDateString()}
                            </p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">End Date:</span>
                            <p className="text-gray-600">
                              {new Date(assignment.competition_end_date).toLocaleDateString()}
                            </p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Participants:</span>
                            <p className="text-gray-600">
                              {assignment.total_participants || 0} students
                            </p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Expertise Areas:</span>
                            <p className="text-gray-600">
                              {assignment.expertise_areas?.join(', ') || 'General'}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="ml-6 flex flex-col space-y-2">
                        {assignment.pending_evaluations > 0 && (
                          <button
                            onClick={() => handleViewEvaluations(assignment)}
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                          >
                            <FiEye className="h-4 w-4 mr-2" />
                            Evaluate ({assignment.pending_evaluations})
                          </button>
                        )}
                        
                        <button
                          onClick={() => handleViewEvaluations(assignment)}
                          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <FiBarChart3 className="h-4 w-4 mr-2" />
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FiAward className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Assignments Yet</h3>
                <p className="text-gray-600">
                  You haven't been assigned to any competitions yet. Check back later or contact your institute.
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Exams Tab */}
      {activeTab === 'exams' && (
        <div className="space-y-6">
          <ExamsTab />
        </div>
      )}

      {/* Evaluation Tab */}
      {activeTab === 'evaluation' && selectedAssignment && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {selectedAssignment.competition_title}
                </h2>
                <p className="text-gray-600">{selectedAssignment.competition_description}</p>
              </div>
              <button
                onClick={() => setActiveTab('overview')}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ← Back to Overview
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Total Participants:</span>
                <p className="text-gray-600">{selectedAssignment.total_participants || 0}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Pending Evaluations:</span>
                <p className="text-gray-600">{selectedAssignment.pending_evaluations || 0}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Completed Evaluations:</span>
                <p className="text-gray-600">{selectedAssignment.completed_evaluations || 0}</p>
              </div>
            </div>
          </div>

          <MentorEvaluation
            competitionId={selectedAssignment.competition_id}
            mentorId={selectedAssignment.mentor_id}
          />
        </div>
      )}
    </div>
  );
};

export default MentorDashboardPage;
