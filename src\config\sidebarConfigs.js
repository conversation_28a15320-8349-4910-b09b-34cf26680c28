import {
  HomeIcon,
  UserIcon,
  Cog6ToothIcon,
  BookOpenIcon,
  UsersIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
  ChartBarIcon,
  CreditCardIcon,
  CogIcon,
  BellIcon,
  EnvelopeIcon,
  SparklesIcon,
  ClipboardDocumentCheckIcon,
  CalendarDaysIcon,
  TrophyIcon,
  UserPlusIcon,
  StarIcon,
  BanknotesIcon,
  ReceiptPercentIcon,
  PresentationChartLineIcon
} from "@heroicons/react/24/outline";

export const sidebarConfigs = {
  admin: [
    { label: "Dashboard", icon: HomeIcon, path: "/admin/dashboard" },
    { label: "Users", icon: UsersIcon, path: "/admin/users" },
    { label: "Institute Approvals", icon: BuildingOfficeIcon, path: "/admin/institute-approvals" },
    { label: "Events", icon: CalendarDaysIcon, path: "/admin/events" },
    { label: "Payment Management", icon: CreditCardIcon, path: "/admin/payment-management" },
    {
      label: "Revenue & Payouts",
      icon: BanknotesIcon,
      children: [
        { label: "Revenue Overview", icon: PresentationChartLineIcon, path: "/admin/revenue/overview" },
        { label: "Payout Management", icon: BanknotesIcon, path: "/admin/payouts/management" },
        { label: "Analytics", icon: ChartBarIcon, path: "/admin/revenue/analytics" },
      ],
    },
    {
      label: "Config",
      icon: DocumentTextIcon,
      children: [
        { label: "Material", icon: DocumentTextIcon, path: "/admin/material" },
        { label: "Grades", icon: AcademicCapIcon, path: "/admin/grades" },
      ],
    },
    { label: "Plans", icon: CreditCardIcon, path: "/admin/plans" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/admin/settings" },
  ],
  student: [
    { label: "Dashboard", icon: HomeIcon, path: "/student/dashboard" },
    { label: "Classes", icon: BookOpenIcon, path: "/student/classes" },
    { label: "Tasks", icon: ClipboardDocumentCheckIcon, path: "/student/tasks" },
    { label: "Exams", icon: AcademicCapIcon, path: "/student/exams" },
    { label: "Events", icon: CalendarDaysIcon, path: "/student/events" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/student/settings" },
  ],
  teacher: [
    { label: "Dashboard", icon: HomeIcon, path: "/teacher/dashboard" },
    { label: "My Classes", icon: AcademicCapIcon, path: "/teacher/classes" },
    { label: "Tasks", icon: ClipboardDocumentCheckIcon, path: "/teacher/tasks" },
    { label: "Exams", icon: AcademicCapIcon, path: "/teacher/exams" },
    { label: "Events", icon: CalendarDaysIcon, path: "/teacher/events" },
    { label: "Competitions", icon: TrophyIcon, path: "/teacher/competitions" },
    { label: "Mentorship", icon: UserPlusIcon, path: "/teacher/mentorship" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/teacher/settings" },
  ],
  mentor: [
    { label: "Dashboard", icon: HomeIcon, path: "/mentor/dashboard" },
    { label: "Collaborations", icon: UsersIcon, path: "/mentor/collaborations" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/mentor/settings" },
  ],
  institute: [
    { label: "Dashboard", icon: HomeIcon, path: "/institute/dashboard" },
    { label: "Events", icon: CalendarDaysIcon, path: "/institute/events" },
    { label: "Exams", icon: AcademicCapIcon, path: "/institute/exams" },
    {
      label: "Payouts",
      icon: BanknotesIcon,
      children: [
        { label: "Payout Dashboard", icon: ChartBarIcon, path: "/institute/payouts/dashboard" },
        { label: "Sales Reports", icon: ReceiptPercentIcon, path: "/institute/payouts/sales-reports" },
        { label: "Bank Details", icon: CreditCardIcon, path: "/institute/payouts/bank-details" },
        { label: "Payout History", icon: ClipboardDocumentListIcon, path: "/institute/payouts/history" },
      ],
    },
    { label: "Mentors", icon: UserPlusIcon, path: "/institute/mentors" },
    { label: "Collaborations", icon: UsersIcon, path: "/institute/collaborations" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/institute/settings" },
  ],
  sponsor: [
    { label: "Dashboard", icon: HomeIcon, path: "/sponsor/dashboard" },
    { label: "Institutes", icon: BuildingOfficeIcon, path: "/sponsor/institutes" },
    { label: "Funding", icon: CurrencyDollarIcon, path: "/sponsor/funding" },
    { label: "Reports", icon: ChartBarIcon, path: "/sponsor/reports" },
    { label: "Settings", icon: Cog6ToothIcon, path: "/sponsor/settings" },
  ],
}; 