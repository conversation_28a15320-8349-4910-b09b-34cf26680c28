# 🔄 Event Payment Flow Architecture

## 🎯 Key Principle
**Frontend NEVER calls PayFast directly. Backend handles ALL PayFast integration.**

---

## 📱 Complete Payment Flow

### **Step 1: User Selects Ticket**
```
Frontend: TicketSelection Component
- User selects ticket type and quantity
- Form auto-fills from localStorage userdata
- User clicks "Proceed to Payment"
```

### **Step 2: Frontend → Backend API Call**
```javascript
// Frontend calls backend API
POST ${BASE_URL}/api/buy-ticket
{
  "event_id": "event-123",
  "ticket_id": "ticket-1",
  "quantity": 1,
  "buyer_info": {
    "name": "<PERSON>",
    "email": "<EMAIL>", 
    "phone": "+923001234567"
  }
}
```

### **Step 3: Backend Response**
```javascript
// Backend responds with payment URL
{
  "booking_id": "booking-456",
  "total_amount": 1000,
  "currency": "PKR",
  "payment_required": true,
  "payfast_url": "/api/payment-redirect/booking-456"  // Backend endpoint!
}
```

### **Step 4: Frontend Redirects to Backend**
```javascript
// Frontend redirects to BACKEND payment endpoint
if (booking.payment_required && booking.payfast_url) {
  window.location.href = booking.payfast_url;  // Backend URL
}
```

### **Step 5: Backend → PayFast Integration**
```
Backend handles:
1. Creates PayFast form with merchant credentials
2. Generates signature and security hash
3. Redirects user to PayFast payment gateway
4. Handles PayFast notify_url callbacks
5. Updates booking status in database
```

### **Step 6: PayFast → User → Frontend**
```
PayFast redirects user back to:
- Success: /payment/success?registration_id=booking-456
- Cancel: /payment/cancel
```

### **Step 7: Frontend Status Polling**
```javascript
// Frontend polls backend for booking status
GET ${BASE_URL}/api/booking-status/booking-456

// Polls every 2 seconds until:
// - status: "confirmed" (success)
// - status: "failed" (failed)
// - timeout after 30 attempts
```

---

## 🔧 Backend Responsibilities

### **Payment Endpoint**
```
GET /api/payment-redirect/{booking_id}
- Creates PayFast form with proper credentials
- Generates security signature
- Redirects user to PayFast gateway
```

### **PayFast Notify Handler**
```
POST /api/payments/payfast/notify
- Receives PayFast payment notifications
- Validates payment signature
- Updates booking status in database
- Sends confirmation emails
```

### **Status Endpoint**
```
GET /api/booking-status/{booking_id}
- Returns current booking and payment status
- Includes event details and QR codes
- Used by frontend for status polling
```

---

## 🎯 Frontend Components Updated

### **TicketSelection.jsx**
- ✅ Calls `${BASE_URL}/api/buy-ticket`
- ✅ Redirects to `booking.payfast_url` (backend endpoint)
- ✅ No direct PayFast integration

### **EventPaymentSuccess.jsx**
- ✅ Polls `${BASE_URL}/api/booking-status/{id}`
- ✅ Shows real-time payment confirmation
- ✅ Displays ticket with QR code when confirmed

### **BookingService.js**
- ✅ All API calls use `${BASE_URL}`
- ✅ Handles booking status polling
- ✅ Manages ticket downloads

---

## 🔒 Security Benefits

### **Frontend Security**
- ✅ **No PayFast Credentials**: Frontend never sees merchant keys
- ✅ **No Direct Integration**: Backend handles all PayFast complexity
- ✅ **Secure Redirects**: All payment flows go through backend

### **Backend Security**
- ✅ **Credential Protection**: PayFast keys stored securely on server
- ✅ **Signature Validation**: Validates all PayFast notifications
- ✅ **Audit Trail**: Complete payment tracking in database

---

## 💡 Key Advantages

### **Simplified Frontend**
- ✅ **Clean API Calls**: Simple REST API integration
- ✅ **No Payment Complexity**: Backend handles PayFast details
- ✅ **Better UX**: Seamless redirect flow

### **Secure Backend**
- ✅ **Centralized Payment Logic**: All payment handling in one place
- ✅ **Easy Maintenance**: PayFast updates only affect backend
- ✅ **Compliance Ready**: Secure credential management

### **Scalable Architecture**
- ✅ **Multiple Payment Gateways**: Easy to add other providers
- ✅ **API Consistency**: Same pattern for all payment methods
- ✅ **Mobile Ready**: Works with mobile apps and web

---

## 🚀 Implementation Status

### **✅ Frontend Complete**
- All components call backend APIs with BASE_URL
- Payment flow redirects to backend endpoints
- Status polling works with real API structure

### **⏳ Backend Required**
- Implement `/api/buy-ticket` endpoint
- Create PayFast integration service
- Set up payment redirect and notify handlers
- Implement booking status management

**The frontend is 100% ready for the backend payment integration!** 🎉
