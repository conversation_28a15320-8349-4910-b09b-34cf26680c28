import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import { 
  fetchClasses, 
  createClass, 
  updateClass, 
  deleteClass 
} from '../../store/slices/ClassesSlice';
import { 
  FiPlus, 
  FiEdit3, 
  FiTrash2, 
  FiSearch, 
  FiBook, 
  FiSave, 
  FiX,
  FiLoader
} from 'react-icons/fi';

const Grades = () => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  
  // Define theme classes with fallbacks
  const themeClasses = {
    cardBg: currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white',
    input: currentTheme === 'dark' 
      ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400' 
      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500',
    label: currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
  };
  
  // Redux state
  const { classes = [], loading, error, success } = useSelector(state => state.classes);
  
  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGrade, setEditingGrade] = useState(null);
  const [formData, setFormData] = useState({ ClassNo: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch grades on mount
  useEffect(() => {
    dispatch(fetchClasses());
  }, [dispatch]);

  // Handle success/error messages
  useEffect(() => {
    if (success) {
      setShowCreateForm(false);
      setEditingGrade(null);
      setFormData({ ClassNo: '' });
      setIsSubmitting(false);
    }
  }, [success]);

  // Filter grades based on search term
  const filteredGrades = useMemo(() => {
    if (!searchTerm) return classes;
    return classes.filter(cls => 
      cls.ClassNo.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [classes, searchTerm]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.ClassNo.trim()) return;

    setIsSubmitting(true);
    try {
      if (editingGrade) {
        await dispatch(updateClass({ 
          class_id: editingGrade.id, 
          classData: formData 
        })).unwrap();
      } else {
        await dispatch(createClass(formData)).unwrap();
      }
      dispatch(fetchClasses()); // Refresh the list
    } catch (error) {
      console.error('Error saving grade:', error);
      setIsSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async (gradeId) => {
    // For now, just proceed with deletion - can be enhanced with proper modal later
    try {
      await dispatch(deleteClass(gradeId)).unwrap();
      dispatch(fetchClasses()); // Refresh the list
    } catch (error) {
      console.error('Error deleting grade:', error);
    }
  };

  // Handle edit
  const handleEdit = (grade) => {
    setEditingGrade(grade);
    setFormData({ ClassNo: grade.ClassNo });
    setShowCreateForm(true);
  };

  // Reset form
  const resetForm = () => {
    setShowCreateForm(false);
    setEditingGrade(null);
    setFormData({ ClassNo: '' });
    setIsSubmitting(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Grades Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage grade levels for the education system
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <FiPlus className="w-4 h-4 mr-2" />
          Add Grade
        </button>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search grades..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${themeClasses.input}`}
        />
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Success Message - Removed for cleaner UI */}

      {/* Create/Edit Form */}
      {showCreateForm && (
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {editingGrade ? 'Edit Grade' : 'Add New Grade'}
            </h3>
            <button
              onClick={resetForm}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <FiX className="w-5 h-5" />
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Grade Level <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.ClassNo}
                onChange={(e) => setFormData({ ClassNo: e.target.value })}
                placeholder="e.g., 10, 11, 12"
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${themeClasses.input}`}
                required
                disabled={isSubmitting}
              />
            </div>
            
            <div className="flex gap-3">
              <button
                type="submit"
                disabled={isSubmitting || !formData.ClassNo.trim()}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isSubmitting ? (
                  <FiLoader className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <FiSave className="w-4 h-4 mr-2" />
                )}
                {editingGrade ? 'Update Grade' : 'Create Grade'}
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Grades List */}
      <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden`}>
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
            <FiBook className="w-5 h-5 mr-2 text-blue-600" />
            Grades ({filteredGrades.length})
          </h3>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <FiLoader className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading grades...</span>
          </div>
        ) : filteredGrades.length === 0 ? (
          <div className="text-center py-12">
            <FiBook className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm ? 'No grades found matching your search.' : 'No grades available. Add your first grade!'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredGrades.map((grade) => (
              <div key={grade.id} className="px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-4">
                    <FiBook className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Grade {grade.ClassNo}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      ID: {grade.id}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEdit(grade)}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors duration-200"
                    title="Edit grade"
                  >
                    <FiEdit3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(grade.id)}
                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors duration-200"
                    title="Delete grade"
                  >
                    <FiTrash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Grades;
