import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

import URL from "../../utils/api/API_URL";

// Subjects API endpoints - with fallback to mock data if needed
const BASE_URL = `${URL}/api/subjects`;

// Get token from localStorage
const getAuthToken = () => localStorage.getItem("token");

// Mock data for development - using proper UUID format
const mockSubjects = {
  subjects: [
    { id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', name: 'Mathematics', description: 'Advanced Mathematics', created_at: new Date().toISOString() },
    { id: 'b2c3d4e5-f6g7-8901-bcde-f23456789012', name: 'Physics', description: 'General Physics', created_at: new Date().toISOString() },
    { id: 'c3d4e5f6-g7h8-9012-cdef-************', name: 'Chemistry', description: 'Organic Chemistry', created_at: new Date().toISOString() },
    { id: 'd4e5f6g7-h8i9-0123-def0-************', name: 'Biology', description: 'Cell Biology', created_at: new Date().toISOString() },
    { id: 'e5f6g7h8-i9j0-1234-ef01-************', name: 'Computer Science', description: 'Programming Fundamentals', created_at: new Date().toISOString() },
    { id: 'f6g7h8i9-j0k1-2345-f012-************', name: 'English Literature', description: 'Literature and Writing', created_at: new Date().toISOString() },
    { id: 'g7h8i9j0-k1l2-3456-0123-************', name: 'History', description: 'World History', created_at: new Date().toISOString() },
    { id: 'h8i9j0k1-l2m3-4567-1234-890123456789', name: 'Geography', description: 'Physical and Human Geography', created_at: new Date().toISOString() },
    { id: 'i9j0k1l2-m3n4-5678-2345-901234567890', name: 'Economics', description: 'Microeconomics and Macroeconomics', created_at: new Date().toISOString() },
    { id: 'j0k1l2m3-n4o5-6789-3456-012345678901', name: 'Psychology', description: 'Cognitive and Behavioral Psychology', created_at: new Date().toISOString() }
  ],
  total: 10
};

// Thunks - API calls with mock fallback

// Fetch all subjects with pagination
export const fetchSubjects = createAsyncThunk(
  "subjects/fetchSubjects",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      // Try to fetch from real API first
      try {
        const res = await axios.get(`${BASE_URL}/?skip=${skip}&limit=${limit}`, {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
        });
        return res.data;
      } catch (apiError) {
        // If API endpoint doesn't exist, fall back to mock data
        console.warn('Subjects API endpoint not available, using mock data:', apiError.message);

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Simulate pagination
        const startIndex = skip;
        const endIndex = Math.min(skip + limit, mockSubjects.subjects.length);
        const paginatedSubjects = mockSubjects.subjects.slice(startIndex, endIndex);

        return {
          subjects: paginatedSubjects,
          total: mockSubjects.total
        };
      }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create new subject
export const createSubject = createAsyncThunk(
  "subjects/createSubject",
  async (subjectData, thunkAPI) => {
    try {
      // Try to use real API first
      try {
        const res = await axios.post(`${BASE_URL}/`, subjectData, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            "Content-Type": "application/json",
          },
        });
        return res.data;
      } catch (apiError) {
        // If API endpoint doesn't exist, fall back to mock implementation
        console.warn('Create subject API endpoint not available, using mock implementation:', apiError.message);

        await new Promise(resolve => setTimeout(resolve, 500));

        // Generate a proper UUID for new subjects
        const generateUUID = () => {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        };

        const newSubject = {
          id: generateUUID(),
          ...subjectData,
          created_at: new Date().toISOString()
        };

        // Add to mock data (for this session only)
        mockSubjects.subjects.push(newSubject);
        mockSubjects.total++;

        return newSubject;
      }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchSubjectById = createAsyncThunk(
  "subjects/fetchSubjectById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update subject
export const updateSubject = createAsyncThunk(
  "subjects/updateSubject",
  async ({ id, subjectData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, subjectData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch subject hierarchy
export const fetchSubjectHierarchy = createAsyncThunk(
  "subjects/fetchSubjectHierarchy",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}/hierarchy`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete subject (handle new response shape)
export const deleteSubject = createAsyncThunk(
  "subjects/deleteSubject",
  async (id, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { id, detail: res.data.detail };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  subjects: [],
  total: 0,
  currentSubject: null,
  hierarchy: null,
  loading: false,
  error: null,
  deleteDetail: null,
};

// Slice
const subjectSlice = createSlice({
  name: "subjects",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch All
      .addCase(fetchSubjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjects.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = action.payload.subjects;
        state.total = action.payload.total;
      })
      .addCase(fetchSubjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubject.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects.push(action.payload);
      })
      .addCase(createSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchSubjectById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubject = action.payload;
      })
      .addCase(fetchSubjectById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubject.fulfilled, (state, action) => {
        state.loading = false;
        // Update the subject in the array
        const idx = state.subjects.findIndex(s => s.id === action.payload.id);
        if (idx !== -1) {
          state.subjects[idx] = action.payload;
        }
        // If currentSubject is the updated one, update it too
        if (state.currentSubject && state.currentSubject.id === action.payload.id) {
          state.currentSubject = action.payload;
        }
      })
      .addCase(updateSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Hierarchy
      .addCase(fetchSubjectHierarchy.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectHierarchy.fulfilled, (state, action) => {
        state.loading = false;
        state.hierarchy = action.payload;
      })
      .addCase(fetchSubjectHierarchy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.deleteDetail = null;
      })
      .addCase(deleteSubject.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = state.subjects.filter(
          (subj) => subj.id !== action.payload.id
        );
        state.deleteDetail = action.payload.detail;
      })
      .addCase(deleteSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default subjectSlice.reducer;
