/**
 * MVP Payment Cancel Page
 * 
 * Simple page shown when user cancels payment on PayFast or payment fails.
 * No sensitive logic here - just display cancellation message and options.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  FiX,
  FiArrowLeft,
  FiRefreshCw,
  FiHome,
  FiCalendar,
  FiCreditCard,
  FiInfo
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import mvpPaymentService from '../../services/mvpPaymentService';

const MVPPaymentCancel = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showInfo, showError } = useNotification();
  
  // State
  const [paymentParams, setPaymentParams] = useState({});
  const [ticketInfo, setTicketInfo] = useState(null);

  // Extract parameters from URL
  useEffect(() => {
    const params = mvpPaymentService.parseReturnParams();
    setPaymentParams(params);
    
    // Log the cancellation
    if (params.ticket_id) {
      mvpPaymentService.logPaymentEvent(params.ticket_id, 'cancel', params);
    }
    
    showInfo('Payment was cancelled. Your card was not charged.');
  }, []);

  /**
   * Try payment again
   */
  const tryAgain = () => {
    if (paymentParams.ticket_id) {
      // Go back to the event page to try payment again
      navigate(`/events/${paymentParams.event_id || 'browse'}`, {
        state: { selectedTicketId: paymentParams.ticket_id }
      });
    } else {
      navigate('/events');
    }
  };

  /**
   * Navigate to events
   */
  const goToEvents = () => {
    navigate('/events');
  };

  /**
   * Navigate home
   */
  const goHome = () => {
    navigate('/');
  };

  /**
   * Go back to previous page
   */
  const goBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Cancel Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiX className="w-10 h-10 text-orange-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Cancelled
          </h1>
          <p className="text-gray-600">
            Your payment was cancelled and your card was not charged.
          </p>
        </div>

        {/* Payment Details */}
        {paymentParams.ticket_id && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Cancelled Payment</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Ticket ID</p>
                <p className="font-medium text-gray-900">{paymentParams.ticket_id}</p>
              </div>
              
              {paymentParams.item_name && (
                <div>
                  <p className="text-gray-600">Item</p>
                  <p className="font-medium text-gray-900">{paymentParams.item_name}</p>
                </div>
              )}
              
              {paymentParams.amount_gross && (
                <div>
                  <p className="text-gray-600">Amount</p>
                  <p className="font-medium text-gray-900">R {paymentParams.amount_gross}</p>
                </div>
              )}
              
              <div>
                <p className="text-gray-600">Status</p>
                <p className="font-medium text-orange-600">Cancelled</p>
              </div>
            </div>
          </div>
        )}

        {/* Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <div className="flex items-start">
            <FiInfo className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-900 mb-2">What happened?</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• You cancelled the payment on the PayFast payment page</p>
                <p>• Your card or bank account was not charged</p>
                <p>• The ticket is still available for purchase</p>
                <p>• You can try the payment again at any time</p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">What would you like to do?</h2>
          
          <div className="space-y-3">
            <button
              onClick={tryAgain}
              className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiCreditCard className="w-5 h-5 mr-2" />
              Try Payment Again
            </button>
            
            <button
              onClick={goBack}
              className="w-full flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FiArrowLeft className="w-5 h-5 mr-2" />
              Go Back to Event
            </button>
            
            <button
              onClick={goToEvents}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FiCalendar className="w-5 h-5 mr-2" />
              Browse Other Events
            </button>
            
            <button
              onClick={goHome}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FiHome className="w-5 h-5 mr-2" />
              Go to Homepage
            </button>
          </div>
        </div>

        {/* Support Info */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            Having trouble with payment? Contact our support team for assistance.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MVPPaymentCancel;
