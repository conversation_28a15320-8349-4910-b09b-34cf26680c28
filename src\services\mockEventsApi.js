// Mock Events API Service
// This provides realistic event data with tickets until the backend API is implemented

export const mockEvents = [
  {
    id: 'event-1',
    title: 'React & Next.js Workshop',
    description: 'Learn modern React development with Next.js, including server-side rendering, API routes, and deployment strategies.',
    short_description: 'Hands-on workshop covering React and Next.js fundamentals',
    banner_image_url: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',
    gallery_images: [
      'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop'
    ],
    start_datetime: '2024-02-15T09:00:00Z',
    end_datetime: '2024-02-15T17:00:00Z',
    registration_start: '2024-01-15T00:00:00Z',
    registration_end: '2024-02-14T23:59:59Z',
    category: 'workshop',
    location: 'Tech Hub, Cape Town',
    status: 'published',
    is_featured: true,
    is_public: true,
    requires_approval: false,
    max_attendees: 50,
    min_attendees: 10,
    current_attendees: 23,
    agenda: [
      { time: '09:00', title: 'Welcome & Setup', description: 'Introduction and environment setup' },
      { time: '10:00', title: 'React Fundamentals', description: 'Components, hooks, and state management' },
      { time: '12:00', title: 'Lunch Break', description: 'Networking and refreshments' },
      { time: '13:00', title: 'Next.js Deep Dive', description: 'SSR, API routes, and deployment' },
      { time: '16:00', title: 'Q&A & Wrap-up', description: 'Questions and next steps' }
    ],
    requirements: 'Basic JavaScript knowledge required. Laptop with Node.js installed.',
    tags: ['react', 'nextjs', 'javascript', 'frontend'],
    external_links: {
      github: 'https://github.com/example/react-workshop',
      slides: 'https://slides.example.com/react-workshop'
    },
    organizer: {
      id: 'org-1',
      name: 'Tech Academy',
      email: '<EMAIL>',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop'
    },
    tickets: [
      {
        id: 'ticket-1',
        name: 'Early Bird',
        description: 'Limited time offer for early registrations',
        price: 199.99,
        currency: 'ZAR',
        max_quantity_per_order: 2,
        available_quantity: 15,
        status: 'ACTIVE'
      },
      {
        id: 'ticket-2',
        name: 'Regular',
        description: 'Standard workshop access',
        price: 299.99,
        currency: 'ZAR',
        max_quantity_per_order: 3,
        available_quantity: 25,
        status: 'ACTIVE'
      },
      {
        id: 'ticket-3',
        name: 'Student',
        description: 'Special pricing for students (ID required)',
        price: 149.99,
        currency: 'ZAR',
        max_quantity_per_order: 1,
        available_quantity: 10,
        status: 'ACTIVE'
      }
    ]
  },
  {
    id: 'event-2',
    title: 'AI & Machine Learning Conference',
    description: 'Join industry experts to explore the latest trends in artificial intelligence and machine learning.',
    short_description: 'Conference on AI and ML innovations',
    banner_image_url: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop',
    start_datetime: '2024-03-20T08:00:00Z',
    end_datetime: '2024-03-21T18:00:00Z',
    registration_start: '2024-02-01T00:00:00Z',
    registration_end: '2024-03-19T23:59:59Z',
    category: 'conference',
    location: 'Convention Center, Johannesburg',
    status: 'published',
    is_featured: true,
    is_public: true,
    requires_approval: false,
    max_attendees: 200,
    min_attendees: 50,
    current_attendees: 87,
    agenda: [
      { time: '08:00', title: 'Registration & Coffee', description: 'Welcome and networking' },
      { time: '09:00', title: 'Keynote: Future of AI', description: 'Opening keynote presentation' },
      { time: '10:30', title: 'ML in Production', description: 'Panel discussion on deployment strategies' },
      { time: '14:00', title: 'Hands-on Workshops', description: 'Practical AI/ML sessions' },
      { time: '17:00', title: 'Networking Reception', description: 'Closing networking event' }
    ],
    requirements: 'No prerequisites required. All skill levels welcome.',
    tags: ['ai', 'machine-learning', 'data-science', 'conference'],
    tickets: [
      {
        id: 'ticket-4',
        name: 'General Admission',
        description: 'Full conference access',
        price: 0,
        currency: 'ZAR',
        max_quantity_per_order: 5,
        available_quantity: 100,
        status: 'ACTIVE'
      },
      {
        id: 'ticket-5',
        name: 'VIP Pass',
        description: 'Premium access with exclusive networking sessions',
        price: 599.99,
        currency: 'ZAR',
        max_quantity_per_order: 2,
        available_quantity: 20,
        status: 'ACTIVE'
      }
    ]
  },
  {
    id: 'event-3',
    title: 'Startup Pitch Competition',
    description: 'Showcase your startup idea to investors and industry experts. Win prizes and funding opportunities.',
    short_description: 'Pitch your startup to investors',
    banner_image_url: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=800&h=400&fit=crop',
    start_datetime: '2024-04-10T18:00:00Z',
    end_datetime: '2024-04-10T22:00:00Z',
    registration_start: '2024-03-01T00:00:00Z',
    registration_end: '2024-04-08T23:59:59Z',
    category: 'competition',
    location: 'Innovation Hub, Pretoria',
    status: 'published',
    is_featured: false,
    is_public: true,
    requires_approval: true,
    max_attendees: 30,
    min_attendees: 5,
    current_attendees: 12,
    agenda: [
      { time: '18:00', title: 'Welcome & Networking', description: 'Meet fellow entrepreneurs' },
      { time: '18:30', title: 'Pitch Presentations', description: '5-minute pitches from startups' },
      { time: '20:30', title: 'Judging & Deliberation', description: 'Panel evaluates presentations' },
      { time: '21:30', title: 'Awards & Closing', description: 'Winner announcement and networking' }
    ],
    requirements: 'Must have a startup idea or early-stage company. Pitch deck required.',
    tags: ['startup', 'entrepreneurship', 'pitch', 'competition'],
    prize_details: {
      first_place: 'R50,000 funding + mentorship',
      second_place: 'R25,000 funding',
      third_place: 'R10,000 funding'
    },
    tickets: [
      {
        id: 'ticket-6',
        name: 'Participant',
        description: 'For startups presenting their pitch',
        price: 0,
        currency: 'ZAR',
        max_quantity_per_order: 1,
        available_quantity: 15,
        status: 'ACTIVE'
      },
      {
        id: 'ticket-7',
        name: 'Audience',
        description: 'Watch the pitches and network',
        price: 50.00,
        currency: 'ZAR',
        max_quantity_per_order: 3,
        available_quantity: 15,
        status: 'ACTIVE'
      }
    ]
  }
];

// Mock API functions
export const mockEventsApi = {
  // Get all events with filtering
  getEvents: async (params = {}) => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
    
    let filteredEvents = [...mockEvents];
    
    // Apply filters
    if (params.category) {
      filteredEvents = filteredEvents.filter(event => event.category === params.category);
    }
    
    if (params.is_featured) {
      filteredEvents = filteredEvents.filter(event => event.is_featured);
    }
    
    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      filteredEvents = filteredEvents.filter(event => 
        event.title.toLowerCase().includes(searchTerm) ||
        event.description.toLowerCase().includes(searchTerm) ||
        event.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }
    
    // Pagination
    const page = parseInt(params.page) || 1;
    const limit = parseInt(params.limit) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    const paginatedEvents = filteredEvents.slice(startIndex, endIndex);
    
    return {
      events: paginatedEvents,
      total: filteredEvents.length,
      page,
      limit,
      has_next: endIndex < filteredEvents.length,
      has_prev: page > 1
    };
  },

  // Get single event by ID
  getEvent: async (eventId) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const event = mockEvents.find(e => e.id === eventId);
    if (!event) {
      throw new Error('Event not found');
    }
    
    return event;
  },

  // Alias for getEvents to match EventsSlice expectations
  fetchPublicEvents: async (params = {}) => {
    return mockEventsApi.getEvents(params);
  },

  // Register for event (follows correct flow: register first, pay later)
  registerForEvent: async (eventId, registrationData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const event = mockEvents.find(e => e.id === eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    // Find the selected ticket
    const selectedTicket = registrationData.ticket_id ?
      event.tickets.find(t => t.id === registrationData.ticket_id) :
      event.tickets[0]; // Default to first ticket

    const isFreeTicket = selectedTicket && selectedTicket.price === 0;
    const totalAmount = selectedTicket ? selectedTicket.price * (registrationData.quantity || 1) : 0;

    // Mock registration response following correct flow
    return {
      success: true,
      registration_id: `reg-${Date.now()}`,
      registration_number: `REG-2024-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      event_id: eventId,
      status: isFreeTicket ? 'CONFIRMED' : 'PENDING_PAYMENT',
      payment_status: isFreeTicket ? 'COMPLETED' : 'PENDING',
      event: {
        id: eventId,
        title: event.title,
        start_datetime: event.start_datetime,
        location: event.location
      },
      ticket: {
        id: selectedTicket?.id,
        name: selectedTicket?.name || 'General Admission',
        price: selectedTicket?.price || 0,
        quantity: registrationData.quantity || 1,
        total_amount: totalAmount,
        currency: 'ZAR'
      },
      attendee: {
        name: registrationData.attendee_info?.name || registrationData.name || 'Mock User',
        email: registrationData.attendee_info?.email || registrationData.email || '<EMAIL>'
      },
      qr_code: isFreeTicket ? 'mock_qr_code_base64' : null,
      check_in_code: isFreeTicket ? `CHK-${Math.floor(Math.random() * 100000)}` : null,
      registered_at: new Date().toISOString(),
      message: isFreeTicket ? 'Registration confirmed successfully' : 'Registration created - payment required to complete'
    };
  }
};

export default mockEventsApi;
