import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>ser,
  <PERSON><PERSON>lock,
  FiFileText,
  FiStar,
  FiMessageSquare,
  FiSave,
  FiEye,
  FiChevronLeft,
  FiChevronRight
} from 'react-icons/fi';
import {
  getSubmissionsToEvaluate,
  getSubmissionDetails,
  submitMentorEvaluation
} from '../../services/competitionService';
import { LoadingSpinner, ErrorMessage } from '../ui';

const MentorEvaluation = ({ competitionId, mentorId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submissions, setSubmissions] = useState([]);
  const [currentSubmissionIndex, setCurrentSubmissionIndex] = useState(0);
  const [currentSubmission, setCurrentSubmission] = useState(null);
  const [evaluationData, setEvaluationData] = useState({
    technical_score: 0,
    creativity_score: 0,
    presentation_score: 0,
    overall_score: 0,
    feedback: '',
    strengths: '',
    improvements: '',
    mentor_notes: ''
  });
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (competitionId) {
      loadSubmissions();
    }
  }, [competitionId]);

  useEffect(() => {
    if (submissions.length > 0 && currentSubmissionIndex >= 0) {
      loadSubmissionDetails(submissions[currentSubmissionIndex].attempt_id);
    }
  }, [submissions, currentSubmissionIndex]);

  const loadSubmissions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getSubmissionsToEvaluate(competitionId, {
        mentor_id: mentorId,
        status: 'pending_evaluation'
      });
      
      setSubmissions(data.submissions || []);
      
      if (data.submissions && data.submissions.length > 0) {
        setCurrentSubmissionIndex(0);
      }
      
    } catch (err) {
      console.error('Error loading submissions:', err);
      setError('Failed to load submissions for evaluation');
    } finally {
      setLoading(false);
    }
  };

  const loadSubmissionDetails = async (attemptId) => {
    try {
      const details = await getSubmissionDetails(attemptId);
      setCurrentSubmission(details);
      
      // Reset evaluation form
      setEvaluationData({
        technical_score: details.evaluation?.technical_score || 0,
        creativity_score: details.evaluation?.creativity_score || 0,
        presentation_score: details.evaluation?.presentation_score || 0,
        overall_score: details.evaluation?.overall_score || 0,
        feedback: details.evaluation?.feedback || '',
        strengths: details.evaluation?.strengths || '',
        improvements: details.evaluation?.improvements || '',
        mentor_notes: details.evaluation?.mentor_notes || ''
      });
      
    } catch (err) {
      console.error('Error loading submission details:', err);
      setError('Failed to load submission details');
    }
  };

  const handleScoreChange = (field, value) => {
    const numValue = Math.max(0, Math.min(100, parseInt(value) || 0));
    setEvaluationData(prev => {
      const updated = { ...prev, [field]: numValue };
      
      // Auto-calculate overall score as average
      if (['technical_score', 'creativity_score', 'presentation_score'].includes(field)) {
        const avg = Math.round(
          (updated.technical_score + updated.creativity_score + updated.presentation_score) / 3
        );
        updated.overall_score = avg;
      }
      
      return updated;
    });
  };

  const handleTextChange = (field, value) => {
    setEvaluationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmitEvaluation = async () => {
    if (!currentSubmission) return;
    
    try {
      setSubmitting(true);
      
      await submitMentorEvaluation(currentSubmission.attempt_id, {
        ...evaluationData,
        evaluation_date: new Date().toISOString(),
        mentor_id: mentorId
      });
      
      // Move to next submission or reload list
      if (currentSubmissionIndex < submissions.length - 1) {
        setCurrentSubmissionIndex(prev => prev + 1);
      } else {
        await loadSubmissions();
      }
      
    } catch (err) {
      console.error('Error submitting evaluation:', err);
      setError('Failed to submit evaluation');
    } finally {
      setSubmitting(false);
    }
  };

  const navigateSubmission = (direction) => {
    if (direction === 'prev' && currentSubmissionIndex > 0) {
      setCurrentSubmissionIndex(prev => prev - 1);
    } else if (direction === 'next' && currentSubmissionIndex < submissions.length - 1) {
      setCurrentSubmissionIndex(prev => prev + 1);
    }
  };

  const ScoreInput = ({ label, field, value, description }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label} <span className="text-gray-500">({value}/100)</span>
      </label>
      <input
        type="range"
        min="0"
        max="100"
        value={value}
        onChange={(e) => handleScoreChange(field, e.target.value)}
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
      />
      <div className="flex justify-between text-xs text-gray-500">
        <span>0</span>
        <span>50</span>
        <span>100</span>
      </div>
      {description && (
        <p className="text-xs text-gray-600">{description}</p>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (submissions.length === 0) {
    return (
      <div className="text-center py-12">
        <FiFileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Submissions to Evaluate</h3>
        <p className="text-gray-600">All submissions have been evaluated or none are available.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Navigation */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Mentor Evaluation</h2>
          <p className="text-sm text-gray-600 mt-1">
            Submission {currentSubmissionIndex + 1} of {submissions.length}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigateSubmission('prev')}
            disabled={currentSubmissionIndex === 0}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FiChevronLeft className="h-4 w-4" />
          </button>
          <button
            onClick={() => navigateSubmission('next')}
            disabled={currentSubmissionIndex === submissions.length - 1}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FiChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>

      {currentSubmission && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Submission Details */}
          <div className="space-y-6">
            {/* Student Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Student Information</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <FiUser className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900">{currentSubmission.student_name}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <FiClock className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">
                    Submitted: {new Date(currentSubmission.submitted_at).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <FiFileText className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">
                    Time Taken: {currentSubmission.time_taken_minutes} minutes
                  </span>
                </div>
              </div>
            </div>

            {/* Submission Content */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Submission Content</h3>
              
              {/* Answers */}
              {currentSubmission.answers && (
                <div className="space-y-4">
                  {currentSubmission.answers.map((answer, index) => (
                    <div key={index} className="border-l-4 border-blue-200 pl-4">
                      <h4 className="font-medium text-gray-900 mb-2">
                        Question {index + 1}
                      </h4>
                      <p className="text-sm text-gray-600 mb-2">
                        {answer.question_text}
                      </p>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-gray-900">{answer.answer_text}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Attachments */}
              {currentSubmission.attachments && currentSubmission.attachments.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-900 mb-3">Attachments</h4>
                  <div className="space-y-2">
                    {currentSubmission.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md">
                        <FiFileText className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-900">{attachment.filename}</span>
                        <button className="ml-auto text-blue-600 hover:text-blue-800">
                          <FiEye className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Evaluation Form */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Evaluation Scores</h3>
              
              <div className="space-y-6">
                <ScoreInput
                  label="Technical Skills"
                  field="technical_score"
                  value={evaluationData.technical_score}
                  description="Code quality, problem-solving approach, technical accuracy"
                />
                
                <ScoreInput
                  label="Creativity & Innovation"
                  field="creativity_score"
                  value={evaluationData.creativity_score}
                  description="Original thinking, creative solutions, innovative approaches"
                />
                
                <ScoreInput
                  label="Presentation & Communication"
                  field="presentation_score"
                  value={evaluationData.presentation_score}
                  description="Clarity of explanation, documentation, communication skills"
                />
                
                <div className="pt-4 border-t border-gray-200">
                  <ScoreInput
                    label="Overall Score"
                    field="overall_score"
                    value={evaluationData.overall_score}
                    description="Automatically calculated as average of above scores"
                  />
                </div>
              </div>
            </div>

            {/* Feedback Form */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Detailed Feedback</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    General Feedback
                  </label>
                  <textarea
                    value={evaluationData.feedback}
                    onChange={(e) => handleTextChange('feedback', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Provide overall feedback on the submission..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Strengths
                  </label>
                  <textarea
                    value={evaluationData.strengths}
                    onChange={(e) => handleTextChange('strengths', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="What did the student do well?"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Areas for Improvement
                  </label>
                  <textarea
                    value={evaluationData.improvements}
                    onChange={(e) => handleTextChange('improvements', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="What could be improved?"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mentor Notes (Private)
                  </label>
                  <textarea
                    value={evaluationData.mentor_notes}
                    onChange={(e) => handleTextChange('mentor_notes', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Private notes for other mentors/organizers..."
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end pt-6 border-t border-gray-200 mt-6">
                <button
                  onClick={handleSubmitEvaluation}
                  disabled={submitting || evaluationData.overall_score === 0}
                  className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting && <LoadingSpinner size="sm" className="mr-2" />}
                  <FiSave className="h-4 w-4 mr-2" />
                  {submitting ? 'Submitting...' : 'Submit Evaluation'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorEvaluation;
