/**
 * New Event Registration & Ticket Purchasing Service
 * 
 * Handles all event-related API calls including:
 * - Event listing and details
 * - Event registration (free events)
 * - Ticket purchasing (paid events)
 * - Registration management
 * - PayFast payment integration
 * 
 * Designed to work with the new backend API structure.
 */

import axios from 'axios';
import { BASE_API } from '../utils/api/API_URL';
import { getAuthToken } from '../utils/helpers/authHelpers';
import logger from '../utils/helpers/logger';

class NewEventService {
  constructor() {
    this.baseUrl = BASE_API;
    this.apiPrefix = '/api/events';
  }

  /**
   * Get auth headers for API requests
   */
  getAuthHeaders() {
    const token = getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }



  // ==================== EVENT LISTING & DETAILS ====================

  /**
   * Get all public events with filtering and pagination
   */
  async getEvents(params = {}) {
    try {

      logger.info('Fetching events with params:', params);
      
      const response = await axios.get(`${this.baseUrl}${this.apiPrefix}`, {
        params: {
          category: params.category,
          status: params.status,
          featured: params.featured,
          upcoming: params.upcoming,
          search: params.search,
          page: params.page || 1,
          limit: params.limit || 20
        }
      });
      
      logger.info('Events fetched successfully:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to fetch events:', error);
      
      if (error.response?.status === 404) {
        // Backend endpoints not implemented, use mock data
        return this.getMockEvents(params);
      }
      
      throw this.handleApiError(error);
    }
  }

  /**
   * Get event details by ID
   */
  async getEventDetails(eventId) {
    try {

      logger.info('Fetching event details:', eventId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${eventId}`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Event details fetched successfully:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to fetch event details:', error);
      
      if (error.response?.status === 404) {
        return this.getMockEventDetails(eventId);
      }
      
      throw this.handleApiError(error);
    }
  }

  // ==================== EVENT REGISTRATION ====================

  /**
   * Register for event (both free and paid - payment happens later)
   * This creates a registration record regardless of ticket price
   */
  async registerForEvent(eventId, registrationData) {
    try {
      logger.info('Registering for event:', { eventId, registrationData });

      // Use the correct backend endpoint: POST /api/events/registrations/
      const payload = {
        event_id: eventId,
        ticket_id: registrationData.ticket_id,
        quantity: registrationData.quantity || 1,
        attendee_info: registrationData.attendee_info || {},
        special_requirements: registrationData.special_requirements || '',
        emergency_contact: registrationData.emergency_contact || {}
      };

      const response = await axios.post(
        `${this.baseUrl}/api/events/registrations/`,
        payload,
        { headers: this.getAuthHeaders() }
      );

      logger.info('Event registration successful:', response.data);
      return {
        success: true,
        registration_id: response.data.id,
        registration_number: response.data.registration_number,
        status: response.data.status,
        payment_status: response.data.payment_status,
        event_id: response.data.event_id,
        ticket_id: response.data.ticket_id,
        total_amount: response.data.total_amount,
        currency: response.data.currency,
        registered_at: response.data.registered_at,
        message: response.data.status === 'PENDING' ?
          'Registration created - payment required to complete' :
          'Registration confirmed successfully'
      };

    } catch (error) {
      logger.error('Failed to register for event:', error);

      if (error.response?.status === 404) {
        logger.warn('Event registration endpoint not found (404), using mock registration');
        return this.getMockRegistration(eventId, registrationData);
      }

      // For other errors, still try mock as fallback
      logger.warn('Event registration failed, falling back to mock implementation');
      return this.getMockRegistration(eventId, registrationData);
    }
  }

  /**
   * Pay for an existing registration
   * This is called from the registrations page for unpaid tickets
   */
  async payForRegistration(registrationId) {
    try {

      logger.info('Creating payment for registration:', registrationId);

      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/registrations/${registrationId}/pay`,
        {},
        { headers: this.getAuthHeaders() }
      );

      logger.info('Payment link created:', response.data);
      console.log('Backend payment response:', response.data);

      // If we have payment_data, it means we need to submit a form to PayFast
      if (response.data.payment_data && response.data.payment_url) {
        console.log('Payment data found, redirecting to PayFast...');

        // Add a small delay to ensure UI updates are complete
        setTimeout(() => {
          console.log('Executing PayFast redirect...');
          this.redirectToPayFast(response.data);
        }, 500);

        return response.data;
      } else {
        console.warn('No payment_data or payment_url found in response:', response.data);

        // If we have a simple payment_url, try direct redirect
        if (response.data.payment_url) {
          console.log('Found simple payment_url, doing direct redirect...');
          window.location.href = response.data.payment_url;
        }
      }

      return response.data;

    } catch (error) {
      logger.error('Failed to create payment:', error);
      console.error('Payment creation error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        registrationId
      });

      if (error.response?.status === 404) {
        console.log('Payment endpoint not found, using mock payment');
        return this.getMockPayment(registrationId);
      }

      // For other errors, still try mock as fallback
      if (error.response?.status >= 400) {
        console.warn(`Payment API error (${error.response.status}), falling back to mock`);
        return this.getMockPayment(registrationId);
      }

      throw this.handleApiError(error);
    }
  }

  /**
   * Redirect to PayFast payment using form submission
   * @param {Object} paymentData - PayFast payment response from backend
   */
  redirectToPayFast(paymentData) {
    try {
      logger.info('Redirecting to PayFast with form submission');
      console.log('PayFast payment data:', paymentData);

      // Validate payment data structure
      if (!paymentData.payment_url) {
        throw new Error('Missing payment_url in payment data');
      }

      if (!paymentData.payment_data) {
        throw new Error('Missing payment_data in payment data');
      }

      const form = document.createElement('form');
      form.method = 'POST';
      form.action = paymentData.payment_url;
      form.style.display = 'none';
      form.target = '_self'; // Ensure it opens in same window

      console.log('Form action URL:', paymentData.payment_url);
      console.log('Payment form data:', paymentData.payment_data);

      // Add all payment fields as hidden inputs
      Object.keys(paymentData.payment_data).forEach(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = paymentData.payment_data[key];
        form.appendChild(input);
        console.log(`Added form field: ${key} = ${paymentData.payment_data[key]}`);
      });

      document.body.appendChild(form);

      console.log('Form HTML:', form.outerHTML);
      console.log('Submitting form to PayFast...');

      // FORCE MANUAL FORM FOR DEBUGGING
      console.log('🔧 DEBUG MODE: Creating manual form instead of auto-submit');
      this.createManualPayFastForm(paymentData);
      return; // Skip auto-submit for now

      // Try immediate submission first
      try {
        form.submit();
        console.log('Form submitted successfully');
      } catch (submitError) {
        console.error('Form submission failed:', submitError);

        // Fallback: Try manual submission with a small delay
        setTimeout(() => {
          try {
            console.log('Retrying form submission...');
            form.submit();
          } catch (retryError) {
            console.error('Retry submission also failed:', retryError);
            // Last resort: create manual form for user to submit
            console.log('Creating manual form as fallback...');
            this.createManualPayFastForm(paymentData);
          }
        }, 100);
      }

      // Clean up after a delay
      setTimeout(() => {
        if (document.body.contains(form)) {
          document.body.removeChild(form);
        }
      }, 2000);

    } catch (error) {
      logger.error('Failed to redirect to PayFast:', error);
      console.error('PayFast redirect error:', error);
      console.error('Payment data received:', paymentData);
      throw new Error('Failed to redirect to payment gateway: ' + error.message);
    }
  }

  /**
   * Manual PayFast redirect (for debugging)
   * Creates a visible form that user can submit manually
   */
  createManualPayFastForm(paymentData) {
    try {
      console.log('Creating manual PayFast form for debugging');

      // Remove any existing forms
      const existingForms = document.querySelectorAll('form[data-payfast-debug]');
      existingForms.forEach(form => form.remove());

      const form = document.createElement('form');
      form.method = 'POST';
      form.action = paymentData.payment_url;
      form.setAttribute('data-payfast-debug', 'true');
      form.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border: 2px solid #333;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      `;

      // Add title
      const title = document.createElement('h3');
      title.textContent = '🔧 DEBUG: PayFast Payment Form';
      title.style.cssText = 'margin: 0 0 15px 0; color: #333; font-size: 18px;';
      form.appendChild(title);

      // Add debug info
      const debugInfo = document.createElement('div');
      debugInfo.innerHTML = `
        <p style="margin: 5px 0; font-size: 14px; color: #666;">
          <strong>Action URL:</strong> ${paymentData.payment_url}<br>
          <strong>Method:</strong> POST<br>
          <strong>Fields:</strong> ${Object.keys(paymentData.payment_data).length} form fields
        </p>
      `;
      form.appendChild(debugInfo);

      // Add all payment fields as hidden inputs
      Object.keys(paymentData.payment_data).forEach(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = paymentData.payment_data[key];
        form.appendChild(input);
      });

      // Add visible submit button
      const submitBtn = document.createElement('button');
      submitBtn.type = 'submit';
      submitBtn.textContent = 'Continue to PayFast';
      submitBtn.style.cssText = `
        background: #007cba;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin-right: 10px;
      `;

      // Add cancel button
      const cancelBtn = document.createElement('button');
      cancelBtn.type = 'button';
      cancelBtn.textContent = 'Cancel';
      cancelBtn.style.cssText = `
        background: #666;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
      `;
      cancelBtn.onclick = () => form.remove();

      form.appendChild(submitBtn);
      form.appendChild(cancelBtn);

      document.body.appendChild(form);

      console.log('Manual form created and displayed');
      return form;

    } catch (error) {
      console.error('Failed to create manual PayFast form:', error);
      throw error;
    }
  }

  // ==================== REGISTRATION MANAGEMENT ====================

  /**
   * Get user's event registrations
   */
  async getMyRegistrations(params = {}) {
    try {

      logger.info('Fetching user registrations:', params);
      
      const response = await axios.get(
        `${this.baseUrl}/api/events/registrations/my-registrations`,
        {
          params: {
            status: params.status,
            upcoming: params.upcoming,
            page: params.page || 1,
            limit: params.limit || 20
          },
          headers: this.getAuthHeaders()
        }
      );
      
      logger.info('User registrations fetched:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to fetch registrations:', error);
      
      if (error.response?.status === 404) {
        return this.getMockRegistrations(params);
      }
      
      throw this.handleApiError(error);
    }
  }

  /**
   * Get registration details
   */
  async getRegistrationDetails(registrationId) {
    try {

      logger.info('Fetching registration details:', registrationId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/registrations/${registrationId}`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Registration details fetched:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to fetch registration details:', error);
      
      if (error.response?.status === 404) {
        return this.getMockRegistrationDetails(registrationId);
      }
      
      throw this.handleApiError(error);
    }
  }

  /**
   * Cancel registration
   */
  async cancelRegistration(registrationId) {
    try {

      logger.info('Cancelling registration:', registrationId);
      
      const response = await axios.delete(
        `${this.baseUrl}${this.apiPrefix}/registrations/${registrationId}`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Registration cancelled successfully:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to cancel registration:', error);
      
      if (error.response?.status === 404) {
        return this.getMockCancellation(registrationId);
      }
      
      throw this.handleApiError(error);
    }
  }

  // ==================== EVENT CREATION (Organizers) ====================

  /**
   * Create new event
   */
  async createEvent(eventData) {
    try {

      logger.info('Creating new event:', eventData);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}`,
        eventData,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Event created successfully:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to create event:', error);
      
      if (error.response?.status === 404) {
        return this.getMockEventCreation(eventData);
      }
      
      throw this.handleApiError(error);
    }
  }

  /**
   * Update event
   */
  async updateEvent(eventId, eventData) {
    try {

      logger.info('Updating event:', { eventId, eventData });
      
      const response = await axios.put(
        `${this.baseUrl}${this.apiPrefix}/${eventId}`,
        eventData,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Event updated successfully:', response.data);
      return response.data;
      
    } catch (error) {
      logger.error('Failed to update event:', error);
      
      if (error.response?.status === 404) {
        return this.getMockEventUpdate(eventId, eventData);
      }
      
      throw this.handleApiError(error);
    }
  }

  // ==================== ERROR HANDLING ====================

  /**
   * Handle API errors with proper error messages
   */
  handleApiError(error) {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          throw new Error(data.message || 'Invalid request data');
        case 401:
          throw new Error('Please log in to continue');
        case 403:
          throw new Error('You are not authorized to perform this action');
        case 404:
          throw new Error('Event not found or no longer available');
        case 409:
          throw new Error(data.message || 'Conflict - you may already be registered');
        case 422:
          throw new Error(data.message || 'Validation error');
        case 500:
          throw new Error('Server error - please try again later');
        default:
          throw new Error(data.message || 'An unexpected error occurred');
      }
    } else if (error.request) {
      throw new Error('Network error - please check your connection');
    } else {
      throw new Error('An unexpected error occurred');
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get user info for registration context
   */
  getUserInfo() {
    try {
      const userdata = localStorage.getItem('userdata');
      return userdata ? JSON.parse(userdata) : null;
    } catch (error) {
      logger.error('Failed to get user info:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    const token = getAuthToken();
    return !!token;
  }

  /**
   * Log event action for analytics/debugging
   */
  logEventAction(action, data = {}) {
    try {
      const user = this.getUserInfo();
      const logData = {
        action,
        user_id: user?.id || 'anonymous',
        timestamp: new Date().toISOString(),
        ...data
      };

      logger.info('Event action:', logData);

      // Could send to analytics service here
      // analytics.track('event_action', logData);

    } catch (error) {
      logger.error('Failed to log event action:', error);
    }
  }

  // ==================== MOCK IMPLEMENTATIONS ====================

  /**
   * Mock events data for testing when backend is not available
   */
  getMockEvents(params = {}) {
    logger.warn('Using mock events data - backend not available');

    const mockEvents = [
      {
        id: 'event-1',
        title: 'Web Development Workshop',
        description: 'Learn modern web development with React and Node.js',
        short_description: 'Hands-on web development workshop',
        category: 'WORKSHOP',
        status: 'PUBLISHED',
        start_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        end_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),
        location: 'Tech Hub, Cape Town',
        banner_image_url: 'https://via.placeholder.com/800x400?text=Web+Development',
        is_featured: true,
        is_public: true,
        max_attendees: 50,
        current_registrations: 25,
        registration_start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        registration_end: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
        organizer: {
          id: 'org-1',
          name: 'Tech Academy',
          email: '<EMAIL>'
        },
        tickets: [
          {
            id: 'ticket-free',
            name: 'Free Entry',
            description: 'Free access to all sessions',
            price: 0.00,
            currency: 'ZAR',
            available_quantity: 30,
            max_per_user: 1,
            status: 'ACTIVE'
          }
        ],
        created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'event-2',
        title: 'AI & Machine Learning Conference',
        description: 'Explore the latest trends in AI and machine learning',
        short_description: 'Premier AI conference with industry experts',
        category: 'CONFERENCE',
        status: 'PUBLISHED',
        start_datetime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        end_datetime: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000).toISOString(),
        location: 'Convention Center, Johannesburg',
        banner_image_url: 'https://via.placeholder.com/800x400?text=AI+Conference',
        is_featured: true,
        is_public: true,
        max_attendees: 200,
        current_registrations: 150,
        registration_start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        registration_end: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
        organizer: {
          id: 'org-2',
          name: 'AI Institute',
          email: '<EMAIL>'
        },
        tickets: [
          {
            id: 'ticket-basic',
            name: 'General Admission',
            description: 'Access to all sessions and networking',
            price: 299.00,
            currency: 'ZAR',
            available_quantity: 100,
            max_per_user: 3,
            status: 'ACTIVE'
          },
          {
            id: 'ticket-vip',
            name: 'VIP Pass',
            description: 'Premium access with exclusive sessions',
            price: 599.00,
            currency: 'ZAR',
            available_quantity: 50,
            max_per_user: 2,
            status: 'ACTIVE'
          }
        ],
        created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    // Apply filters
    let filteredEvents = mockEvents;

    if (params.category) {
      filteredEvents = filteredEvents.filter(event =>
        event.category.toLowerCase() === params.category.toLowerCase()
      );
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      filteredEvents = filteredEvents.filter(event =>
        event.title.toLowerCase().includes(searchTerm) ||
        event.description.toLowerCase().includes(searchTerm)
      );
    }

    if (params.featured) {
      filteredEvents = filteredEvents.filter(event => event.is_featured);
    }

    return {
      events: filteredEvents,
      total: filteredEvents.length,
      page: params.page || 1,
      limit: params.limit || 20,
      has_next: false,
      has_prev: false
    };
  }

  /**
   * Mock event details
   */
  getMockEventDetails(eventId) {
    logger.warn('Using mock event details - backend not available');

    const mockEvent = {
      id: eventId,
      title: 'Web Development Workshop',
      description: 'Learn modern web development with React, Node.js, and best practices. This comprehensive workshop covers frontend and backend development, database integration, and deployment strategies.',
      short_description: 'Hands-on web development workshop',
      category: 'WORKSHOP',
      status: 'PUBLISHED',
      start_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      end_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),
      location: 'Tech Hub, Cape Town',
      banner_image_url: 'https://via.placeholder.com/800x400?text=Web+Development',
      gallery_images: [
        'https://via.placeholder.com/400x300?text=Gallery+1',
        'https://via.placeholder.com/400x300?text=Gallery+2'
      ],
      is_featured: true,
      is_public: true,
      requires_approval: false,
      max_attendees: 50,
      min_attendees: 10,
      current_registrations: 25,
      registration_start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      registration_end: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
      requirements: 'Bring your laptop with Node.js installed',
      agenda: [
        {
          time: '09:00',
          title: 'Registration & Coffee',
          description: 'Welcome and networking'
        },
        {
          time: '09:30',
          title: 'Introduction to React',
          description: 'Modern frontend development'
        },
        {
          time: '12:00',
          title: 'Lunch Break',
          description: 'Networking lunch'
        },
        {
          time: '13:00',
          title: 'Backend with Node.js',
          description: 'Server-side development'
        }
      ],
      organizer: {
        id: 'org-1',
        name: 'Tech Academy',
        email: '<EMAIL>',
        profile_picture: 'https://via.placeholder.com/100x100?text=TA'
      },
      tickets: [
        {
          id: 'ticket-free',
          name: 'Free Entry',
          description: 'Free access to all sessions',
          price: 0.00,
          currency: 'ZAR',
          available_quantity: 30,
          max_per_user: 1,
          status: 'ACTIVE',
          features: ['Access to all sessions', 'Lunch included', 'Certificate of completion']
        },
        {
          id: 'ticket-premium',
          name: 'Premium Access',
          description: 'Premium access with extras',
          price: 99.00,
          currency: 'ZAR',
          available_quantity: 20,
          max_per_user: 1,
          status: 'ACTIVE',
          features: ['All Free features', 'Take-home materials', 'One-on-one mentoring session']
        }
      ],
      user_registration: {
        is_registered: false,
        registration_id: null,
        ticket_name: null,
        quantity: 0,
        status: null
      },
      created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    };

    return mockEvent;
  }

  /**
   * Mock registration (creates registration record regardless of ticket price)
   */
  getMockRegistration(eventId, registrationData) {
    logger.warn('Using mock registration - backend not available');

    const registrationId = `reg-${Date.now()}`;
    const registrationNumber = `REG-2024-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

    // Get ticket details to determine if it's free or paid
    const ticketPrice = registrationData.ticket_id === 'ticket-free' ? 0.00 :
                       registrationData.ticket_id === 'ticket-premium' ? 99.00 : 299.00;
    const quantity = registrationData.quantity || 1;
    const totalAmount = ticketPrice * quantity;
    const isFree = ticketPrice === 0;

    return {
      success: true,
      registration_id: registrationId,
      registration_number: registrationNumber,
      status: isFree ? 'CONFIRMED' : 'PENDING_PAYMENT',
      payment_status: isFree ? 'COMPLETED' : 'PENDING',
      event: {
        id: eventId,
        title: isFree ? 'Free Workshop' : 'Premium Conference',
        start_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        location: 'Mock Location'
      },
      ticket: {
        id: registrationData.ticket_id,
        name: isFree ? 'Free Entry' : (registrationData.ticket_id === 'ticket-premium' ? 'Premium Access' : 'General Admission'),
        price: ticketPrice,
        quantity: quantity,
        total_amount: totalAmount,
        currency: 'ZAR'
      },
      attendee: {
        name: registrationData.attendee_info?.name || 'Mock User',
        email: registrationData.attendee_info?.email || '<EMAIL>'
      },
      qr_code: isFree ? 'mock_qr_code_base64' : null,
      check_in_code: isFree ? `CHK-${Math.floor(Math.random() * 100000)}` : null,
      message: isFree ? 'Registration confirmed successfully' : 'Registration created - payment required to complete'
    };
  }

  /**
   * Mock payment for existing registration
   */
  getMockPayment(registrationId) {
    logger.warn('Using mock payment - backend not available');
    console.log('Creating mock PayFast payment for registration:', registrationId);

    const paymentId = `mock-pay-${Date.now()}`;
    const amount = '100.00';
    const itemName = 'Event Registration - Mock Payment';

    // Create proper PayFast form data structure
    const paymentData = {
      merchant_id: '10000100',
      merchant_key: '46f0cd694581a',
      return_url: `${window.location.origin}/payment/success`,
      cancel_url: `${window.location.origin}/payment/cancel`,
      notify_url: 'https://edufair.duckdns.org/api/payments/payfast/webhook',
      name_first: 'Mock',
      name_last: 'User',
      email_address: '<EMAIL>',
      m_payment_id: paymentId,
      amount: amount,
      item_name: itemName,
      item_description: `Mock payment for registration ${registrationId}`,
      custom_str1: paymentId,
      custom_str2: registrationId,
      custom_str3: 'mock_payment'
    };

    // Generate a mock signature (this won't work with real PayFast, but for testing)
    // In real backend, this would be MD5 hash of parameter string
    paymentData.signature = 'mock_signature_' + Date.now();

    const response = {
      success: true,
      payment_id: paymentId,
      payfast_payment_id: paymentId,
      payment_url: 'https://sandbox.payfast.co.za/eng/process',
      payment_data: paymentData,
      status: 'pending',
      registration_id: registrationId,
      amount: parseFloat(amount),
      currency: 'ZAR',
      expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
      message: 'Mock payment created - this will redirect to PayFast sandbox'
    };

    console.log('Mock payment response:', response);
    return response;
  }

  /**
   * Mock user registrations
   */
  getMockRegistrations(params = {}) {
    logger.warn('Using mock registrations - backend not available');

    const mockRegistrations = [
      {
        registration_id: 'reg-1',
        registration_number: 'REG-2024-001',
        status: 'CONFIRMED',
        registered_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        event: {
          id: 'event-1',
          title: 'Web Development Workshop',
          start_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          location: 'Tech Hub, Cape Town',
          banner_image_url: 'https://via.placeholder.com/400x200?text=Workshop'
        },
        ticket: {
          name: 'Free Entry',
          price: 0.00,
          quantity: 1,
          total_amount: 0.00
        },
        payment: {
          status: 'COMPLETED',
          reference: null,
          amount: 0.00,
          currency: 'ZAR'
        },
        qr_code: 'mock_qr_code_1',
        check_in_code: 'CHK-123456',
        can_cancel: true,
        can_modify: false
      },
      {
        registration_id: 'reg-2',
        registration_number: 'REG-2024-002',
        status: 'PENDING_PAYMENT',
        registered_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        event: {
          id: 'event-2',
          title: 'AI Conference',
          start_datetime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
          location: 'Convention Center, Johannesburg',
          banner_image_url: 'https://via.placeholder.com/400x200?text=AI+Conference'
        },
        ticket: {
          name: 'General Admission',
          price: 299.00,
          quantity: 1,
          total_amount: 299.00
        },
        payment: {
          status: 'PENDING',
          reference: 'PAY-REG-2024-002',
          amount: 299.00,
          currency: 'ZAR'
        },
        qr_code: null,
        check_in_code: null,
        can_cancel: true,
        can_modify: true
      }
    ];

    // Apply filters
    let filteredRegistrations = mockRegistrations;

    if (params.status) {
      filteredRegistrations = filteredRegistrations.filter(reg =>
        reg.status.toLowerCase() === params.status.toLowerCase()
      );
    }

    if (params.upcoming) {
      const now = new Date();
      filteredRegistrations = filteredRegistrations.filter(reg =>
        new Date(reg.event.start_datetime) > now
      );
    }

    return {
      registrations: filteredRegistrations,
      total: filteredRegistrations.length,
      page: params.page || 1,
      limit: params.limit || 20
    };
  }

  /**
   * Mock registration details
   */
  getMockRegistrationDetails(registrationId) {
    logger.warn('Using mock registration details - backend not available');

    return {
      registration_id: registrationId,
      registration_number: 'REG-2024-001',
      status: 'CONFIRMED',
      registered_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      confirmed_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      event: {
        id: 'event-1',
        title: 'Web Development Workshop',
        description: 'Learn modern web development',
        start_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        end_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),
        location: 'Tech Hub, Cape Town',
        requirements: 'Bring your laptop'
      },
      ticket: {
        name: 'Free Entry',
        price: 0.00,
        quantity: 1,
        total_amount: 0.00,
        features: ['Access to all sessions', 'Lunch included', 'Certificate']
      },
      attendee_info: {
        name: 'Mock User',
        email: '<EMAIL>',
        phone: '+27123456789'
      },
      qr_code: 'mock_qr_code_base64',
      check_in_code: 'CHK-123456',
      can_cancel: true,
      can_modify: false
    };
  }

  /**
   * Mock registration cancellation
   */
  getMockCancellation(registrationId) {
    logger.warn('Using mock cancellation - backend not available');

    return {
      success: true,
      registration_id: registrationId,
      status: 'CANCELLED',
      cancelled_at: new Date().toISOString(),
      refund_info: {
        eligible: true,
        amount: 0.00,
        processing_time: '3-5 business days'
      },
      message: 'Registration cancelled successfully'
    };
  }

  /**
   * Mock event creation
   */
  getMockEventCreation(eventData) {
    logger.warn('Using mock event creation - backend not available');

    const eventId = `event-${Date.now()}`;

    return {
      id: eventId,
      ...eventData,
      status: 'DRAFT',
      current_registrations: 0,
      organizer: {
        id: 'mock-organizer',
        name: 'Mock Organizer',
        email: '<EMAIL>'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      message: 'Event created successfully (mock)'
    };
  }

  /**
   * Mock event update
   */
  getMockEventUpdate(eventId, eventData) {
    logger.warn('Using mock event update - backend not available');

    return {
      id: eventId,
      ...eventData,
      updated_at: new Date().toISOString(),
      message: 'Event updated successfully (mock)'
    };
  }
}

// Export singleton instance
const newEventService = new NewEventService();
export default newEventService;

// Export class for testing
export { NewEventService };
