import axios from 'axios';
import { getAuthToken } from '../utils/helpers/authHelpers';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';

class InstituteService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/api/institutes`;
  }

  /**
   * Get public institutes list with filtering and pagination
   * @param {Object} params - Query parameters
   * @param {string} params.search - Search term
   * @param {string} params.institute_type - Filter by institute type
   * @param {string} params.country - Filter by country
   * @param {string} params.state - Filter by state
   * @param {string} params.city - Filter by city
   * @param {boolean} params.verified_only - Show only verified institutes
   * @param {number} params.page - Page number (minimum: 1)
   * @param {number} params.size - Page size (minimum: 1, maximum: 100)
   * @returns {Promise<Object>} Response with institutes list and pagination info
   */
  async getPublicInstitutes(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      // Add parameters if they exist
      if (params.search) queryParams.append('search', params.search);
      if (params.institute_type) queryParams.append('institute_type', params.institute_type);
      if (params.country) queryParams.append('country', params.country);
      if (params.state) queryParams.append('state', params.state);
      if (params.city) queryParams.append('city', params.city);
      if (params.verified_only !== undefined) queryParams.append('verified_only', params.verified_only);
      if (params.page) queryParams.append('page', params.page);
      if (params.size) queryParams.append('size', params.size);

      const headers = {
        'accept': 'application/json'
      };

      // Add auth header if token is available (optional for public endpoints)
      const token = getAuthToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await axios.get(`${this.baseURL}/public?${queryParams.toString()}`, {
        headers
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching public institutes:', error);

      // Return empty result on error to prevent UI crashes
      if (error.response?.status === 404 || error.code === 'ECONNREFUSED') {
        console.warn('Institutes API not available, returning empty results');
        return {
          institutes: [],
          total: 0,
          page: params.page || 1,
          size: params.size || 20,
          has_next: false,
          has_prev: false
        };
      }

      throw error;
    }
  }

  /**
   * Search institutes with debounced search functionality
   * @param {string} searchTerm - Search term
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Response with filtered institutes
   */
  async searchInstitutes(searchTerm, filters = {}) {
    return this.getPublicInstitutes({
      search: searchTerm,
      verified_only: true, // Default to verified institutes only
      page: 1,
      size: 20,
      ...filters
    });
  }

  /**
   * Get institute types for filtering
   * @returns {Array<string>} List of institute types
   */
  getInstituteTypes() {
    return [
      'university',
      'college',
      'school',
      'training_center',
      'research_institute',
      'online_platform',
      'other'
    ];
  }
}

export default new InstituteService();
