/**
 * Booking Service - Handles event booking status and user bookings
 * Based on the MVP API specification
 */

import axios from 'axios';
import { BASE_URL } from '../utils/api/API_URL';

const getAuthToken = () => {
  return localStorage.getItem('token') || localStorage.getItem('auth_token');
};

class BookingService {
  /**
   * Check booking status
   * @param {string} bookingId - The booking ID to check
   * @returns {Promise} Booking status data
   */
  static async checkBookingStatus(bookingId) {
    try {
      const response = await axios.get(`${BASE_URL}/api/booking-status/${bookingId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to check booking status');
    }
  }

  /**
   * Get user's bookings
   * @returns {Promise} User's booking list
   */
  static async getUserBookings() {
    try {
      const response = await axios.get(`${BASE_URL}/api/my-bookings`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user bookings');
    }
  }

  /**
   * Poll booking status until confirmed or failed
   * @param {string} bookingId - The booking ID to poll
   * @param {function} onStatusUpdate - Callback for status updates
   * @param {number} maxAttempts - Maximum polling attempts (default: 30)
   * @param {number} interval - Polling interval in ms (default: 2000)
   * @returns {Promise} Final booking status
   */
  static async pollBookingStatus(bookingId, onStatusUpdate, maxAttempts = 30, interval = 2000) {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const status = await this.checkBookingStatus(bookingId);
          
          // Call status update callback
          if (onStatusUpdate) {
            onStatusUpdate(status);
          }
          
          // Check if booking is in final state
          if (status.status === 'confirmed' || status.status === 'failed' || status.status === 'cancelled') {
            resolve(status);
            return;
          }
          
          // Check if we've reached max attempts
          if (attempts >= maxAttempts) {
            reject(new Error('Booking status check timeout'));
            return;
          }
          
          // Continue polling
          setTimeout(poll, interval);
          
        } catch (error) {
          reject(error);
        }
      };
      
      // Start polling
      poll();
    });
  }

  /**
   * Download ticket PDF
   * @param {string} bookingId - The booking ID
   * @returns {Promise} Blob for PDF download
   */
  static async downloadTicket(bookingId) {
    try {
      const response = await axios.get(`${BASE_URL}/api/download-ticket/${bookingId}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to download ticket');
    }
  }

  /**
   * Cancel booking
   * @param {string} bookingId - The booking ID to cancel
   * @returns {Promise} Cancellation result
   */
  static async cancelBooking(bookingId) {
    try {
      const response = await axios.post(`${BASE_URL}/api/cancel-booking/${bookingId}`, {}, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to cancel booking');
    }
  }
}

export default BookingService;
