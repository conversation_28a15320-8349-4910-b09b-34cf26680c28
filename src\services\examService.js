import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';

/**
 * Exam Service
 * Handles all exam-related API operations for mentors and teachers
 */

const BASE_URL = `${API_BASE_URL}/api/exams`;
const getAuthToken = () => localStorage.getItem("token");

const getAuthHeaders = () => ({
  Authorization: `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * Exam Management APIs
 */

// Get Institute Exams (using same pattern as teacher exams)
export const getInstituteExams = async (instituteId) => {
  try {
    const response = await axios.get(`${BASE_URL}/exams/my-exams`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching institute exams:', error);
    // Return mock data for development
    return {
      exams: [
        {
          id: 'exam-1',
          title: 'JavaScript Fundamentals',
          description: 'Basic JavaScript concepts and syntax',
          questions_count: 20,
          duration_minutes: 60,
          difficulty_level: 'beginner',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
          usage_count: 3,
          last_used: '2024-11-20T14:30:00Z',
          status: 'active',
          tags: ['javascript', 'programming', 'fundamentals'],
          created_by: 'Tech Institute',
          created_by_type: 'institute'
        },
        {
          id: 'exam-2',
          title: 'Advanced React Patterns',
          description: 'Complex React patterns and best practices',
          questions_count: 15,
          duration_minutes: 90,
          difficulty_level: 'advanced',
          created_at: '2024-02-10T09:00:00Z',
          updated_at: '2024-02-12T14:00:00Z',
          usage_count: 1,
          last_used: '2024-10-15T11:00:00Z',
          status: 'active',
          tags: ['react', 'javascript', 'advanced'],
          created_by: 'Tech Institute',
          created_by_type: 'institute'
        },
        {
          id: 'exam-3',
          title: 'Database Design Fundamentals',
          description: 'Relational database design principles and SQL',
          questions_count: 25,
          duration_minutes: 75,
          difficulty_level: 'intermediate',
          created_at: '2024-03-05T14:00:00Z',
          updated_at: '2024-03-05T14:00:00Z',
          usage_count: 0,
          last_used: null,
          status: 'draft',
          tags: ['database', 'sql', 'design'],
          created_by: 'Tech Institute',
          created_by_type: 'institute'
        }
      ]
    };
  }
};

// Get My Exams (for mentors and teachers)
export const getMyExams = async () => {
  try {
    const response = await axios.get(`${BASE_URL}/my-exams`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching my exams:', error);
    // Return mock data for development
    return {
      exams: [
        {
          id: 'exam-1',
          title: 'JavaScript Fundamentals',
          description: 'Basic JavaScript concepts and syntax',
          questions_count: 20,
          duration_minutes: 60,
          difficulty_level: 'beginner',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
          usage_count: 3,
          last_used: '2024-11-20T14:30:00Z',
          status: 'active',
          tags: ['javascript', 'programming', 'fundamentals']
        },
        {
          id: 'exam-2',
          title: 'Advanced React Patterns',
          description: 'Complex React patterns and best practices',
          questions_count: 15,
          duration_minutes: 90,
          difficulty_level: 'advanced',
          created_at: '2024-02-10T09:00:00Z',
          updated_at: '2024-02-12T14:00:00Z',
          usage_count: 1,
          last_used: '2024-10-15T11:00:00Z',
          status: 'active',
          tags: ['react', 'javascript', 'advanced']
        }
      ]
    };
  }
};

// Create New Exam
export const createExam = async (examData) => {
  try {
    const response = await axios.post(`${BASE_URL}/`, examData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error creating exam:', error);
    throw error;
  }
};

// Get Exam Details
export const getExam = async (examId) => {
  try {
    const response = await axios.get(`${BASE_URL}/${examId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching exam:', error);
    throw error;
  }
};

// Update Exam
export const updateExam = async (examId, updateData) => {
  try {
    const response = await axios.put(`${BASE_URL}/${examId}`, updateData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating exam:', error);
    throw error;
  }
};

// Delete Exam
export const deleteExam = async (examId) => {
  try {
    const response = await axios.delete(`${BASE_URL}/${examId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting exam:', error);
    throw error;
  }
};

// Copy Exam
export const copyExam = async (examId, copyData = {}) => {
  try {
    const response = await axios.post(`${BASE_URL}/${examId}/copy`, copyData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error copying exam:', error);
    throw error;
  }
};

// Get Exam Usage Information
export const getExamUsageInfo = async (examId) => {
  try {
    const response = await axios.get(`${BASE_URL}/${examId}/usage-info`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching exam usage info:', error);
    // Return mock data for development
    return {
      exam_id: examId,
      usage_count: 3,
      active_competitions: [
        {
          id: 'comp-1',
          title: 'Programming Challenge 2024',
          status: 'active',
          participants: 45
        }
      ],
      past_competitions: [
        {
          id: 'comp-2',
          title: 'Summer Coding Contest',
          status: 'completed',
          participants: 32
        }
      ],
      can_modify: false,
      recommended_action: 'copy',
      reason: 'Exam is currently being used in active competitions'
    };
  }
};

// Update Exam with Copy (Smart Update)
export const updateExamWithCopy = async (examId, updateData) => {
  try {
    const response = await axios.put(`${BASE_URL}/${examId}/update-with-copy`, updateData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating exam with copy:', error);
    throw error;
  }
};

// Get Available Exams for Competition (Public Library)
export const getAvailableExamsForCompetition = async (filters = {}) => {
  try {
    const queryParams = new URLSearchParams(filters).toString();
    const url = `${BASE_URL}/available-for-competition${queryParams ? `?${queryParams}` : ''}`;
    
    const response = await axios.get(url, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching available exams:', error);
    // Return mock data for development
    return {
      exams: [
        {
          id: 'exam-1',
          title: 'JavaScript Fundamentals',
          description: 'Basic JavaScript concepts and syntax',
          questions_count: 20,
          duration_minutes: 60,
          difficulty_level: 'beginner',
          created_by: 'John Mentor',
          created_at: '2024-01-15T10:00:00Z',
          usage_count: 3,
          last_used: '2024-11-20T14:30:00Z',
          tags: ['javascript', 'programming', 'fundamentals'],
          is_public: true,
          rating: 4.5,
          reviews_count: 12
        },
        {
          id: 'exam-2',
          title: 'Advanced React Patterns',
          description: 'Complex React patterns and best practices',
          questions_count: 15,
          duration_minutes: 90,
          difficulty_level: 'advanced',
          created_by: 'Sarah Teacher',
          created_at: '2024-02-10T09:00:00Z',
          usage_count: 1,
          last_used: '2024-10-15T11:00:00Z',
          tags: ['react', 'javascript', 'advanced'],
          is_public: true,
          rating: 4.8,
          reviews_count: 8
        },
        {
          id: 'exam-3',
          title: 'Data Structures & Algorithms',
          description: 'Comprehensive DSA assessment',
          questions_count: 25,
          duration_minutes: 120,
          difficulty_level: 'intermediate',
          created_by: 'Mike Mentor',
          created_at: '2024-03-05T16:00:00Z',
          usage_count: 5,
          last_used: '2024-12-01T10:00:00Z',
          tags: ['algorithms', 'data-structures', 'computer-science'],
          is_public: true,
          rating: 4.7,
          reviews_count: 15
        }
      ],
      total_count: 3,
      filters_applied: filters
    };
  }
};

// Search Exams
export const searchExams = async (searchQuery, filters = {}) => {
  try {
    const params = {
      q: searchQuery,
      ...filters
    };
    const queryParams = new URLSearchParams(params).toString();
    
    const response = await axios.get(`${BASE_URL}/search?${queryParams}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error searching exams:', error);
    throw error;
  }
};

// Create Institute Exam (using same pattern as teacher exams)
export const createInstituteExam = async (instituteId, examData) => {
  try {
    const response = await axios.post(`${BASE_URL}/exams/`, examData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error creating institute exam:', error);
    throw error;
  }
};

// Update Institute Exam (using same pattern as teacher exams)
export const updateInstituteExam = async (instituteId, examId, updateData) => {
  try {
    const response = await axios.put(`${BASE_URL}/exams/${examId}`, updateData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating institute exam:', error);
    throw error;
  }
};

// Delete Institute Exam (using same pattern as teacher exams)
export const deleteInstituteExam = async (instituteId, examId) => {
  try {
    const response = await axios.delete(`${BASE_URL}/exams/${examId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting institute exam:', error);
    throw error;
  }
};

export default {
  getInstituteExams,
  createInstituteExam,
  updateInstituteExam,
  deleteInstituteExam,
  getMyExams,
  createExam,
  getExam,
  updateExam,
  deleteExam,
  copyExam,
  getExamUsageInfo,
  updateExamWithCopy,
  getAvailableExamsForCompetition,
  searchExams
};
