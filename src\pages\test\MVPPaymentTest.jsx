/**
 * MVP Payment Flow Test Page
 * 
 * Test page to demonstrate the simple MVP payment flow:
 * 1. User selects ticket
 * 2. Frontend sends ticket_id to backend
 * 3. Backend returns PayFast link
 * 4. Frontend redirects to PayFast
 * 5. PayFast redirects back to success/cancel pages
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiArrowLeft,
  FiCreditCard,
  FiCheck,
  FiInfo,
  FiSearch
} from 'react-icons/fi';
import MVPTicketSelection from '../../components/events/MVPTicketSelection';
import TicketStatusChecker from '../../components/payment/TicketStatusChecker';

const MVPPaymentTest = () => {
  const navigate = useNavigate();
  
  // Mock event data for testing
  const mockEvent = {
    id: 'test-event-mvp',
    title: 'MVP Payment Test Event',
    description: 'Testing the simple MVP payment flow',
    date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
    location: 'Test Venue, Test City',
    tickets: [
      {
        id: 'ticket-free',
        name: 'Free Entry',
        description: 'Free ticket for testing',
        price: 0,
        currency: 'ZAR',
        available_quantity: 100
      },
      {
        id: 'ticket-basic',
        name: 'Basic Ticket',
        description: 'Standard entry ticket',
        price: 50,
        currency: 'ZAR',
        available_quantity: 50
      },
      {
        id: 'ticket-premium',
        name: 'Premium Ticket',
        description: 'Premium entry with extras',
        price: 150,
        currency: 'ZAR',
        available_quantity: 20
      }
    ]
  };

  const [activeTab, setActiveTab] = useState('purchase');

  /**
   * Handle successful ticket purchase
   */
  const handlePurchaseSuccess = (data) => {
    console.log('Purchase initiated:', data);
    // In real app, user would be redirected to PayFast
    // and then return to success/cancel pages
  };

  /**
   * Handle purchase error
   */
  const handlePurchaseError = (error) => {
    console.error('Purchase failed:', error);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            MVP Payment Flow Test
          </h1>
          <p className="text-gray-600">
            Test the simple cashier-style payment flow where frontend just passes ticket ID to backend.
          </p>
        </div>

        {/* Backend Status Warning */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
          <div className="flex items-start">
            <FiInfo className="w-5 h-5 text-orange-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h2 className="text-lg font-semibold text-orange-900 mb-2">⚠️ Backend Status</h2>
              <div className="text-sm text-orange-800 space-y-2">
                <p><strong>PayFast endpoints are not yet implemented in the backend.</strong></p>
                <p>The frontend includes a mock implementation for testing. Once the backend implements the required endpoints, the frontend will automatically use the real API.</p>
                <p className="font-medium">Required endpoints: <code>/api/payfast/create-payment</code> and <code>/api/tickets/:id/status</code></p>
              </div>
            </div>
          </div>
        </div>

        {/* Flow Description */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">MVP Payment Flow</h2>
          <div className="space-y-2 text-sm text-blue-800">
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-3">1</span>
              User browses events and tickets (list comes from backend)
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-3">2</span>
              User clicks "Buy Ticket" for a specific ticket ID
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-3">3</span>
              Frontend sends ticket_id to backend /api/payfast/create-payment
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-3">4</span>
              Backend responds with PayFast payment link
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-3">5</span>
              Frontend redirects user to PayFast payment page
            </div>
            <div className="flex items-center">
              <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-3">6</span>
              PayFast redirects back to /payment/success or /payment/cancel
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('purchase')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'purchase'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FiCreditCard className="w-4 h-4 inline mr-2" />
                Ticket Purchase
              </button>
              <button
                onClick={() => setActiveTab('status')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'status'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FiSearch className="w-4 h-4 inline mr-2" />
                Status Check
              </button>
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {activeTab === 'purchase' && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Ticket Purchase</h2>
                <MVPTicketSelection
                  event={mockEvent}
                  onSuccess={handlePurchaseSuccess}
                  onError={handlePurchaseError}
                />
              </div>
            )}

            {activeTab === 'status' && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Status Check</h2>
                <TicketStatusChecker />
              </div>
            )}
          </div>

          {/* Info Panel */}
          <div className="space-y-6">
            {/* API Endpoints */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Required API Endpoints</h3>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-red-50 border border-red-200 rounded">
                  <p className="font-medium text-red-900">❌ Create Payment</p>
                  <p className="text-red-700">POST /api/payfast/create-payment</p>
                  <p className="text-xs text-red-600">Body: {"{ ticket_id: 'ticket-123' }"}</p>
                  <p className="text-xs text-red-600 mt-1">Status: Not implemented</p>
                </div>
                <div className="p-3 bg-red-50 border border-red-200 rounded">
                  <p className="font-medium text-red-900">❌ Check Status</p>
                  <p className="text-red-700">GET /api/tickets/:id/status</p>
                  <p className="text-xs text-red-600">Returns ticket and payment status</p>
                  <p className="text-xs text-red-600 mt-1">Status: Not implemented</p>
                </div>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="font-medium text-yellow-900">🔄 Using Mock Implementation</p>
                  <p className="text-xs text-yellow-700">Frontend automatically detects missing endpoints and uses mock responses for testing</p>
                </div>
              </div>
            </div>

            {/* Return URLs */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Return URLs</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <p className="font-medium text-green-600">Success</p>
                  <p className="text-gray-600 break-all">/payment/success?ticket_id=...</p>
                </div>
                <div>
                  <p className="font-medium text-orange-600">Cancel</p>
                  <p className="text-gray-600 break-all">/payment/cancel?ticket_id=...</p>
                </div>
              </div>
            </div>

            {/* Test Instructions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Instructions</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-start">
                  <FiCheck className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Select a ticket and click "Buy Ticket"</span>
                </div>
                <div className="flex items-start">
                  <FiCheck className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Free tickets will be confirmed immediately</span>
                </div>
                <div className="flex items-start">
                  <FiCheck className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Paid tickets will show mock PayFast URL</span>
                </div>
                <div className="flex items-start">
                  <FiCheck className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Test status checker with any ticket ID</span>
                </div>
                <div className="flex items-start">
                  <FiCheck className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Check browser console for API calls and mock responses</span>
                </div>
                <div className="flex items-start">
                  <FiInfo className="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Currently using mock implementation - will automatically switch to real API when backend is ready</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MVPPaymentTest;
