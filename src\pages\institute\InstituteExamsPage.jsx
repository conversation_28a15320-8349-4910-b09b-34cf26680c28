import React from 'react';
import { useSelector } from 'react-redux';
import InstituteExamsTab from '../../components/exams/InstituteExamsTab';

const InstituteExamsPage = () => {
  const { currentUser } = useSelector((state) => state.users);
  const instituteId = currentUser?.id;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Institute Exams</h1>
            <p className="text-gray-600 mt-1">
              Manage your institute's exam library and create assessments for competitions
            </p>
          </div>
        </div>
      </div>

      {/* Exams Content */}
      <InstituteExamsTab instituteId={instituteId} />
    </div>
  );
};

export default InstituteExamsPage;
