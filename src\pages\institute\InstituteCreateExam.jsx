import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';
import { fetchClasses } from '../../store/slices/ClassesSlice';
import ExamCreationWizard from '../../components/exam/ExamCreationWizard';

const InstituteCreateExam = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { examId } = useParams();
  const isEditing = !!examId;

  useEffect(() => {
    // Load necessary data (same as teacher)
    dispatch(fetchAllOwnClasses());
    dispatch(fetchSubjects());
    dispatch(fetchClasses());
  }, [dispatch]);

  // Override the success navigation to go to institute exams
  const handleSuccess = () => {
    navigate('/institute/exams');
  };

  return (
    <ExamCreationWizard 
      examId={examId} 
      isEditing={isEditing}
      onSuccess={handleSuccess}
      userType="institute" // Pass user type for any institute-specific logic
    />
  );
};

export default InstituteCreateExam;
