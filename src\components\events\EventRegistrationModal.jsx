/**
 * EventRegistrationModal Component
 * 
 * Enhanced event registration modal that handles both free and paid events.
 * Integrates with the payment system for events with registration fees.
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiX,
  FiCalendar,
  FiMapPin,
  FiDollarSign,
  FiUser,
  FiMail,
  FiPhone,
  FiInfo,
  FiCreditCard,
  FiCheck,
  FiMinus,
  FiPlus,
  FiUsers,
  FiClock
} from 'react-icons/fi';
import {
  registerForEvent,
  registerForEventWithPayment,
  selectRegistrationLoading,
  selectPaymentRegistrationLoading,
  selectRegistrationError,
  selectPaymentRegistrationError,
  selectRegistrationSuccess,
  selectPaymentRegistrationSuccess,
  clearErrors
} from '../../store/slices/EventsSlice';
import { LoadingSpinner } from '../ui';

const EventRegistrationModal = ({
  isOpen,
  onClose,
  event,
  className = ''
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux selectors
  const registrationLoading = useSelector(selectRegistrationLoading);
  const paymentRegistrationLoading = useSelector(selectPaymentRegistrationLoading);
  const registrationError = useSelector(selectRegistrationError);
  const paymentRegistrationError = useSelector(selectPaymentRegistrationError);
  const registrationSuccess = useSelector(selectRegistrationSuccess);
  const paymentRegistrationSuccess = useSelector(selectPaymentRegistrationSuccess);

  // Local state
  const [step, setStep] = useState('tickets'); // 'tickets', 'details', 'payment', 'confirmation'
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [formData, setFormData] = useState({
    // Registration details
    additional_info: '',
    dietary_requirements: '',
    emergency_contact: {
      name: '',
      phone: '',
      relationship: ''
    },
    // User details for payment
    user_email: '',
    user_name: ''
  });
  const [formErrors, setFormErrors] = useState({});

  // Initialize form with user data
  useEffect(() => {
    if (isOpen) {
      const userData = JSON.parse(localStorage.getItem('userdata') || '{}');
      setFormData(prev => ({
        ...prev,
        user_email: userData.email || '',
        user_name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim()
      }));
      setStep('tickets');
      setSelectedTicket(null);
      setQuantity(1);
      dispatch(clearErrors());
    }
  }, [isOpen, dispatch]);

  // Handle successful registration
  useEffect(() => {
    if (registrationSuccess || paymentRegistrationSuccess) {
      setStep('confirmation');
    }
  }, [registrationSuccess, paymentRegistrationSuccess]);

  // Check if event requires payment
  const requiresPayment = event?.registration_fee && event.registration_fee > 0;
  const isLoading = registrationLoading || paymentRegistrationLoading;
  const error = registrationError || paymentRegistrationError;

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.user_email || !/\S+@\S+\.\S+/.test(formData.user_email)) {
      errors.user_email = 'Valid email is required';
    }

    if (!formData.user_name || formData.user_name.trim().length < 2) {
      errors.user_name = 'Full name is required';
    }

    if (formData.emergency_contact.name && !formData.emergency_contact.phone) {
      errors.emergency_phone = 'Phone number is required when emergency contact name is provided';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name.startsWith('emergency_')) {
      const field = name.replace('emergency_', '');
      setFormData(prev => ({
        ...prev,
        emergency_contact: {
          ...prev.emergency_contact,
          [field]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear field error
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const registrationData = {
        additional_info: formData.additional_info,
        emergency_contact: formData.emergency_contact.name ? formData.emergency_contact : null
      };

      if (requiresPayment) {
        // For paid events, use payment registration flow
        const paymentData = {
          user_email: formData.user_email,
          user_name: formData.user_name,
          return_url: `${window.location.origin}/payment/success`,
          cancel_url: `${window.location.origin}/payment/cancel`
        };

        const result = await dispatch(registerForEventWithPayment({
          eventId: event.id,
          registrationData,
          paymentData
        })).unwrap();

        // If payment is required, redirect to payment page
        if (result.requiresPayment && result.payment) {
          const searchParams = new URLSearchParams({
            registration_id: result.registration.registration_id,
            amount: result.registration.payment_amount,
            type: 'event'
          });
          navigate(`/payment/${event.id}?${searchParams.toString()}`);
        }
      } else {
        // For free events, use regular registration
        await dispatch(registerForEvent({
          eventId: event.id,
          registrationData
        })).unwrap();
      }
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  // Mock tickets data (replace with real API data)
  const mockTickets = [
    {
      id: 'ticket-1',
      name: 'General Admission',
      description: 'Standard access to the event',
      price: 0,
      currency: 'ZAR',
      max_quantity_per_order: 5,
      available_quantity: 100,
      status: 'ACTIVE'
    },
    {
      id: 'ticket-2',
      name: 'VIP Access',
      description: 'Premium access with exclusive benefits',
      price: 299.99,
      currency: 'ZAR',
      max_quantity_per_order: 2,
      available_quantity: 20,
      status: 'ACTIVE'
    },
    {
      id: 'ticket-3',
      name: 'Student Discount',
      description: 'Special pricing for students',
      price: 149.99,
      currency: 'ZAR',
      max_quantity_per_order: 1,
      available_quantity: 50,
      status: 'ACTIVE'
    }
  ];

  // Handle ticket selection
  const handleTicketSelect = (ticket) => {
    setSelectedTicket(ticket);
    setQuantity(1);
  };

  // Handle quantity change
  const handleQuantityChange = (change) => {
    if (!selectedTicket) return;

    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= selectedTicket.max_quantity_per_order) {
      setQuantity(newQuantity);
    }
  };

  // Handle next step
  const handleNextStep = () => {
    if (step === 'tickets' && selectedTicket) {
      setStep('details');
    } else if (step === 'details') {
      if (selectedTicket.price > 0) {
        setStep('payment');
      } else {
        handleSubmit();
      }
    }
  };

  // Handle close
  const handleClose = () => {
    if (!isLoading) {
      setStep('tickets');
      setSelectedTicket(null);
      setQuantity(1);
      setFormData({
        additional_info: '',
        dietary_requirements: '',
        emergency_contact: { name: '', phone: '', relationship: '' },
        user_email: '',
        user_name: ''
      });
      setFormErrors({});
      onClose();
    }
  };

  // Format currency
  const formatCurrency = (amount, currency = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              {step === 'confirmation' ? 'Registration Confirmed!' : 'Register for Event'}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {event?.title}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Event Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-4">
            {event?.image_url && (
              <img
                src={event.image_url}
                alt={event.title}
                className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
              />
            )}
            <div className="flex-1">
              <h4 className="font-semibold text-gray-900 mb-2">
                {event?.title}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <FiCalendar className="w-4 h-4" />
                  <span>
                    {event?.start_date && new Date(event.start_date).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <FiMapPin className="w-4 h-4" />
                  <span>{event?.location}</span>
                </div>
                {requiresPayment && (
                  <div className="flex items-center space-x-2">
                    <FiDollarSign className="w-4 h-4" />
                    <span className="font-semibold">
                      {formatCurrency(event.registration_fee, event.currency)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Step Content */}
        {step === 'details' && (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            {/* User Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  name="user_name"
                  value={formData.user_name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    formErrors.user_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                />
                {formErrors.user_name && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.user_name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="user_email"
                  value={formData.user_email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    formErrors.user_email ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                />
                {formErrors.user_email && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.user_email}</p>
                )}
              </div>
            </div>

            {/* Additional Information */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Additional Information
              </label>
              <textarea
                name="additional_info"
                value={formData.additional_info}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Any special requirements or notes..."
              />
            </div>

            {/* Emergency Contact */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Emergency Contact (Optional)
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <input
                    type="text"
                    name="emergency_name"
                    value={formData.emergency_contact.name}
                    onChange={handleInputChange}
                    placeholder="Contact Name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <input
                    type="tel"
                    name="emergency_phone"
                    value={formData.emergency_contact.phone}
                    onChange={handleInputChange}
                    placeholder="Phone Number"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      formErrors.emergency_phone ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {formErrors.emergency_phone && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.emergency_phone}</p>
                  )}
                </div>
                <div>
                  <input
                    type="text"
                    name="emergency_relationship"
                    value={formData.emergency_contact.relationship}
                    onChange={handleInputChange}
                    placeholder="Relationship"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Payment Notice */}
            {requiresPayment && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <FiCreditCard className="w-5 h-5 text-blue-500 mt-0.5" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium">Payment Required</p>
                    <p>
                      This event requires a registration fee of{' '}
                      <span className="font-semibold">
                        {formatCurrency(event.registration_fee, event.currency)}
                      </span>
                      . You will be redirected to our secure payment page after registration.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    {requiresPayment ? (
                      <>
                        <FiCreditCard className="w-4 h-4" />
                        <span>Continue to Payment</span>
                      </>
                    ) : (
                      <>
                        <FiCheck className="w-4 h-4" />
                        <span>Register</span>
                      </>
                    )}
                  </>
                )}
              </button>
            </div>
          </form>
        )}

        {/* Confirmation Step */}
        {step === 'confirmation' && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiCheck className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Registration Successful!
            </h3>
            <p className="text-gray-600 mb-6">
              {requiresPayment 
                ? 'Your registration has been created. Complete your payment to confirm your spot.'
                : 'You have successfully registered for this event.'
              }
            </p>
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default EventRegistrationModal;
