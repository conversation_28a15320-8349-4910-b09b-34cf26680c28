/**
 * New Event Card Component
 * 
 * Redesigned event card that uses the new event API structure.
 * Displays event information and handles registration/ticket purchasing.
 */

import React, { useState } from 'react';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiDollarSign,
  FiEye,
  FiUserPlus,
  FiCheck,
  FiStar,
  FiAward,
  FiExternalLink
} from 'react-icons/fi';
import { format } from 'date-fns';
import NewEventRegistration from './NewEventRegistration';
import { useNotification } from '../../contexts/NotificationContext';
import newEventService from '../../services/newEventService';

const NewEventCard = ({
  event,
  onViewDetails,
  onRegistrationSuccess,
  variant = 'default', // 'default', 'featured', 'compact'
  showActions = true
}) => {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [isRegistered, setIsRegistered] = useState(event?.user_registration?.is_registered || false);
  
  const { showSuccess, showError } = useNotification();

  // Format date for display
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return 'TBD';
    }
  };

  // Format time for display
  const formatTime = (dateString) => {
    try {
      return format(new Date(dateString), 'h:mm a');
    } catch (error) {
      return '';
    }
  };

  // Get the cheapest ticket price
  const getCheapestPrice = () => {
    if (!event.tickets || event.tickets.length === 0) return null;
    
    const activePaidTickets = event.tickets.filter(ticket => 
      ticket.status === 'ACTIVE' && ticket.price > 0
    );
    
    if (activePaidTickets.length === 0) {
      // Check if there are free tickets
      const freeTickets = event.tickets.filter(ticket => 
        ticket.status === 'ACTIVE' && ticket.price === 0
      );
      return freeTickets.length > 0 ? 0 : null;
    }
    
    return Math.min(...activePaidTickets.map(ticket => ticket.price));
  };

  // Check if event has free tickets
  const hasFreeTickets = () => {
    return event.tickets?.some(ticket => 
      ticket.status === 'ACTIVE' && ticket.price === 0
    );
  };

  // Check if event is sold out
  const isSoldOut = () => {
    return event.tickets?.every(ticket => 
      ticket.status === 'SOLD_OUT' || ticket.available_quantity === 0
    );
  };

  // Check if registration is open
  const isRegistrationOpen = () => {
    const now = new Date();
    const registrationStart = event.registration_start ? new Date(event.registration_start) : null;
    const registrationEnd = event.registration_end ? new Date(event.registration_end) : null;
    
    if (registrationStart && now < registrationStart) return false;
    if (registrationEnd && now > registrationEnd) return false;
    
    return true;
  };

  // Get event status badge
  const getStatusBadge = () => {
    if (isRegistered) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <FiCheck className="w-3 h-3 mr-1" />
          Registered
        </span>
      );
    }
    
    if (isSoldOut()) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Sold Out
        </span>
      );
    }
    
    if (!isRegistrationOpen()) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Registration Closed
        </span>
      );
    }
    
    return null;
  };

  // Get category color
  const getCategoryColor = (category) => {
    const colors = {
      'WORKSHOP': 'bg-blue-100 text-blue-800',
      'SEMINAR': 'bg-green-100 text-green-800',
      'CONFERENCE': 'bg-purple-100 text-purple-800',
      'COMPETITION': 'bg-orange-100 text-orange-800',
      'NETWORKING': 'bg-pink-100 text-pink-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  // Handle registration button click
  const handleRegisterClick = () => {
    if (!newEventService.isAuthenticated()) {
      showError('Please log in to register for events');
      return;
    }
    
    if (isRegistered) {
      showError('You are already registered for this event');
      return;
    }
    
    if (isSoldOut()) {
      showError('This event is sold out');
      return;
    }
    
    if (!isRegistrationOpen()) {
      showError('Registration is not currently open for this event');
      return;
    }
    
    setShowRegistrationModal(true);
  };

  // Handle registration success
  const handleRegistrationSuccess = (result) => {
    setIsRegistered(true);
    setShowRegistrationModal(false);
    
    if (onRegistrationSuccess) {
      onRegistrationSuccess(result);
    }
    
    showSuccess('Registration successful!');
  };

  // Handle view details
  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(event);
    }
  };

  // Render compact variant
  if (variant === 'compact') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center space-x-4">
          {event.banner_image_url && (
            <img
              src={event.banner_image_url}
              alt={event.title}
              className="w-16 h-16 object-cover rounded-lg"
            />
          )}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 truncate">{event.title}</h3>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <FiCalendar className="w-4 h-4 mr-1" />
              {formatDate(event.start_datetime)}
            </div>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <FiMapPin className="w-4 h-4 mr-1" />
              {event.location}
            </div>
          </div>
          {showActions && (
            <div className="flex space-x-2">
              <button
                onClick={handleViewDetails}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <FiEye className="w-4 h-4" />
              </button>
              <button
                onClick={handleRegisterClick}
                disabled={isRegistered || isSoldOut() || !isRegistrationOpen()}
                className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiUserPlus className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Render default/featured variant
  return (
    <>
      <div className={`
        bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300
        ${variant === 'featured' ? 'ring-2 ring-blue-500 ring-opacity-20' : ''}
      `}>
        {/* Event Image */}
        <div className="relative">
          {event.banner_image_url ? (
            <img
              src={event.banner_image_url}
              alt={event.title}
              className="w-full h-48 object-cover"
            />
          ) : (
            <div className="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <FiCalendar className="w-12 h-12 text-white opacity-50" />
            </div>
          )}
          
          {/* Badges */}
          <div className="absolute top-4 left-4 flex space-x-2">
            {event.is_featured && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <FiStar className="w-3 h-3 mr-1" />
                Featured
              </span>
            )}
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>
              {event.category}
            </span>
          </div>
          
          {/* Status Badge */}
          <div className="absolute top-4 right-4">
            {getStatusBadge()}
          </div>
        </div>

        {/* Event Content */}
        <div className="p-6">
          {/* Title and Description */}
          <div className="mb-4">
            <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{event.title}</h3>
            <p className="text-gray-600 text-sm line-clamp-2">{event.short_description || event.description}</p>
          </div>

          {/* Event Details */}
          <div className="space-y-3 mb-6">
            <div className="flex items-center text-sm text-gray-600">
              <FiCalendar className="w-4 h-4 mr-3 text-gray-400" />
              <span>{formatDate(event.start_datetime)}</span>
              {formatTime(event.start_datetime) && (
                <>
                  <FiClock className="w-4 h-4 ml-4 mr-2 text-gray-400" />
                  <span>{formatTime(event.start_datetime)}</span>
                </>
              )}
            </div>
            
            <div className="flex items-center text-sm text-gray-600">
              <FiMapPin className="w-4 h-4 mr-3 text-gray-400" />
              <span className="truncate">{event.location}</span>
            </div>
            
            <div className="flex items-center text-sm text-gray-600">
              <FiUsers className="w-4 h-4 mr-3 text-gray-400" />
              <span>{event.current_registrations || 0} / {event.max_attendees} registered</span>
            </div>
          </div>

          {/* Pricing */}
          <div className="mb-6">
            {getCheapestPrice() === null ? (
              <div className="text-gray-500 text-sm">No tickets available</div>
            ) : getCheapestPrice() === 0 ? (
              <div className="flex items-center">
                <span className="text-2xl font-bold text-green-600">FREE</span>
                {event.tickets?.some(t => t.price > 0) && (
                  <span className="text-sm text-gray-500 ml-2">+ Paid options</span>
                )}
              </div>
            ) : (
              <div className="flex items-center">
                <FiDollarSign className="w-5 h-5 text-gray-400" />
                <span className="text-2xl font-bold text-gray-900">{getCheapestPrice().toFixed(2)}</span>
                <span className="text-sm text-gray-500 ml-1">ZAR</span>
                {hasFreeTickets() && (
                  <span className="text-sm text-gray-500 ml-2">+ Free options</span>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex space-x-3">
              <button
                onClick={handleViewDetails}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
              >
                <FiEye className="w-4 h-4 mr-2" />
                View Details
              </button>
              
              <button
                onClick={handleRegisterClick}
                disabled={isRegistered || isSoldOut() || !isRegistrationOpen()}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
              >
                {isRegistered ? (
                  <>
                    <FiCheck className="w-4 h-4 mr-2" />
                    Registered
                  </>
                ) : (
                  <>
                    <FiUserPlus className="w-4 h-4 mr-2" />
                    Register
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Registration Modal */}
      {showRegistrationModal && (
        <NewEventRegistration
          event={event}
          isOpen={showRegistrationModal}
          onClose={() => setShowRegistrationModal(false)}
          onSuccess={handleRegistrationSuccess}
        />
      )}
    </>
  );
};

export default NewEventCard;
