import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiDollarSign,
  FiSave,
  FiAward,
  FiStar,
  FiPlus,
  FiTrash2,
  FiBookOpen,
  FiMic,
  FiVideo,
  FiSettings
} from 'react-icons/fi';
import {
  createInstituteEvent,
  selectCreateLoading,
  selectCreateError,
  selectCreateSuccess
} from '../../store/slices/InstituteEventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { cleanEventData, validateEventForm } from '../../utils/eventValidation';

const CreateEventPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('basic');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data state - Updated to match API docs
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    short_description: '',
    banner_image_url: '',
    gallery_images: [], // API expects array of image URLs
    category: 'WORKSHOP', // Updated to uppercase enum value
    start_datetime: '', // API expects start_datetime
    end_datetime: '', // API expects end_datetime
    registration_start: '',
    registration_end: '', // API expects registration_end
    location: '', // Now just a string
    max_attendees: '', // API expects max_attendees
    min_attendees: 1,
    status: 'DRAFT', // API default status (uppercase enum)
    is_featured: false,
    is_public: true,
    requires_approval: false,
    tags: [],
    requirements: '',
    agenda: [], // Changed from string to array
    external_links: {},
    is_competition: false,
    competition_exam_id: '', // UUID for competition exam
    competition_rules: '',
    prize_details: {
      first_place: '',
      second_place: '',
      third_place: ''
    },
    organizer_id: '' // Will be set automatically by backend
  });

  // Additional state - Updated to match new API structure
  const [tickets, setTickets] = useState([
    {
      id: 1,
      name: 'General Admission',
      description: '',
      price: 0,
      currency: 'ZAR',
      total_quantity: 1,
      status: 'ACTIVE',
      sale_start: '',
      sale_end: '',
      min_quantity_per_order: 1,
      max_quantity_per_order: 10,
      requires_approval: false,
      is_transferable: true,
      is_refundable: true,
      refund_policy: '',
      terms_and_conditions: '',
      benefits: [],
      includes: []
    }
  ]);

  // Redux selectors
  const createLoading = useSelector(selectCreateLoading);
  const createError = useSelector(selectCreateError);
  const createSuccess = useSelector(selectCreateSuccess);

  // Event categories - Updated to match API enum values
  const categories = [
    { value: 'WORKSHOP', label: 'Workshop', icon: FiBookOpen, color: 'blue' },
    { value: 'CONFERENCE', label: 'Conference', icon: FiMic, color: 'purple' },
    { value: 'WEBINAR', label: 'Webinar', icon: FiVideo, color: 'green' },
    { value: 'COMPETITION', label: 'Competition', icon: FiAward, color: 'orange' }
  ];

  // Validate date order
  const validateDates = () => {
    const errors = [];

    if (formData.start_datetime && formData.end_datetime) {
      if (new Date(formData.start_datetime) >= new Date(formData.end_datetime)) {
        errors.push('Event end time must be after start time');
      }
    }

    if (formData.registration_start && formData.registration_end) {
      if (new Date(formData.registration_start) >= new Date(formData.registration_end)) {
        errors.push('Registration end must be after registration start');
      }
    }

    if (formData.registration_end && formData.start_datetime) {
      if (new Date(formData.registration_end) >= new Date(formData.start_datetime)) {
        errors.push('Registration must end before the event starts');
      }
    }

    return errors;
  };



  // Handle form submission
  const handleSubmit = async (status = 'DRAFT') => {
    setIsSubmitting(true);

    // Validate form data
    const validation = validateEventForm(formData);
    if (!validation.isValid) {
      alert('Please fix the following issues:\n' + validation.errors.join('\n'));
      setIsSubmitting(false);
      return;
    }

    try {
      // Prepare event data with tickets
      const eventDataWithTickets = {
        ...formData,
        status,
        tickets
      };

      // Clean the event data using utility function
      const cleanedEventData = cleanEventData(eventDataWithTickets);

      console.log('Submitting cleaned event data:', cleanedEventData);

      await dispatch(createInstituteEvent(cleanedEventData)).unwrap();
      navigate('/institute/events');
    } catch (error) {
      console.error('Failed to create event:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle ticket changes
  const handleTicketChange = (index, field, value) => {
    const newTickets = [...tickets];
    newTickets[index] = { ...newTickets[index], [field]: value };
    setTickets(newTickets);
  };

  // Handle ticket array fields (benefits, includes)
  const handleTicketArrayChange = (ticketIndex, field, arrayIndex, value) => {
    const newTickets = [...tickets];
    const newArray = [...newTickets[ticketIndex][field]];
    newArray[arrayIndex] = value;
    newTickets[ticketIndex] = { ...newTickets[ticketIndex], [field]: newArray };
    setTickets(newTickets);
  };

  const addTicketArrayItem = (ticketIndex, field) => {
    const newTickets = [...tickets];
    newTickets[ticketIndex] = {
      ...newTickets[ticketIndex],
      [field]: [...newTickets[ticketIndex][field], '']
    };
    setTickets(newTickets);
  };

  const removeTicketArrayItem = (ticketIndex, field, arrayIndex) => {
    const newTickets = [...tickets];
    const newArray = newTickets[ticketIndex][field].filter((_, i) => i !== arrayIndex);
    newTickets[ticketIndex] = { ...newTickets[ticketIndex], [field]: newArray };
    setTickets(newTickets);
  };

  const addTicket = () => {
    setTickets(prev => [
      ...prev,
      {
        id: Date.now(),
        name: '',
        description: '',
        price: 0,
        currency: 'ZAR',
        total_quantity: 1,
        status: 'ACTIVE',
        sale_start: '',
        sale_end: '',
        min_quantity_per_order: 1,
        max_quantity_per_order: 10,
        requires_approval: false,
        is_transferable: true,
        is_refundable: true,
        refund_policy: '',
        terms_and_conditions: '',
        benefits: [],
        includes: []
      }
    ]);
  };

  const removeTicket = (index) => {
    setTickets(prev => prev.filter((_, i) => i !== index));
  };

  // Tab configuration
  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: FiCalendar },
    { id: 'details', label: 'Details', icon: FiMapPin },
    { id: 'settings', label: 'Settings', icon: FiSettings },
    { id: 'tickets', label: 'Tickets', icon: FiDollarSign }
  ];

  // Handle success redirect
  useEffect(() => {
    if (createSuccess) {
      navigate('/institute/events');
    }
  }, [createSuccess, navigate]);

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <FiArrowLeft className="h-4 w-4 mr-2" />
          Back to Events
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Create New Event</h1>
        <p className="mt-2 text-gray-600">
          Create an educational event including workshops, conferences, webinars, or competitions for students
        </p>
        <div className="mt-2 flex items-center text-sm text-blue-600">
          <FiAward className="h-4 w-4 mr-1" />
          <span>Use the "Competition" category for academic contests and challenges</span>
        </div>
      </div>

      {/* Error Message */}
      {createError && (
        <div className="mb-6">
          <ErrorMessage message={createError} />
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 inline mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter event title"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => {
                      handleInputChange('category', e.target.value);
                      handleInputChange('is_competition', e.target.value === 'COMPETITION');
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {categories.map((cat) => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Attendees
                  </label>
                  <input
                    type="number"
                    value={formData.max_attendees}
                    onChange={(e) => handleInputChange('max_attendees', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Leave empty for unlimited"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date & Time *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.start_datetime}
                    onChange={(e) => handleInputChange('start_datetime', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date & Time *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.end_datetime}
                    onChange={(e) => handleInputChange('end_datetime', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Short Description
                  </label>
                  <input
                    type="text"
                    value={formData.short_description}
                    onChange={(e) => handleInputChange('short_description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Brief description for event cards"
                    maxLength={150}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Detailed event description"
                  />
                </div>

                <div className="md:col-span-2">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="is_featured"
                        checked={formData.is_featured}
                        onChange={(e) => handleInputChange('is_featured', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700">
                        <FiStar className="h-4 w-4 inline mr-1" />
                        Featured Event
                      </label>
                    </div>
                    
                    {formData.category === 'competition' && (
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="is_competition"
                          checked={formData.is_competition}
                          onChange={(e) => handleInputChange('is_competition', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="is_competition" className="ml-2 block text-sm text-gray-700">
                          <FiAward className="h-4 w-4 inline mr-1" />
                          Competition Event
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Details Tab */}
          {activeTab === 'details' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Event Details</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location *
                  </label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Main Auditorium, 123 University Ave, City"
                  />
                  <p className="text-sm text-gray-500 mt-1">Enter the complete location details</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Registration Start
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.registration_start}
                    onChange={(e) => handleInputChange('registration_start', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">When registration opens</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Registration Deadline
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.registration_end}
                    onChange={(e) => handleInputChange('registration_end', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">Must be before event start time</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location Address
                  </label>
                  <input
                    type="text"
                    value={formData.location_address}
                    onChange={(e) => handleInputChange('location_address', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Full address or online meeting link"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banner Image URL
                  </label>
                  <input
                    type="url"
                    value={formData.banner_image_url}
                    onChange={(e) => handleInputChange('banner_image_url', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="https://example.com/banner.jpg"
                  />
                </div>

                {/* Competition Details */}
                {formData.is_competition && (
                  <>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Competition Rules
                      </label>
                      <textarea
                        value={formData.competition_rules}
                        onChange={(e) => handleInputChange('competition_rules', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Competition rules and guidelines"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Prize Details
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <input
                          type="text"
                          value={formData.prize_details.first_place}
                          onChange={(e) => handleInputChange('prize_details.first_place', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="1st place prize"
                        />
                        <input
                          type="text"
                          value={formData.prize_details.second_place}
                          onChange={(e) => handleInputChange('prize_details.second_place', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="2nd place prize"
                        />
                        <input
                          type="text"
                          value={formData.prize_details.third_place}
                          onChange={(e) => handleInputChange('prize_details.third_place', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="3rd place prize"
                        />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Event Settings</h2>

              {/* Status and Visibility */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="DRAFT">Draft</option>
                    <option value="PUBLISHED">Published</option>
                    <option value="CANCELLED">Cancelled</option>
                  </select>
                  <p className="text-sm text-gray-500 mt-1">Only published events are visible to users</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Attendees
                  </label>
                  <input
                    type="number"
                    value={formData.max_attendees}
                    onChange={(e) => handleInputChange('max_attendees', e.target.value)}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Leave empty for unlimited"
                  />
                </div>
              </div>

              {/* Checkboxes */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_featured}
                    onChange={(e) => handleInputChange('is_featured', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Featured Event (appears prominently on homepage)
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_public}
                    onChange={(e) => handleInputChange('is_public', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Public Event (visible to all users)
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.requires_approval}
                    onChange={(e) => handleInputChange('requires_approval', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Requires Approval (registrations need manual approval)
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_competition}
                    onChange={(e) => handleInputChange('is_competition', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Competition Event (includes prizes and rules)
                  </label>
                </div>
              </div>

              {/* Requirements */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Requirements
                </label>
                <textarea
                  value={formData.requirements}
                  onChange={(e) => handleInputChange('requirements', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Any prerequisites or requirements for attendees"
                />
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={formData.tags.join(', ')}
                  onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., technology, workshop, beginner"
                />
              </div>

              {/* Competition Fields */}
              {formData.is_competition && (
                <div className="space-y-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <h3 className="text-md font-semibold text-orange-900">Competition Details</h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Competition Rules
                    </label>
                    <textarea
                      value={formData.competition_rules}
                      onChange={(e) => handleInputChange('competition_rules', e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Rules and guidelines for the competition"
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Tickets Tab */}
          {activeTab === 'tickets' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Ticket Types</h3>
                <button
                  type="button"
                  onClick={addTicket}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <FiPlus className="h-4 w-4 mr-2" />
                  Add Ticket Type
                </button>
              </div>

              <div className="space-y-6">
                {tickets.map((ticket, index) => (
                  <div key={ticket.id} className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-md font-semibold text-gray-900">Ticket #{index + 1}</h4>
                      <button
                        type="button"
                        onClick={() => removeTicket(index)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-md"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Basic Info */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Ticket Name *
                        </label>
                        <input
                          type="text"
                          value={ticket.name}
                          onChange={(e) => handleTicketChange(index, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="e.g., General Admission"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Price (ZAR) *
                        </label>
                        <input
                          type="number"
                          value={ticket.price}
                          onChange={(e) => handleTicketChange(index, 'price', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="0"
                          step="0.01"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Total Quantity *
                        </label>
                        <input
                          type="number"
                          value={ticket.total_quantity}
                          onChange={(e) => handleTicketChange(index, 'total_quantity', parseInt(e.target.value) || 1)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="1"
                        />
                      </div>
                    </div>

                    {/* Description */}
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <textarea
                        value={ticket.description}
                        onChange={(e) => handleTicketChange(index, 'description', e.target.value)}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Describe what this ticket includes"
                      />
                    </div>

                    {/* Sale Period */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Sale Start Date
                        </label>
                        <input
                          type="datetime-local"
                          value={ticket.sale_start}
                          onChange={(e) => handleTicketChange(index, 'sale_start', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Sale End Date
                        </label>
                        <input
                          type="datetime-local"
                          value={ticket.sale_end}
                          onChange={(e) => handleTicketChange(index, 'sale_end', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>

                    {/* Quantity Limits */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Min Quantity Per Order
                        </label>
                        <input
                          type="number"
                          value={ticket.min_quantity_per_order}
                          onChange={(e) => handleTicketChange(index, 'min_quantity_per_order', parseInt(e.target.value) || 1)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="1"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Quantity Per Order
                        </label>
                        <input
                          type="number"
                          value={ticket.max_quantity_per_order}
                          onChange={(e) => handleTicketChange(index, 'max_quantity_per_order', parseInt(e.target.value) || 10)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="1"
                        />
                      </div>
                    </div>

                    {/* Options */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={ticket.requires_approval}
                          onChange={(e) => handleTicketChange(index, 'requires_approval', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700">
                          Requires Approval
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={ticket.is_transferable}
                          onChange={(e) => handleTicketChange(index, 'is_transferable', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700">
                          Transferable
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={ticket.is_refundable}
                          onChange={(e) => handleTicketChange(index, 'is_refundable', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 block text-sm text-gray-700">
                          Refundable
                        </label>
                      </div>
                    </div>

                    {/* Policies */}
                    {ticket.is_refundable && (
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Refund Policy
                        </label>
                        <textarea
                          value={ticket.refund_policy}
                          onChange={(e) => handleTicketChange(index, 'refund_policy', e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Describe the refund policy for this ticket"
                        />
                      </div>
                    )}

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Terms and Conditions
                      </label>
                      <textarea
                        value={ticket.terms_and_conditions}
                        onChange={(e) => handleTicketChange(index, 'terms_and_conditions', e.target.value)}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Terms and conditions for this ticket"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={() => navigate('/institute/events')}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </button>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={() => handleSubmit('DRAFT')}
            disabled={isSubmitting}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Save as Draft
          </button>
          <button
            type="button"
            onClick={() => handleSubmit('PUBLISHED')}
            disabled={isSubmitting || !formData.title || !formData.start_datetime}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <FiSave className="h-4 w-4 mr-2" />
                Publish Event
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateEventPage;
