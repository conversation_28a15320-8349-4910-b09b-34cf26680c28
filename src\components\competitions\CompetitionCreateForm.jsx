import React, { useState, useEffect } from 'react';
import {
  FiCalendar,
  FiUsers,
  FiAward,
  FiBookOpen,
  FiSettings,
  FiSave,
  FiX,
  FiSearch,
  FiCopy
} from 'react-icons/fi';
import { createCompetition } from '../../services/competitionService';
import { LoadingSpinner } from '../ui';
import ExamLibraryBrowser from '../exams/ExamLibraryBrowser';
import InstituteExamsTab from '../exams/InstituteExamsTab';

const CompetitionCreateForm = ({ isOpen, onClose, onSuccess, instituteId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_datetime: '',
    end_datetime: '',
    category_id: '',
    exam_id: '',
    copy_exam: false,
    judging_type: 'hybrid',
    mentor_assignment_strategy: 'auto',
    min_mentors_required: 1,
    judging_deadline: '',
    result_publication_date: '',
    competition_rules: '',
    prize_details: {
      first_place: '',
      second_place: '',
      third_place: ''
    }
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [selectedExam, setSelectedExam] = useState(null);
  const [showExamBrowser, setShowExamBrowser] = useState(false);
  const [showMyExams, setShowMyExams] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setError(null);
      setSelectedExam(null);
      setFormData({
        title: '',
        description: '',
        start_datetime: '',
        end_datetime: '',
        category_id: '',
        exam_id: '',
        copy_exam: false,
        judging_type: 'hybrid',
        mentor_assignment_strategy: 'auto',
        min_mentors_required: 1,
        judging_deadline: '',
        result_publication_date: '',
        competition_rules: '',
        prize_details: {
          first_place: '',
          second_place: '',
          third_place: ''
        }
      });
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('prize_')) {
      const prizeField = name.replace('prize_', '');
      setFormData(prev => ({
        ...prev,
        prize_details: {
          ...prev.prize_details,
          [prizeField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleExamSelect = (selection) => {
    setSelectedExam(selection.exam);
    setFormData(prev => ({
      ...prev,
      exam_id: selection.exam.id,
      copy_exam: selection.copyExam
    }));
    setShowExamBrowser(false);
  };

  const handleMyExamSelect = (exam) => {
    setSelectedExam(exam);
    setFormData(prev => ({
      ...prev,
      exam_id: exam.id,
      copy_exam: false // No need to copy own exams
    }));
    setShowMyExams(false);
  };

  const handleRemoveExam = () => {
    setSelectedExam(null);
    setFormData(prev => ({
      ...prev,
      exam_id: '',
      copy_exam: false
    }));
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      setError('Competition title is required');
      return false;
    }
    if (!formData.exam_id || !selectedExam) {
      setError('Please select an exam for the competition');
      return false;
    }
    if (!formData.start_datetime) {
      setError('Start date and time is required');
      return false;
    }
    if (!formData.end_datetime) {
      setError('End date and time is required');
      return false;
    }

    const startDate = new Date(formData.start_datetime);
    const endDate = new Date(formData.end_datetime);

    if (startDate >= endDate) {
      setError('End time must be after start time');
      return false;
    }

    if (startDate <= new Date()) {
      setError('Start time must be in the future');
      return false;
    }

    // Validate judging deadline if provided
    if (formData.judging_deadline) {
      const judgingDate = new Date(formData.judging_deadline);
      if (judgingDate <= endDate) {
        setError('Judging deadline must be after competition end time');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError(null);

      const competitionData = {
        ...formData,
        start_datetime: new Date(formData.start_datetime).toISOString(),
        end_datetime: new Date(formData.end_datetime).toISOString(),
        max_attendees: formData.max_attendees ? parseInt(formData.max_attendees) : undefined
      };

      await createCompetition(competitionData);
      
      onSuccess && onSuccess();
      onClose();
    } catch (err) {
      console.error('Failed to create competition:', err);
      setError(err.response?.data?.message || 'Failed to create competition');
    } finally {
      setLoading(false);
    }
  };

  const TabButton = ({ id, label, icon: Icon }) => (
    <button
      type="button"
      onClick={() => setActiveTab(id)}
      className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
        activeTab === id
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      <Icon className="h-4 w-4" />
      <span>{label}</span>
    </button>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Create Competition</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 p-6 border-b border-gray-200">
          <TabButton id="basic" label="Basic Info" icon={FiBookOpen} />
          <TabButton id="exam" label="Exam Selection" icon={FiSearch} />
          <TabButton id="schedule" label="Schedule" icon={FiCalendar} />
          <TabButton id="judging" label="Judging" icon={FiUsers} />
          <TabButton id="prizes" label="Prizes" icon={FiAward} />
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-6 border-b border-gray-200">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="p-6 space-y-6">
            {/* Basic Info Tab */}
            {activeTab === 'basic' && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Competition Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter competition title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Describe the competition"
                  />
                </div>



                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select category...</option>
                    <option value="programming">Programming</option>
                    <option value="mathematics">Mathematics</option>
                    <option value="science">Science</option>
                    <option value="general">General Knowledge</option>
                  </select>
                </div>
              </div>
            )}

            {/* Exam Selection Tab */}
            {activeTab === 'exam' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Exam</h3>
                  <p className="text-gray-600 mb-6">
                    Choose an existing exam from the library or browse available exams
                  </p>
                </div>

                {selectedExam ? (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <FiBookOpen className="h-5 w-5 text-blue-600" />
                          <h4 className="text-lg font-medium text-gray-900">{selectedExam.title}</h4>
                          {formData.copy_exam && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <FiCopy className="h-3 w-3 mr-1" />
                              Copy
                            </span>
                          )}
                        </div>

                        <p className="text-gray-600 mb-4">{selectedExam.description}</p>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Questions:</span>
                            <p className="font-medium">{selectedExam.questions_count}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Duration:</span>
                            <p className="font-medium">{selectedExam.duration_minutes} min</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Level:</span>
                            <p className="font-medium capitalize">{selectedExam.difficulty_level}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Created by:</span>
                            <p className="font-medium">{selectedExam.created_by}</p>
                          </div>
                        </div>
                      </div>

                      <button
                        type="button"
                        onClick={handleRemoveExam}
                        className="ml-4 text-gray-400 hover:text-gray-600"
                      >
                        <FiX className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <FiSearch className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Exam Selected</h3>
                    <p className="text-gray-600 mb-6">
                      Choose an exam from your institute's library or browse all available exams
                    </p>
                    <div className="flex items-center justify-center space-x-4">
                      <button
                        type="button"
                        onClick={() => setShowMyExams(true)}
                        className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50"
                      >
                        <FiBookOpen className="h-5 w-5 mr-2" />
                        Institute Exams
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowExamBrowser(true)}
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <FiSearch className="h-5 w-5 mr-2" />
                        Browse Library
                      </button>
                    </div>
                  </div>
                )}

                {selectedExam && (
                  <div className="flex justify-center space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowMyExams(true)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <FiBookOpen className="h-4 w-4 mr-2" />
                      Institute Exams
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowExamBrowser(true)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <FiSearch className="h-4 w-4 mr-2" />
                      Browse Library
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Schedule Tab */}
            {activeTab === 'schedule' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Start Date & Time *
                    </label>
                    <input
                      type="datetime-local"
                      name="start_datetime"
                      value={formData.start_datetime}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      End Date & Time *
                    </label>
                    <input
                      type="datetime-local"
                      name="end_datetime"
                      value={formData.end_datetime}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Participants
                  </label>
                  <input
                    type="number"
                    name="max_attendees"
                    value={formData.max_attendees}
                    onChange={handleInputChange}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Leave empty for unlimited"
                  />
                </div>
              </div>
            )}

            {/* Judging Tab */}
            {activeTab === 'judging' && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Judging Type *
                  </label>
                  <select
                    name="judging_type"
                    value={formData.judging_type}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="ai_only">AI Only</option>
                    <option value="mentor_review">Mentor Review Only</option>
                    <option value="hybrid">Hybrid (AI + Mentor Review)</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Choose how submissions will be evaluated
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mentor Assignment Strategy
                  </label>
                  <select
                    name="mentor_assignment_strategy"
                    value={formData.mentor_assignment_strategy}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="auto">Automatic Assignment</option>
                    <option value="manual">Manual Assignment</option>
                    <option value="volunteer">Volunteer Basis</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    How mentors will be assigned to evaluate submissions
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Mentors Required
                  </label>
                  <input
                    type="number"
                    name="min_mentors_required"
                    value={formData.min_mentors_required}
                    onChange={handleInputChange}
                    min="1"
                    max="10"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Minimum number of mentors needed for evaluation
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Judging Deadline
                    </label>
                    <input
                      type="datetime-local"
                      name="judging_deadline"
                      value={formData.judging_deadline}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      When mentor evaluations must be completed
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Result Publication Date
                    </label>
                    <input
                      type="datetime-local"
                      name="result_publication_date"
                      value={formData.result_publication_date}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      When results will be published to participants
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Competition Rules
                  </label>
                  <textarea
                    name="competition_rules"
                    value={formData.competition_rules}
                    onChange={handleInputChange}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter competition rules and guidelines..."
                  />
                </div>
              </div>
            )}



            {/* Prizes Tab */}
            {activeTab === 'prizes' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Place Prize
                    </label>
                    <input
                      type="text"
                      name="prize_first_place"
                      value={formData.prize_details.first_place}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., $500 or Certificate"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Second Place Prize
                    </label>
                    <input
                      type="text"
                      name="prize_second_place"
                      value={formData.prize_details.second_place}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., $300 or Certificate"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Third Place Prize
                    </label>
                    <input
                      type="text"
                      name="prize_third_place"
                      value={formData.prize_details.third_place}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., $100 or Certificate"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-4 p-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading && <LoadingSpinner size="sm" className="mr-2" />}
              <FiSave className="h-4 w-4 mr-2" />
              {loading ? 'Creating...' : 'Create Competition'}
            </button>
          </div>
        </form>
      </div>

      {/* Exam Library Browser Modal */}
      <ExamLibraryBrowser
        isOpen={showExamBrowser}
        onClose={() => setShowExamBrowser(false)}
        onSelectExam={handleExamSelect}
        selectedExamId={formData.exam_id}
        showCopyOption={true}
      />

      {/* My Exams Modal */}
      {showMyExams && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Select from Institute Exams</h2>
              <button
                onClick={() => setShowMyExams(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
              <InstituteExamsTab
                instituteId={instituteId}
                onExamSelect={handleMyExamSelect}
                selectionMode={true}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompetitionCreateForm;
