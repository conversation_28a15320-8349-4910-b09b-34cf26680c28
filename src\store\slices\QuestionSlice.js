import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

// NOTE: Question endpoints don't exist in the actual API
// These are mock implementations until backend endpoints are available
const BASE_URL = `${URL}/api/questions`;
const getAuthToken = () => localStorage.getItem("token");

// Mock data for development
const mockQuestions = [
  {
    id: '1',
    question_text: 'What is 2 + 2?',
    question_type: 'multiple_choice',
    options: ['2', '3', '4', '5'],
    correct_answer: '4',
    marks: 1,
    subject_id: '1',
    chapter_id: '1',
    topic_id: '1',
    difficulty: 'easy',
    created_at: new Date().toISOString()
  },
  {
    id: '2',
    question_text: 'Explain the theory of relativity.',
    question_type: 'essay',
    options: [],
    correct_answer: '',
    marks: 10,
    subject_id: '2',
    chapter_id: '2',
    topic_id: '2',
    difficulty: 'hard',
    created_at: new Date().toISOString()
  }
];

// Thunks - MOCK IMPLEMENTATIONS
export const fetchQuestions = createAsyncThunk(
  "questions/fetchQuestions",
  async (_, thunkAPI) => {
    try {
      // TODO: Replace with actual API call when endpoint is available
      console.warn('Questions endpoint not available - using mock data');

      await new Promise(resolve => setTimeout(resolve, 500));

      return { questions: mockQuestions, total: mockQuestions.length };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchQuestionById = createAsyncThunk(
  "questions/fetchQuestionById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createQuestion = createAsyncThunk(
  "questions/createQuestion",
  async (questionData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/`, questionData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateQuestion = createAsyncThunk(
  "questions/updateQuestion",
  async ({ id, questionData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, questionData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const deleteQuestion = createAsyncThunk(
  "questions/deleteQuestion",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateQuestionOption = createAsyncThunk(
  "questions/updateQuestionOption",
  async ({ optionId, optionData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/options/${optionId}`, optionData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// AI Generate Questions
export const aiGenerateQuestions = createAsyncThunk(
  "questions/aiGenerateQuestions",
  async (payload, thunkAPI) => {
    try {
      // Transform the payload to match the expected API format
      const transformedPayload = {
        class: payload.class || "10", // Default class level if not provided
        subject: payload.subject || "",
        chapter: payload.chapter || "",
        no_of_questions: payload.no_of_questions || 3,
        topic: payload.topic || "",
        subtopic: payload.subtopic || "",
        // Difficulty distribution (optional)
        no_of_easy: payload.no_of_easy || null,
        no_of_medium: payload.no_of_medium || null,
        no_of_hard: payload.no_of_hard || null
      };

      console.log('AI Generate Questions - Sending payload:', transformedPayload);

      const res = await axios.post(`${BASE_URL}/ai-generate`, transformedPayload, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
        transformResponse: [(data) => {
          // Force parse JSON if it's a string
          if (typeof data === 'string') {
            try {
              return JSON.parse(data);
            } catch (e) {
              console.error('Failed to parse JSON:', e);
              return data;
            }
          }
          return data;
        }]
      });

      console.log('API Response after transform:', res.data);
      console.log('Response type:', typeof res.data);

      return res.data;
    } catch (err) {
      // Safely extract error message to avoid circular reference issues
      const errorMessage = err.response?.data?.detail ||
                          err.response?.data?.message ||
                          err.message ||
                          'Failed to generate questions';

      console.error('AI Generate Questions Error:', errorMessage);
      return thunkAPI.rejectWithValue(errorMessage);
    }
  }
);









const initialState = {
  questions: [],
  currentQuestion: null,
  loading: false,
  error: null,
  success: null,
  aiGeneratedQuestions: null,

};

const questionSlice = createSlice({
  name: "questions",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch all
      .addCase(fetchQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.questions = action.payload;
      })
      .addCase(fetchQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch by ID
      .addCase(fetchQuestionById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestionById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentQuestion = action.payload;
      })
      .addCase(fetchQuestionById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create
      .addCase(createQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question created successfully.";
        state.questions.push(action.payload);
      })
      .addCase(createQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update
      .addCase(updateQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question updated successfully.";
        const idx = state.questions.findIndex(q => q.id === action.payload.id);
        if (idx !== -1) state.questions[idx] = action.payload;
        if (state.currentQuestion && state.currentQuestion.id === action.payload.id) {
          state.currentQuestion = action.payload;
        }
      })
      .addCase(updateQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete
      .addCase(deleteQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question deleted successfully.";
        state.questions = state.questions.filter(q => q.id !== action.payload);
        if (state.currentQuestion && state.currentQuestion.id === action.payload) {
          state.currentQuestion = null;
        }
      })
      .addCase(deleteQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update Option
      .addCase(updateQuestionOption.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateQuestionOption.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Question option updated successfully.";
      })
      .addCase(updateQuestionOption.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // AI Generate Questions
      .addCase(aiGenerateQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.aiGeneratedQuestions = null;
      })
      .addCase(aiGenerateQuestions.fulfilled, (state, action) => {
        state.loading = false;
        let response = action.payload;

        console.log('AI Questions Redux - Raw Response:', response);
        console.log('AI Questions Redux - Response Type:', typeof response);

        // Handle case where response is still a JSON string
        if (typeof response === 'string') {
          try {
            response = JSON.parse(response);
            console.log('AI Questions Redux - Parsed Response:', response);
          } catch (e) {
            console.error('AI Questions Redux - Parse Error:', e);
            state.error = "Invalid JSON response from AI service";
            return;
          }
        }

        // Extract questions from response
        const questions = response.questions || [];
        console.log('AI Questions Redux - Extracted questions:', questions);

        state.aiGeneratedQuestions = questions;
        state.success = null; // Remove success message
      })
      .addCase(aiGenerateQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = typeof action.payload === 'object'
          ? action.payload?.detail || action.payload?.message || 'Failed to generate questions'
          : action.payload || 'Failed to generate questions';
        state.aiGeneratedQuestions = null;
      })


  },
});

export default questionSlice.reducer;
