import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiShield,
  FiClock,
  FiAlertTriangle
} from 'react-icons/fi';
import CompetitionInterface from '../../components/competitions/CompetitionInterface';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import {
  getCompetition,
  startCompetitionSession,
  submitCompetitionAnswer,
  submitCompetitionFinal
} from '../../services/competitionService';

const CompetitionInterfacePage = () => {
  const { competitionId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [competition, setCompetition] = useState(null);
  const [session, setSession] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [submissionComplete, setSubmissionComplete] = useState(false);

  useEffect(() => {
    loadCompetitionData();
  }, [competitionId]);

  const loadCompetitionData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const competitionData = await getCompetition(competitionId);
      setCompetition(competitionData);
      
      // Check if user is registered and can participate
      if (!competitionData.user_registered) {
        setError('You are not registered for this competition');
        return;
      }
      
      // Check if competition is active
      const now = new Date();
      const startTime = new Date(competitionData.start_datetime);
      const endTime = new Date(competitionData.end_datetime);
      
      if (now < startTime) {
        setError('Competition has not started yet');
        return;
      }
      
      if (now > endTime) {
        setError('Competition has ended');
        return;
      }
      
    } catch (err) {
      console.error('Error loading competition:', err);
      setError(err.response?.data?.message || 'Failed to load competition');
    } finally {
      setLoading(false);
    }
  };

  const handleStartSession = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const sessionData = await startCompetitionSession(competitionId);
      setSession(sessionData);
      setQuestions(sessionData.questions || []);
      setSessionStarted(true);
      
    } catch (err) {
      console.error('Error starting session:', err);
      setError(err.response?.data?.message || 'Failed to start competition session');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitAnswer = async (questionId, answer) => {
    try {
      await submitCompetitionAnswer(session.session_id, {
        question_id: questionId,
        answer: answer
      });
    } catch (err) {
      console.error('Error submitting answer:', err);
      // Don't show error to user for individual answer submissions
    }
  };

  const handleFinalSubmit = async (answers) => {
    try {
      setLoading(true);
      
      await submitCompetitionFinal(session.session_id, {
        answers: answers,
        submission_time: new Date().toISOString()
      });
      
      setSubmissionComplete(true);
      
      // Redirect to results page after a delay
      setTimeout(() => {
        navigate(`/competitions/${competitionId}/results`);
      }, 3000);
      
    } catch (err) {
      console.error('Error submitting competition:', err);
      setError(err.response?.data?.message || 'Failed to submit competition');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToList = () => {
    navigate('/competitions');
  };

  if (loading && !sessionStarted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <FiAlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Cannot Access Competition</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={handleBackToList}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <FiArrowLeft className="h-4 w-4 mr-2" />
              Back to Competitions
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (submissionComplete) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Competition Submitted!</h2>
            <p className="text-gray-600 mb-6">
              Your competition has been submitted successfully. You will be redirected to the results page shortly.
            </p>
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={() => navigate(`/competitions/${competitionId}/results`)}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                View Results
              </button>
              <button
                onClick={handleBackToList}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Back to Competitions
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!sessionStarted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-2xl w-full">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            {/* Competition Info */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{competition.title}</h1>
              <p className="text-gray-600 mb-4">{competition.description}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <FiClock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900">Duration</p>
                  <p className="text-xs text-gray-600">
                    {Math.round((new Date(competition.end_datetime) - new Date(competition.start_datetime)) / (1000 * 60))} minutes
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <FiShield className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900">Security</p>
                  <p className="text-xs text-gray-600">
                    {competition.security_settings?.proctoring_enabled ? 'Monitored' : 'Standard'}
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <svg className="h-6 w-6 text-purple-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="text-sm font-medium text-gray-900">Questions</p>
                  <p className="text-xs text-gray-600">{competition.total_questions || 'Multiple'}</p>
                </div>
              </div>
            </div>

            {/* Rules and Instructions */}
            {competition.competition_rules && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Competition Rules</h3>
                <div className="text-sm text-blue-800 whitespace-pre-line">
                  {competition.competition_rules}
                </div>
              </div>
            )}

            {/* Security Warnings */}
            {competition.security_settings && Object.values(competition.security_settings).some(Boolean) && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h3 className="text-sm font-medium text-yellow-900 mb-2">Security Notice</h3>
                <ul className="text-sm text-yellow-800 space-y-1">
                  {competition.security_settings.proctoring_enabled && (
                    <li>• This competition is monitored - your activity will be recorded</li>
                  )}
                  {competition.security_settings.tab_switching_detection && (
                    <li>• Tab switching will be detected and may result in disqualification</li>
                  )}
                  {competition.security_settings.copy_paste_disabled && (
                    <li>• Copy and paste functions are disabled during this competition</li>
                  )}
                  {competition.security_settings.browser_lockdown && (
                    <li>• Your browser will be locked to this competition interface</li>
                  )}
                </ul>
              </div>
            )}

            {/* Start Button */}
            <div className="text-center">
              <button
                onClick={handleStartSession}
                disabled={loading}
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading && <LoadingSpinner size="sm" className="mr-2" />}
                {loading ? 'Starting...' : 'Start Competition'}
              </button>
              
              <p className="text-xs text-gray-500 mt-2">
                Once you start, the timer will begin and cannot be paused
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <CompetitionInterface
      competition={competition}
      session={session}
      questions={questions}
      onSubmitAnswer={handleSubmitAnswer}
      onFinalSubmit={handleFinalSubmit}
      securitySettings={competition.security_settings || {}}
    />
  );
};

export default CompetitionInterfacePage;
