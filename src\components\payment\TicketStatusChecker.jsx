/**
 * Ticket Status Checker Component
 * 
 * Allows users to check the status of their ticket purchases.
 * Useful for users who closed browser during payment or want to verify status later.
 */

import React, { useState } from 'react';
import {
  FiSearch,
  FiCheck,
  FiX,
  FiClock,
  FiRefreshCw,
  FiInfo,
  FiCalendar,
  FiMapPin,
  FiUser
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import mvpPaymentService from '../../services/mvpPaymentService';
import { LoadingSpinner } from '../ui';

const TicketStatusChecker = ({ className = '' }) => {
  const { showSuccess, showError, showInfo } = useNotification();
  
  // State
  const [ticketId, setTicketId] = useState('');
  const [ticketStatus, setTicketStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  /**
   * Handle status check
   */
  const handleCheckStatus = async (e) => {
    e.preventDefault();
    
    if (!ticketId.trim()) {
      showError('Please enter a ticket ID');
      return;
    }

    setIsLoading(true);
    setHasSearched(true);
    
    try {
      showInfo('Checking ticket status...');
      const status = await mvpPaymentService.checkTicketStatus(ticketId.trim());
      setTicketStatus(status);
      
      // Log the status check
      mvpPaymentService.logPaymentEvent(ticketId.trim(), 'status_check', {
        status: status.status
      });
      
      if (status.status === 'confirmed' || status.status === 'paid') {
        showSuccess('Ticket found and confirmed!');
      } else if (status.status === 'pending') {
        showInfo('Payment is still being processed.');
      } else {
        showError('Ticket status: ' + (status.status || 'Unknown'));
      }
      
    } catch (error) {
      console.error('Failed to check ticket status:', error);
      setTicketStatus(null);
      showError(error.message || 'Could not find ticket. Please check your ticket ID.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Clear search
   */
  const clearSearch = () => {
    setTicketId('');
    setTicketStatus(null);
    setHasSearched(false);
  };

  /**
   * Get status icon and color
   */
  const getStatusDisplay = (status) => {
    switch (status) {
      case 'confirmed':
      case 'paid':
        return {
          icon: <FiCheck className="w-5 h-5" />,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          label: 'Confirmed'
        };
      case 'pending':
        return {
          icon: <FiClock className="w-5 h-5" />,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          label: 'Pending'
        };
      case 'cancelled':
      case 'failed':
        return {
          icon: <FiX className="w-5 h-5" />,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          label: 'Failed'
        };
      default:
        return {
          icon: <FiInfo className="w-5 h-5" />,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          label: status || 'Unknown'
        };
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Check Ticket Status</h3>
      
      <p className="text-sm text-gray-600 mb-4">
        Enter your ticket ID to check the current status of your purchase.
      </p>

      {/* Search Form */}
      <form onSubmit={handleCheckStatus} className="mb-6">
        <div className="flex space-x-3">
          <div className="flex-1">
            <input
              type="text"
              value={ticketId}
              onChange={(e) => setTicketId(e.target.value)}
              placeholder="Enter ticket ID (e.g., ticket-123)"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
          <button
            type="submit"
            disabled={isLoading || !ticketId.trim()}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <FiSearch className="w-4 h-4 mr-2" />
            )}
            {isLoading ? 'Checking...' : 'Check Status'}
          </button>
        </div>
      </form>

      {/* Results */}
      {hasSearched && (
        <div className="border-t pt-6">
          {ticketStatus ? (
            <div className="space-y-4">
              {/* Status Header */}
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">Ticket Status</h4>
                <button
                  onClick={() => handleCheckStatus({ preventDefault: () => {} })}
                  disabled={isLoading}
                  className="flex items-center text-blue-600 hover:text-blue-700 disabled:opacity-50"
                >
                  <FiRefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>

              {/* Status Display */}
              <div className="flex items-center space-x-3">
                {(() => {
                  const statusDisplay = getStatusDisplay(ticketStatus.status);
                  return (
                    <>
                      <div className={`w-10 h-10 rounded-full ${statusDisplay.bgColor} flex items-center justify-center ${statusDisplay.color}`}>
                        {statusDisplay.icon}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {statusDisplay.label}
                        </p>
                        <p className="text-sm text-gray-600">
                          Ticket ID: {ticketId}
                        </p>
                      </div>
                    </>
                  );
                })()}
              </div>

              {/* Ticket Details */}
              {ticketStatus.ticket && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-2">Ticket Details</h5>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <FiUser className="w-4 h-4 mr-2" />
                      {ticketStatus.ticket.name || 'Ticket'}
                    </div>
                    {ticketStatus.ticket.price && (
                      <div className="flex items-center">
                        <span className="w-4 h-4 mr-2 text-center">R</span>
                        {ticketStatus.ticket.price}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Event Details */}
              {ticketStatus.event && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-2">Event Details</h5>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <FiCalendar className="w-4 h-4 mr-2" />
                      {ticketStatus.event.title || ticketStatus.event.name}
                    </div>
                    {ticketStatus.event.date && (
                      <div className="flex items-center">
                        <FiCalendar className="w-4 h-4 mr-2" />
                        {new Date(ticketStatus.event.date).toLocaleDateString()}
                      </div>
                    )}
                    {ticketStatus.event.location && (
                      <div className="flex items-center">
                        <FiMapPin className="w-4 h-4 mr-2" />
                        {ticketStatus.event.location}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Payment Details */}
              {ticketStatus.payment && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-2">Payment Details</h5>
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                    {ticketStatus.payment.payment_id && (
                      <div>
                        <p className="text-gray-500">Payment ID</p>
                        <p className="font-medium">{ticketStatus.payment.payment_id}</p>
                      </div>
                    )}
                    {ticketStatus.payment.amount && (
                      <div>
                        <p className="text-gray-500">Amount</p>
                        <p className="font-medium">R {ticketStatus.payment.amount}</p>
                      </div>
                    )}
                    {ticketStatus.payment.date && (
                      <div>
                        <p className="text-gray-500">Payment Date</p>
                        <p className="font-medium">
                          {new Date(ticketStatus.payment.date).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Clear Button */}
              <button
                onClick={clearSearch}
                className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Check Another Ticket
              </button>
            </div>
          ) : (
            <div className="text-center py-8">
              <FiX className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="font-medium text-gray-900 mb-2">Ticket Not Found</h4>
              <p className="text-sm text-gray-600 mb-4">
                We couldn't find a ticket with ID "{ticketId}". Please check the ID and try again.
              </p>
              <button
                onClick={clearSearch}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TicketStatusChecker;
