import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiUsers,
  FiBarChart3,
  FiSettings,
  FiEye,
  FiCalendar,
  FiAward,
  FiShield,
  FiBookOpen
} from 'react-icons/fi';
import {
  getInstituteCompetitions,
  deleteCompetition,
  getCompetitionStatistics
} from '../../services/competitionService';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import CompetitionCard from '../../components/competitions/CompetitionCard';
import CompetitionDashboard from '../../components/competitions/CompetitionDashboard';
import CompetitionCreateForm from '../../components/competitions/CompetitionCreateForm';
import SecuritySettingsPanel from '../../components/competitions/SecuritySettingsPanel';
import ResultsCalculationPanel from '../../components/competitions/ResultsCalculationPanel';
import InstituteExamsTab from '../../components/exams/InstituteExamsTab';

const CompetitionManagementPage = () => {
  const navigate = useNavigate();
  const { instituteId } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [competitions, setCompetitions] = useState([]);
  const [selectedCompetition, setSelectedCompetition] = useState(null);
  const [activeTab, setActiveTab] = useState('overview'); // overview, dashboard, settings, security, results
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all', // all, upcoming, ongoing, completed
    search: ''
  });

  useEffect(() => {
    if (instituteId) {
      loadCompetitions();
    }
  }, [instituteId, filters]);

  const loadCompetitions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getInstituteCompetitions(instituteId, {
        status: filters.status !== 'all' ? filters.status : undefined,
        search: filters.search || undefined
      });
      
      setCompetitions(data.competitions || []);
      
    } catch (err) {
      console.error('Error loading competitions:', err);
      setError('Failed to load competitions');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCompetition = () => {
    setShowCreateForm(true);
  };

  const handleEditCompetition = (competition) => {
    navigate(`/institute/${instituteId}/competitions/${competition.id}/edit`);
  };

  const handleDeleteCompetition = async (competition) => {
    if (!window.confirm(`Are you sure you want to delete "${competition.title}"?`)) {
      return;
    }

    try {
      await deleteCompetition(competition.id);
      await loadCompetitions();
    } catch (err) {
      console.error('Error deleting competition:', err);
      setError('Failed to delete competition');
    }
  };

  const handleViewCompetition = (competition) => {
    setSelectedCompetition(competition);
    setActiveTab('dashboard');
  };

  const handleManageMentors = (competition) => {
    navigate(`/institute/${instituteId}/competitions/${competition.id}/mentors`);
  };

  const handleViewResults = (competition) => {
    navigate(`/institute/${instituteId}/competitions/${competition.id}/results`);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      upcoming: { color: 'blue', label: 'Upcoming' },
      ongoing: { color: 'green', label: 'Ongoing' },
      completed: { color: 'gray', label: 'Completed' },
      cancelled: { color: 'red', label: 'Cancelled' }
    };

    const config = statusConfig[status] || { color: 'gray', label: status };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${config.color}-100 text-${config.color}-800`}>
        {config.label}
      </span>
    );
  };

  const TabButton = ({ id, label, icon: Icon, isActive, onClick }) => (
    <button
      onClick={() => onClick(id)}
      className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      <Icon className="h-4 w-4" />
      <span>{label}</span>
    </button>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Competition Management</h1>
          <p className="text-gray-600 mt-1">Manage your institute's competitions</p>
        </div>
        
        <button
          onClick={handleCreateCompetition}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Create Competition
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-2 border-b border-gray-200 pb-4">
        <TabButton
          id="overview"
          label="Overview"
          icon={FiBarChart3}
          isActive={activeTab === 'overview'}
          onClick={setActiveTab}
        />

        <TabButton
          id="exams"
          label="Exams Library"
          icon={FiBookOpen}
          isActive={activeTab === 'exams'}
          onClick={setActiveTab}
        />
        {selectedCompetition && (
          <>
            <TabButton
              id="dashboard"
              label={`${selectedCompetition.title} Dashboard`}
              icon={FiEye}
              isActive={activeTab === 'dashboard'}
              onClick={setActiveTab}
            />
            <TabButton
              id="security"
              label="Security Settings"
              icon={FiShield}
              isActive={activeTab === 'security'}
              onClick={setActiveTab}
            />
            <TabButton
              id="results"
              label="Results & Analytics"
              icon={FiAward}
              isActive={activeTab === 'results'}
              onClick={setActiveTab}
            />
          </>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage message={error} />
      )}

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Status:</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Competitions</option>
                  <option value="upcoming">Upcoming</option>
                  <option value="ongoing">Ongoing</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div className="flex items-center space-x-2 flex-1">
                <label className="text-sm font-medium text-gray-700">Search:</label>
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  placeholder="Search competitions..."
                  className="flex-1 text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Competitions Grid */}
          {competitions.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {competitions.map((competition) => (
                <div key={competition.id} className="relative">
                  <CompetitionCard
                    competition={competition}
                    onViewDetails={handleViewCompetition}
                    showActions={false}
                    variant="detailed"
                  />
                  
                  {/* Management Actions Overlay */}
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <button
                      onClick={() => handleViewCompetition(competition)}
                      className="p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
                      title="View Dashboard"
                    >
                      <FiEye className="h-4 w-4 text-gray-600" />
                    </button>
                    
                    <button
                      onClick={() => handleEditCompetition(competition)}
                      className="p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
                      title="Edit Competition"
                    >
                      <FiEdit className="h-4 w-4 text-blue-600" />
                    </button>
                    
                    <button
                      onClick={() => handleDeleteCompetition(competition)}
                      className="p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
                      title="Delete Competition"
                    >
                      <FiTrash2 className="h-4 w-4 text-red-600" />
                    </button>
                  </div>

                  {/* Quick Actions */}
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                      <div className="flex items-center justify-between text-xs">
                        <button
                          onClick={() => handleManageMentors(competition)}
                          className="flex items-center space-x-1 text-blue-600 hover:text-blue-800"
                        >
                          <FiUsers className="h-3 w-3" />
                          <span>Mentors</span>
                        </button>
                        
                        <button
                          onClick={() => handleViewResults(competition)}
                          className="flex items-center space-x-1 text-green-600 hover:text-green-800"
                        >
                          <FiAward className="h-3 w-3" />
                          <span>Results</span>
                        </button>
                        
                        <button
                          onClick={() => navigate(`/institute/${instituteId}/competitions/${competition.id}/security`)}
                          className="flex items-center space-x-1 text-purple-600 hover:text-purple-800"
                        >
                          <FiShield className="h-3 w-3" />
                          <span>Security</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiCalendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Competitions Found</h3>
              <p className="text-gray-600 mb-6">
                {filters.status !== 'all' || filters.search
                  ? 'No competitions match your current filters.'
                  : 'Get started by creating your first competition.'}
              </p>
              <button
                onClick={handleCreateCompetition}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <FiPlus className="h-4 w-4 mr-2" />
                Create Competition
              </button>
            </div>
          )}
        </div>
      )}

      {/* Dashboard Tab */}
      {activeTab === 'dashboard' && selectedCompetition && (
        <div className="space-y-6">
          {/* Competition Header */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{selectedCompetition.title}</h2>
                <p className="text-gray-600 mt-1">{selectedCompetition.description}</p>
                <div className="flex items-center space-x-4 mt-3">
                  {getStatusBadge(selectedCompetition.status)}
                  <span className="text-sm text-gray-500">
                    {selectedCompetition.current_participants || 0} / {selectedCompetition.max_participants} participants
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleEditCompetition(selectedCompetition)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <FiEdit className="h-4 w-4 mr-2" />
                  Edit
                </button>
                
                <button
                  onClick={() => navigate(`/institute/${instituteId}/competitions/${selectedCompetition.id}/settings`)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <FiSettings className="h-4 w-4 mr-2" />
                  Settings
                </button>
              </div>
            </div>
          </div>

          {/* Competition Dashboard */}
          <CompetitionDashboard
            competitionId={selectedCompetition.id}
            userRole="institute"
          />
        </div>
      )}

      {/* Exams Tab */}
      {activeTab === 'exams' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Institute Exams</h3>
            {instituteId ? (
              <InstituteExamsTab instituteId={instituteId} />
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-600">Loading institute information...</p>
                <p className="text-sm text-gray-500 mt-2">Institute ID: {instituteId || 'Not found'}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Security Settings Tab */}
      {activeTab === 'security' && selectedCompetition && (
        <div className="space-y-6">
          <SecuritySettingsPanel
            competitionId={selectedCompetition.id}
            initialSettings={selectedCompetition.security_settings || {}}
            onUpdate={(settings) => {
              setSelectedCompetition(prev => ({
                ...prev,
                security_settings: settings
              }));
            }}
          />
        </div>
      )}

      {/* Results & Analytics Tab */}
      {activeTab === 'results' && selectedCompetition && (
        <div className="space-y-6">
          <ResultsCalculationPanel
            competitionId={selectedCompetition.id}
            onResultsUpdated={() => {
              // Refresh competition data
              loadCompetitions();
            }}
          />
        </div>
      )}

      {/* Create Competition Modal */}
      {showCreateForm && (
        <CompetitionCreateForm
          isOpen={showCreateForm}
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => {
            setShowCreateForm(false);
            loadCompetitions();
          }}
          instituteId={instituteId}
        />
      )}
    </div>
  );
};

export default CompetitionManagementPage;
