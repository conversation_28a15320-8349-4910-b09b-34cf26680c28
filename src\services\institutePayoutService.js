/**
 * Institute Payout Service
 * 
 * Handles institute payout-related API calls
 */

import axios from 'axios';
import { getAuthToken } from '../utils/helpers/authHelpers';

const API_BASE = '/api/institute/payouts';

export class InstitutePayoutService {
  
  /**
   * Get institute payout dashboard summary
   * @returns {Promise} Payout dashboard data
   */
  static async getPayoutDashboard() {
    try {
      const response = await axios.get(`${API_BASE}/dashboard`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get payout dashboard');
    }
  }

  /**
   * Get sales report for specific period
   * @param {number} days - Number of days to include in report
   * @returns {Promise} Sales report data
   */
  static async getSalesReport(days = 30) {
    try {
      const response = await axios.get(`${API_BASE}/sales-report?days=${days}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get sales report');
    }
  }

  /**
   * Get payout history
   * @param {Object} params - Query parameters
   * @returns {Promise} Payout history data
   */
  static async getPayoutHistory(params = {}) {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const response = await axios.get(`${API_BASE}/payouts?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get payout history');
    }
  }

  /**
   * Get pending revenue details
   * @returns {Promise} Pending revenue data
   */
  static async getPendingRevenue() {
    try {
      const response = await axios.get(`${API_BASE}/pending-revenue`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get pending revenue');
    }
  }

  /**
   * Get bank account details (masked)
   * @returns {Promise} Bank account details
   */
  static async getBankDetails() {
    try {
      const response = await axios.get(`${API_BASE}/bank-details`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get bank details');
    }
  }

  /**
   * Update bank account details
   * @param {Object} bankDetails - Bank account details
   * @returns {Promise} Update response
   */
  static async updateBankDetails(bankDetails) {
    try {
      const response = await axios.put(`${API_BASE}/bank-details`, bankDetails, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update bank details');
    }
  }

  /**
   * Check specific event payout status
   * @param {string} eventId - Event ID
   * @returns {Promise} Event payout status
   */
  static async getEventPayoutStatus(eventId) {
    try {
      const response = await axios.get(`${API_BASE}/payout-status/${eventId}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get event payout status');
    }
  }

  /**
   * Format currency amount
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted currency string
   */
  static formatCurrency(amount, currency = 'PKR') {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Get payout status badge info
   * @param {string} status - Payout status
   * @returns {Object} Status badge info
   */
  static getPayoutStatusBadge(status) {
    const statusMap = {
      'pending': {
        color: 'yellow',
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        label: 'Pending'
      },
      'processing': {
        color: 'blue',
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        label: 'Processing'
      },
      'completed': {
        color: 'green',
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        label: 'Completed'
      },
      'failed': {
        color: 'red',
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        label: 'Failed'
      },
      'cancelled': {
        color: 'gray',
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        label: 'Cancelled'
      }
    };

    return statusMap[status] || {
      color: 'gray',
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
      label: 'Unknown'
    };
  }

  /**
   * Calculate payout percentage
   * @param {number} received - Amount received
   * @param {number} total - Total amount
   * @returns {number} Percentage
   */
  static calculatePayoutPercentage(received, total) {
    if (total === 0) return 0;
    return Math.round((received / total) * 100);
  }

  /**
   * Validate bank account details
   * @param {Object} bankDetails - Bank details to validate
   * @returns {Object} Validation result
   */
  static validateBankDetails(bankDetails) {
    const errors = [];

    if (!bankDetails.bank_account_number?.trim()) {
      errors.push('Bank account number is required');
    }

    if (!bankDetails.bank_name?.trim()) {
      errors.push('Bank name is required');
    }

    if (!bankDetails.account_holder_name?.trim()) {
      errors.push('Account holder name is required');
    }

    // Validate account number format (basic validation)
    if (bankDetails.bank_account_number && !/^\d{10,20}$/.test(bankDetails.bank_account_number.replace(/\s/g, ''))) {
      errors.push('Invalid bank account number format');
    }

    // Validate JazzCash number if provided
    if (bankDetails.jazzcash_number && !/^\+92\d{10}$/.test(bankDetails.jazzcash_number)) {
      errors.push('Invalid JazzCash number format (should be +92XXXXXXXXXX)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Mask bank account number for display
   * @param {string} accountNumber - Account number to mask
   * @returns {string} Masked account number
   */
  static maskAccountNumber(accountNumber) {
    if (!accountNumber) return '';
    const cleaned = accountNumber.replace(/\s/g, '');
    if (cleaned.length <= 4) return cleaned;
    return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4);
  }

  /**
   * Get revenue summary for display
   * @param {Object} summary - Revenue summary data
   * @returns {Object} Formatted summary
   */
  static formatRevenueSummary(summary) {
    return {
      totalReceived: InstitutePayoutService.formatCurrency(summary.total_amount_received || 0),
      pendingAmount: InstitutePayoutService.formatCurrency(summary.pending_amount || 0),
      totalPayouts: summary.total_payouts_received || 0,
      pendingPayouts: summary.pending_payouts || 0,
      payoutPercentage: InstitutePayoutService.calculatePayoutPercentage(
        summary.total_amount_received || 0,
        (summary.total_amount_received || 0) + (summary.pending_amount || 0)
      )
    };
  }
}

// Create and export service instance
const institutePayoutService = new InstitutePayoutService();
export default institutePayoutService;

// Export individual methods for convenience
export const {
  getPayoutDashboard,
  getSalesReport,
  getPayoutHistory,
  getPendingRevenue,
  getBankDetails,
  updateBankDetails,
  getEventPayoutStatus,
  formatCurrency,
  getPayoutStatusBadge,
  calculatePayoutPercentage,
  validateBankDetails,
  maskAccountNumber,
  formatRevenueSummary
} = InstitutePayoutService;
