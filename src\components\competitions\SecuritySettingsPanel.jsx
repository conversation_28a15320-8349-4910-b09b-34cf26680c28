import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hield,
  <PERSON>Eye,
  FiMonitor,
  FiLock,
  FiAlertTriangle,
  FiSave,
  FiInfo
} from 'react-icons/fi';
import { updateCompetitionSecurity } from '../../services/competitionService';
import { LoadingSpinner } from '../ui';

const SecuritySettingsPanel = ({ competitionId, initialSettings = {}, onUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [settings, setSettings] = useState({
    proctoring_enabled: false,
    randomize_questions: false,
    cheating_detection: false,
    tab_switching_detection: false,
    browser_lockdown: false,
    time_limit_strict: false,
    copy_paste_disabled: false,
    screenshot_detection: false,
    ...initialSettings
  });

  useEffect(() => {
    setSettings(prev => ({
      ...prev,
      ...initialSettings
    }));
  }, [initialSettings]);

  const handleToggle = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
    setSuccess(false);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await updateCompetitionSecurity(competitionId, settings);
      
      setSuccess(true);
      onUpdate && onUpdate(settings);
      
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Failed to update security settings:', err);
      setError(err.response?.data?.message || 'Failed to update security settings');
    } finally {
      setLoading(false);
    }
  };

  const SecurityToggle = ({ 
    setting, 
    title, 
    description, 
    icon: Icon, 
    severity = 'medium',
    enabled 
  }) => {
    const severityColors = {
      low: 'text-green-600 bg-green-100',
      medium: 'text-yellow-600 bg-yellow-100',
      high: 'text-red-600 bg-red-100'
    };

    return (
      <div className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
        <div className={`p-2 rounded-lg ${severityColors[severity]}`}>
          <Icon className="h-5 w-5" />
        </div>
        
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
            <button
              onClick={() => handleToggle(setting)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                enabled ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        </div>
      </div>
    );
  };

  const SecurityPreview = () => {
    const enabledFeatures = Object.entries(settings).filter(([_, enabled]) => enabled);
    const securityLevel = enabledFeatures.length >= 6 ? 'High' : 
                         enabledFeatures.length >= 3 ? 'Medium' : 'Low';
    
    const levelColors = {
      High: 'text-red-600 bg-red-100',
      Medium: 'text-yellow-600 bg-yellow-100',
      Low: 'text-green-600 bg-green-100'
    };

    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center space-x-3 mb-3">
          <FiShield className="h-5 w-5 text-gray-600" />
          <h3 className="text-sm font-medium text-gray-900">Security Summary</h3>
        </div>
        
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-sm text-gray-600">Security Level:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${levelColors[securityLevel]}`}>
            {securityLevel}
          </span>
        </div>
        
        <div className="text-sm text-gray-600">
          <p className="mb-2">{enabledFeatures.length} security features enabled:</p>
          <ul className="space-y-1">
            {enabledFeatures.map(([setting, _]) => (
              <li key={setting} className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span className="capitalize">{setting.replace(/_/g, ' ')}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Security Settings</h2>
          <p className="text-sm text-gray-600 mt-1">
            Configure security measures for this competition
          </p>
        </div>
        
        <button
          onClick={handleSave}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading && <LoadingSpinner size="sm" className="mr-2" />}
          <FiSave className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <p className="text-green-800 text-sm">Security settings updated successfully!</p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Security Settings */}
        <div className="lg:col-span-2 space-y-4">
          <SecurityToggle
            setting="proctoring_enabled"
            title="Live Proctoring"
            description="Enable real-time monitoring of participants during the competition"
            icon={FiEye}
            severity="high"
            enabled={settings.proctoring_enabled}
          />

          <SecurityToggle
            setting="randomize_questions"
            title="Randomize Questions"
            description="Present questions in random order for each participant"
            icon={FiShield}
            severity="medium"
            enabled={settings.randomize_questions}
          />

          <SecurityToggle
            setting="cheating_detection"
            title="AI Cheating Detection"
            description="Use AI to detect suspicious behavior patterns"
            icon={FiAlertTriangle}
            severity="high"
            enabled={settings.cheating_detection}
          />

          <SecurityToggle
            setting="tab_switching_detection"
            title="Tab Switching Detection"
            description="Monitor and alert when participants switch browser tabs"
            icon={FiMonitor}
            severity="medium"
            enabled={settings.tab_switching_detection}
          />

          <SecurityToggle
            setting="browser_lockdown"
            title="Browser Lockdown"
            description="Prevent access to other applications during the competition"
            icon={FiLock}
            severity="high"
            enabled={settings.browser_lockdown}
          />

          <SecurityToggle
            setting="time_limit_strict"
            title="Strict Time Limits"
            description="Automatically submit when time expires, no extensions allowed"
            icon={FiAlertTriangle}
            severity="medium"
            enabled={settings.time_limit_strict}
          />

          <SecurityToggle
            setting="copy_paste_disabled"
            title="Disable Copy/Paste"
            description="Prevent copying and pasting text during the competition"
            icon={FiShield}
            severity="low"
            enabled={settings.copy_paste_disabled}
          />

          <SecurityToggle
            setting="screenshot_detection"
            title="Screenshot Detection"
            description="Detect and prevent screenshot attempts"
            icon={FiMonitor}
            severity="medium"
            enabled={settings.screenshot_detection}
          />
        </div>

        {/* Security Preview */}
        <div className="space-y-6">
          <SecurityPreview />
          
          {/* Security Guidelines */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <FiInfo className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-blue-900 mb-2">Security Guidelines</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• High security is recommended for competitive assessments</li>
                  <li>• Test security settings before the competition starts</li>
                  <li>• Inform participants about security measures in advance</li>
                  <li>• Monitor security violations during the competition</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Security Impact */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <FiAlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-yellow-900 mb-2">Important Notes</h3>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• Some features may require participant consent</li>
                  <li>• Browser lockdown may not work on all devices</li>
                  <li>• High security settings may affect user experience</li>
                  <li>• Consider accessibility requirements</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettingsPanel;
