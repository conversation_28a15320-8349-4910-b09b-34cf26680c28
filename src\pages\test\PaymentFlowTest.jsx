/**
 * Payment Flow Test Page
 * 
 * Test page to verify the new WebSocket-based payment flow implementation.
 * This page simulates the complete payment process from ticket selection to completion.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiPlay,
  FiCheck,
  FiX,
  FiWifi,
  FiCreditCard,
  FiArrowLeft,
  FiInfo
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import TicketSelection from '../../components/events/TicketSelection';
import PaymentTracker from '../../components/payment/PaymentTracker';
import paymentService from '../../services/paymentService';
import paymentWebSocketService from '../../services/paymentWebSocketService';

const PaymentFlowTest = () => {
  const navigate = useNavigate();
  const { showSuccess, showError, showInfo } = useNotification();
  
  // Test state
  const [testStep, setTestStep] = useState('selection'); // selection, tracking, complete
  const [testBookingId, setTestBookingId] = useState(null);
  const [testResults, setTestResults] = useState([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  // Mock event data for testing
  const mockEvent = {
    id: 'test-event-123',
    title: 'Test Event - Payment Flow',
    description: 'Testing the new WebSocket payment flow',
    date: new Date().toISOString(),
    location: 'Test Location',
    tickets: [
      {
        id: 'ticket-1',
        name: 'General Admission',
        description: 'Standard ticket for testing',
        price: 100,
        currency: 'ZAR',
        available_quantity: 50,
        max_quantity: 5
      },
      {
        id: 'ticket-2',
        name: 'VIP Pass',
        description: 'Premium ticket for testing',
        price: 250,
        currency: 'ZAR',
        available_quantity: 20,
        max_quantity: 2
      },
      {
        id: 'ticket-3',
        name: 'Free Entry',
        description: 'Free ticket for testing',
        price: 0,
        currency: 'ZAR',
        available_quantity: 100,
        max_quantity: 10
      }
    ]
  };

  /**
   * Add test result
   */
  const addTestResult = (test, status, message) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date().toISOString()
    }]);
  };

  /**
   * Test WebSocket connection
   */
  const testWebSocketConnection = async () => {
    setIsRunningTest(true);
    addTestResult('WebSocket Connection', 'running', 'Testing WebSocket connection...');
    
    try {
      // Test with a mock booking ID
      const mockBookingId = 'test-booking-' + Date.now();
      
      // Setup test listeners
      const testPromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        paymentWebSocketService.on('connected', () => {
          clearTimeout(timeout);
          resolve('Connected successfully');
        });

        paymentWebSocketService.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      // Attempt connection
      await paymentWebSocketService.connect(mockBookingId);
      const result = await testPromise;
      
      addTestResult('WebSocket Connection', 'success', result);
      paymentWebSocketService.disconnect();
      
    } catch (error) {
      addTestResult('WebSocket Connection', 'error', error.message);
    }
    
    setIsRunningTest(false);
  };

  /**
   * Test API endpoints
   */
  const testApiEndpoints = async () => {
    setIsRunningTest(true);
    
    // Test booking creation
    addTestResult('API Endpoints', 'running', 'Testing booking creation...');
    
    try {
      const mockBookingData = {
        event_id: mockEvent.id,
        ticket_id: mockEvent.tickets[0].id,
        quantity: 1,
        attendee_info: {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+27123456789'
        }
      };

      // This will likely fail since we're testing, but we can check the request format
      try {
        await paymentService.createEventBooking(mockBookingData);
        addTestResult('API Endpoints', 'success', 'Booking creation successful');
      } catch (error) {
        // Expected to fail in test environment
        if (error.message.includes('Network Error') || error.message.includes('404')) {
          addTestResult('API Endpoints', 'warning', 'API endpoint not available (expected in test)');
        } else {
          addTestResult('API Endpoints', 'error', error.message);
        }
      }
      
    } catch (error) {
      addTestResult('API Endpoints', 'error', error.message);
    }
    
    setIsRunningTest(false);
  };

  /**
   * Test payment tracker component
   */
  const testPaymentTracker = () => {
    setTestStep('tracking');
    setTestBookingId('test-booking-' + Date.now());
    addTestResult('Payment Tracker', 'success', 'Payment tracker component loaded');
  };

  /**
   * Handle booking success
   */
  const handleBookingSuccess = (bookingData) => {
    addTestResult('Booking Flow', 'success', 'Booking completed successfully');
    showSuccess('Test booking completed!');
  };

  /**
   * Handle booking error
   */
  const handleBookingError = (error) => {
    addTestResult('Booking Flow', 'error', error.message);
    showError('Test booking failed: ' + error.message);
  };

  /**
   * Reset test
   */
  const resetTest = () => {
    setTestStep('selection');
    setTestBookingId(null);
    setTestResults([]);
    paymentWebSocketService.disconnect();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Flow Test
          </h1>
          <p className="text-gray-600">
            Test the new WebSocket-based payment flow implementation
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="space-y-6">
            {/* Test Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Actions</h2>
              
              <div className="space-y-3">
                <button
                  onClick={testWebSocketConnection}
                  disabled={isRunningTest}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  <FiWifi className="w-4 h-4 mr-2" />
                  Test WebSocket Connection
                </button>
                
                <button
                  onClick={testApiEndpoints}
                  disabled={isRunningTest}
                  className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  <FiPlay className="w-4 h-4 mr-2" />
                  Test API Endpoints
                </button>
                
                <button
                  onClick={testPaymentTracker}
                  className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  <FiCreditCard className="w-4 h-4 mr-2" />
                  Test Payment Tracker
                </button>
                
                <button
                  onClick={resetTest}
                  className="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Reset Test
                </button>
              </div>
            </div>

            {/* Test Results */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h2>
              
              {testResults.length === 0 ? (
                <p className="text-gray-500 text-sm">No tests run yet</p>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50">
                      <div className="flex-shrink-0 mt-0.5">
                        {result.status === 'success' && <FiCheck className="w-4 h-4 text-green-500" />}
                        {result.status === 'error' && <FiX className="w-4 h-4 text-red-500" />}
                        {result.status === 'warning' && <FiInfo className="w-4 h-4 text-orange-500" />}
                        {result.status === 'running' && <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">{result.test}</p>
                        <p className="text-sm text-gray-600">{result.message}</p>
                        <p className="text-xs text-gray-400">{new Date(result.timestamp).toLocaleTimeString()}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Test Components */}
          <div className="space-y-6">
            {testStep === 'selection' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Ticket Selection Test</h2>
                <TicketSelection
                  event={mockEvent}
                  onBookingSuccess={handleBookingSuccess}
                  onBookingError={handleBookingError}
                />
              </div>
            )}

            {testStep === 'tracking' && testBookingId && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Tracker Test</h2>
                <PaymentTracker
                  bookingId={testBookingId}
                  onPaymentSuccess={(data) => {
                    addTestResult('Payment Tracker', 'success', 'Payment success event received');
                    setTestStep('complete');
                  }}
                  onPaymentFailure={(data) => {
                    addTestResult('Payment Tracker', 'error', 'Payment failure event received');
                  }}
                  onTimeout={() => {
                    addTestResult('Payment Tracker', 'warning', 'Payment timeout event received');
                  }}
                  autoRedirect={false}
                />
              </div>
            )}

            {testStep === 'complete' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <FiCheck className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h2 className="text-lg font-semibold text-gray-900 mb-2">Test Complete</h2>
                <p className="text-gray-600 mb-4">Payment flow test completed successfully!</p>
                <button
                  onClick={resetTest}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Run Another Test
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFlowTest;
