import React from 'react';
import {
  <PERSON>Book<PERSON>pen,
  FiClock,
  FiUsers,
  FiEdit,
  FiTrash2,
  FiCopy,
  FiEye,
  FiInfo,
  FiStar,
  FiCalendar
} from 'react-icons/fi';

const ExamCard = ({ 
  exam, 
  onEdit, 
  onDelete, 
  onCopy, 
  onView, 
  onViewUsage,
  showActions = true,
  variant = 'default' // default, compact, selection
}) => {
  const getDifficultyColor = (level) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      draft: 'bg-gray-100 text-gray-800',
      archived: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (variant === 'compact') {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">{exam.title}</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
              <span>{exam.questions_count} questions</span>
              <span>{formatDuration(exam.duration_minutes)}</span>
              <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(exam.difficulty_level)}`}>
                {exam.difficulty_level}
              </span>
            </div>
          </div>
          {showActions && (
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={() => onView(exam)}
                className="p-2 text-gray-400 hover:text-gray-600"
                title="View Exam"
              >
                <FiEye className="h-4 w-4" />
              </button>
              <button
                onClick={() => onEdit(exam)}
                className="p-2 text-blue-400 hover:text-blue-600"
                title="Edit Exam"
              >
                <FiEdit className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{exam.title}</h3>
            <p className="text-sm text-gray-600 line-clamp-2">{exam.description}</p>
          </div>
          {exam.rating && (
            <div className="flex items-center space-x-1 ml-4">
              <FiStar className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium text-gray-700">{exam.rating}</span>
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <FiBookOpen className="h-4 w-4 text-gray-400" />
            <span>{exam.questions_count} questions</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <FiClock className="h-4 w-4 text-gray-400" />
            <span>{formatDuration(exam.duration_minutes)}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <FiUsers className="h-4 w-4 text-gray-400" />
            <span>Used {exam.usage_count || 0} times</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <FiCalendar className="h-4 w-4 text-gray-400" />
            <span>{formatDate(exam.updated_at || exam.created_at)}</span>
          </div>
        </div>

        {/* Status and Difficulty */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exam.difficulty_level)}`}>
              {exam.difficulty_level}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(exam.status)}`}>
              {exam.status}
            </span>
          </div>
          {exam.last_used && (
            <span className="text-xs text-gray-500">
              Last used: {formatDate(exam.last_used)}
            </span>
          )}
        </div>

        {/* Tags */}
        {exam.tags && exam.tags.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {exam.tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                  {tag}
                </span>
              ))}
              {exam.tags.length > 3 && (
                <span className="text-xs text-gray-500">+{exam.tags.length - 3} more</span>
              )}
            </div>
          </div>
        )}

        {/* Usage Warning */}
        {exam.usage_count > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <FiInfo className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                This exam is being used in {exam.usage_count} competition(s)
              </span>
            </div>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onView(exam)}
              className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <FiEye className="h-4 w-4 inline mr-1" />
              View
            </button>
            
            <button
              onClick={() => onEdit(exam)}
              className="flex-1 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <FiEdit className="h-4 w-4 inline mr-1" />
              Edit
            </button>
            
            <button
              onClick={() => onViewUsage(exam)}
              className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
              title="View Usage Info"
            >
              <FiInfo className="h-4 w-4" />
            </button>
            
            <button
              onClick={() => onCopy(exam)}
              className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
              title="Copy Exam"
            >
              <FiCopy className="h-4 w-4" />
            </button>
            
            <button
              onClick={() => onDelete(exam)}
              className="p-2 text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 rounded"
              title="Delete Exam"
            >
              <FiTrash2 className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Footer with Creator Info */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Created by {exam.created_by || 'Unknown'}</span>
          <span>ID: {exam.id}</span>
        </div>
      </div>
    </div>
  );
};

export default ExamCard;
