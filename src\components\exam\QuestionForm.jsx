import React from 'react';
import { FiPlus, FiZap, FiClock, FiCheck, FiX, FiAlertCircle, FiInfo } from 'react-icons/fi';

const QuestionForm = ({
  questionType,
  setQuestionType,
  descType,
  setDescType,
  questionForm,
  onQuestionChange,
  onOptionChange,
  onAddQuestion,
  onAIGenerate,
  subjects = [],
  subjectId,
  setSubjectId,
  chaptersBySubject,
  topicsByChapter,
  subtopicsByTopic,
  chapterId,
  topicId,
  subtopicId,
  setChapterId,
  setTopicId,
  setSubtopicId,
  chaptersLoading,
  topicsLoading,
  subtopicsLoading,
  themeClasses,
  isSubmitting,
  userType = 'teacher', // Default to teacher for backward compatibility
  // AI Generation options
  aiNoOfQuestions,
  setAiNoOfQuestions,
  aiDifficultyMode,
  setAiDifficultyMode,
  aiNoOfEasy,
  setAiNoOfEasy,
  aiNoOfMedium,
  setAiNoOfMedium,
  aiNoOfHard,
  setAiNoOfHard,
  // Manual question creation fields
  gradeClasses = [],
  classNumber,
  classNumberId,
  onClassNumberChange

}) => {


  return (
    <div className="space-y-6">
      {/* Institute-specific note */}
      {userType === 'institute' && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-700 rounded-xl p-6">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0 w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
              <FiPlus className="w-5 h-5 text-white" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
                Add Questions to Your Question Bank
              </h4>
              <p className="text-green-700 dark:text-green-200 text-sm leading-relaxed">
                Create categorized questions by subject, chapter, and topic that can be reused across multiple competitions.
                Use AI generation or add questions manually with proper categorization.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Question Type Selection */}
      <div className="flex gap-4 mb-6">
        <button
          type="button"
          onClick={() => setQuestionType("MCQS")}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            questionType === "MCQS"
              ? "bg-blue-600 text-white shadow-lg"
              : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
          }`}
        >
          Multiple Choice
        </button>
        <button
          type="button"
          onClick={() => setQuestionType("DESCRIPTIVE")}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            questionType === "DESCRIPTIVE"
              ? "bg-blue-600 text-white shadow-lg"
              : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
          }`}
        >
          Descriptive
        </button>
      </div>

      {/* AI Generation Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-700">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-4 flex items-center gap-2">
          <FiZap className="w-5 h-5" />
          AI Question Generation
        </h3>



        {/* AI Generation Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              Number of Questions <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={aiNoOfQuestions}
              onChange={e => setAiNoOfQuestions(parseInt(e.target.value) || 1)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              required
            />
          </div>
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              Difficulty Mode
            </label>
            <select
              value={aiDifficultyMode}
              onChange={e => setAiDifficultyMode(e.target.value)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
            >
              <option value="mix">Mixed (Default)</option>
              <option value="custom">Custom Distribution</option>
            </select>
          </div>
        </div>

        {/* Custom Difficulty Distribution */}
        {aiDifficultyMode === "custom" && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Easy Questions
              </label>
              <input
                type="number"
                min="0"
                value={aiNoOfEasy}
                onChange={e => setAiNoOfEasy(parseInt(e.target.value) || 0)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Medium Questions
              </label>
              <input
                type="number"
                min="0"
                value={aiNoOfMedium}
                onChange={e => setAiNoOfMedium(parseInt(e.target.value) || 0)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Hard Questions
              </label>
              <input
                type="number"
                min="0"
                value={aiNoOfHard}
                onChange={e => setAiNoOfHard(parseInt(e.target.value) || 0)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div className="md:col-span-3">
              <p className={`text-sm ${themeClasses.label}`}>
                Total: {aiNoOfEasy + aiNoOfMedium + aiNoOfHard} questions
                {(aiNoOfEasy + aiNoOfMedium + aiNoOfHard) !== aiNoOfQuestions && (
                  <span className="text-red-500 ml-2">
                    (Should equal {aiNoOfQuestions})
                  </span>
                )}
              </p>
            </div>
          </div>
        )}

        {/* Subject/Chapter/Topic/Subtopic Selection - For Both Teachers and Institutes */}
        <div className="space-y-4 mb-6">
          {/* Subject Selection */}
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              Subject <span className="text-red-500">*</span>
            </label>
            <select
              value={subjectId}
              onChange={e => setSubjectId(e.target.value)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              required
            >
              <option value="">Select Subject</option>
              {subjects && subjects.map(subject => (
                <option key={subject.id} value={subject.id}>{subject.name}</option>
              ))}
            </select>
          </div>

          {/* Chapter/Topic/Subtopic Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Chapter <span className="text-red-500">*</span>
              </label>
              <select
                value={chapterId}
                onChange={e => setChapterId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                required
                disabled={chaptersLoading || !subjectId}
              >
                <option value="">{chaptersLoading ? "Loading..." : subjectId ? "Select Chapter" : "Select Subject First"}</option>
                {chaptersBySubject && chaptersBySubject.map(opt => (
                  <option key={opt.id} value={opt.id}>{opt.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Topic (optional)</label>
              <select
                value={topicId}
                onChange={e => setTopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={topicsLoading || !chapterId}
              >
                <option value="">{topicsLoading ? "Loading..." : chapterId ? "Select Topic" : "Select Chapter First"}</option>
                {topicsByChapter && topicsByChapter.map(opt => (
                  <option key={opt.id} value={opt.id}>{opt.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Subtopic (optional)</label>
              <select
                value={subtopicId}
                onChange={e => setSubtopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={subtopicsLoading || !topicId}
              >
                <option value="">{subtopicsLoading ? "Loading..." : topicId ? "Select Subtopic" : "Select Topic First"}</option>
                {subtopicsByTopic && subtopicsByTopic.map(opt => (
                  <option key={opt.id} value={opt.id}>{opt.name}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <button
          type="button"
          onClick={onAIGenerate}
          disabled={!subjectId || !chapterId || isSubmitting || (aiDifficultyMode === "custom" && (aiNoOfEasy + aiNoOfMedium + aiNoOfHard) !== aiNoOfQuestions)}
          className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2"
        >
          <FiZap className="w-4 h-4" />
          {isSubmitting ? "Generating..." : `Generate ${aiNoOfQuestions} AI Questions`}
        </button>
      </div>

      {/* Manual Question Creation */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <FiPlus className="w-5 h-5" />
          Add Question Manually
        </h3>

        {/* Descriptive Type Selection */}
        {questionType === "DESCRIPTIVE" && (
          <div className="mb-4">
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>Descriptive Type</label>
            <select
              value={descType}
              onChange={e => setDescType(e.target.value)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              required
            >
              <option value="SHORT">Short Answer</option>
              <option value="LONG">Long Answer</option>
            </select>
          </div>
        )}

        <form onSubmit={onAddQuestion} className="space-y-6">
          {/* Subject/Chapter/Topic/Subtopic and Class Number Selection for Manual Questions */}
          <div className="space-y-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            {/* Subject Selection */}
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Subject <span className="text-red-500">*</span>
              </label>
              <select
                value={subjectId}
                onChange={e => setSubjectId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                required
              >
                <option value="">Select Subject</option>
                {subjects && subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>{subject.name}</option>
                ))}
              </select>
            </div>

            {/* Chapter/Topic/Subtopic and Class Number Grid */}
            <div className={`grid grid-cols-1 gap-4 ${userType === 'teacher' ? 'md:grid-cols-2 lg:grid-cols-4' : 'md:grid-cols-3'}`}>
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                  Chapter <span className="text-red-500">*</span>
                </label>
                <select
                  value={chapterId}
                  onChange={e => setChapterId(e.target.value)}
                  className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                  required
                  disabled={chaptersLoading || !subjectId}
                >
                  <option value="">{chaptersLoading ? "Loading..." : subjectId ? "Select Chapter" : "Select Subject First"}</option>
                  {chaptersBySubject && chaptersBySubject.map(chapter => (
                    <option key={chapter.id} value={chapter.id}>{chapter.name}</option>
                  ))}
                </select>
                {chaptersLoading && <p className="text-xs text-gray-500 mt-1">Loading chapters...</p>}
              </div>

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Topic <span className="text-gray-400">(optional)</span>
              </label>
              <select
                value={topicId}
                onChange={e => setTopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={!chapterId || topicsLoading}
              >
                <option value="">{topicsLoading ? "Loading..." : chapterId ? "Select Topic (Optional)" : "Select Chapter First"}</option>
                {topicsByChapter && topicsByChapter.map(topic => (
                  <option key={topic.id} value={topic.id}>{topic.name}</option>
                ))}
              </select>
              {topicsLoading && <p className="text-xs text-gray-500 mt-1">Loading topics...</p>}
            </div>

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Subtopic <span className="text-gray-400">(optional)</span>
              </label>
              <select
                value={subtopicId}
                onChange={e => setSubtopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={subtopicsLoading || !chapterId}
              >
                <option value="">{subtopicsLoading ? "Loading..." : chapterId ? "Select Subtopic (Optional)" : "Select Chapter First"}</option>
                {subtopicsByTopic && subtopicsByTopic.map(subtopic => (
                  <option key={subtopic.id} value={subtopic.id}>{subtopic.name}</option>
                ))}
              </select>
              {subtopicsLoading && <p className="text-xs text-gray-500 mt-1">Loading subtopics...</p>}
            </div>

            {/* Class Number - Only for Teachers */}
            {userType === 'teacher' && (
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                  Class Number <span className="text-red-500">*</span>
                </label>
                <select
                  value={classNumber}
                  onChange={onClassNumberChange}
                  className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                  required
                >
                  <option value="">Select Class Number</option>
                  {gradeClasses && gradeClasses.map(cls => (
                    <option key={cls.id} value={cls.ClassNo}>
                      {cls.ClassNo}
                    </option>
                  ))}
                </select>
              </div>
            )}
            </div>
          </div>

          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>Question Text <span className="text-red-500">*</span></label>
            <textarea
              name="text"
              value={questionForm.text}
              onChange={onQuestionChange}
              rows={3}
              className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
              placeholder="Enter your question here..."
              required
            />
          </div>

          {questionType === "MCQS" && (
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Options</label>
              <div className="space-y-3">
                {questionForm.options.map((opt, idx) => (
                  <div key={idx} className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <span className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-sm font-medium">
                      {String.fromCharCode(65 + idx)}
                    </span>
                    <input
                      type="text"
                      value={opt.option_text}
                      onChange={(e) => onOptionChange(idx, "option_text", e.target.value)}
                      className={`flex-1 rounded-lg px-3 py-2 border focus:outline-none focus:ring-2 focus:ring-blue-500 ${themeClasses.input}`}
                      placeholder={`Option ${idx + 1}`}
                      required
                    />
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="correct_option"
                        checked={opt.is_correct}
                        onChange={() => onOptionChange(idx, "is_correct", true)}
                        className="text-blue-600"
                      />
                      <span className="text-sm text-gray-600 dark:text-gray-400">Correct</span>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              {questionType === "MCQS" ? "Answer Explanation (optional)" : "Expected Answer"}
            </label>
            <textarea
              name="answer"
              value={questionForm.answer}
              onChange={onQuestionChange}
              rows={2}
              className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
              placeholder={questionType === "MCQS" ? "Provide explanation for the correct answer..." : "Enter the expected answer..."}
            />
          </div>

          <div className="flex items-center gap-4">
            <div className="flex-1">
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Marks</label>
              <input
                type="number"
                name="marks"
                value={questionForm.marks}
                onChange={onQuestionChange}
                min={1}
                max={100}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div className="flex-1">
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Difficulty Level</label>
              <select
                name="Level"
                value={questionForm.Level}
                onChange={onQuestionChange}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              >
                <option value="EASY">Easy</option>
                <option value="MEDIUM">Medium</option>
                <option value="HARD">Hard</option>
              </select>
            </div>
          </div>

          <button
            type="submit"
            disabled={isSubmitting || !subjectId || !chapterId || !questionForm.text.trim() || (userType === 'teacher' && (!classNumber || !classNumberId))}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2"
          >
            <FiPlus className="w-4 h-4" />
            {isSubmitting ? "Adding..." : "Add Question Manually"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default QuestionForm;
