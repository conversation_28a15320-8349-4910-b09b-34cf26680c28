# 🔧 Backend PayFast Implementation Requirements

## Overview
The frontend MVP payment flow has been implemented and is ready for backend integration. The backend needs to implement the PayFast endpoints that the frontend expects.

## 🚨 Current Status
- ✅ **Frontend MVP Implementation**: Complete and ready
- ❌ **Backend PayFast Endpoints**: Not yet implemented
- 🔄 **Mock Implementation**: Frontend includes mock responses for testing

## 📋 Required Backend Endpoints

### 1. Create Payment Link
```
POST /api/payfast/create-payment
```

**Request Body:**
```json
{
  "ticket_id": "string (UUID or identifier)"
}
```

**Response (Success):**
```json
{
  "success": true,
  "payment_required": true,
  "payment_url": "https://sandbox.payfast.co.za/eng/process?...",
  "ticket_id": "ticket-123",
  "ticket_name": "Event Ticket Name",
  "amount": 150.00,
  "currency": "ZAR",
  "merchant_id": "10000100",
  "merchant_key": "46f0cd694581a",
  "message": "Payment link created successfully"
}
```

**Response (Free Ticket):**
```json
{
  "success": true,
  "payment_required": false,
  "ticket_id": "ticket-123",
  "ticket_name": "Free Event Ticket",
  "amount": 0,
  "currency": "ZAR",
  "status": "confirmed",
  "message": "Free ticket confirmed successfully"
}
```

**Error Responses:**
- `400`: Invalid ticket ID
- `401`: Authentication required
- `403`: Not authorized to purchase this ticket
- `404`: Ticket not found or no longer available
- `422`: Validation error

### 2. Check Ticket Status
```
GET /api/tickets/{ticket_id}/status
```

**Response:**
```json
{
  "ticket_id": "ticket-123",
  "status": "confirmed|pending|failed",
  "ticket": {
    "id": "ticket-123",
    "name": "Event Ticket Name",
    "price": 150.00,
    "currency": "ZAR"
  },
  "event": {
    "id": "event-456",
    "title": "Event Name",
    "date": "2024-12-01T18:00:00Z",
    "location": "Event Venue"
  },
  "payment": {
    "payment_id": "payment-789",
    "amount": 150.00,
    "currency": "ZAR",
    "date": "2024-11-01T10:30:00Z"
  },
  "message": "Ticket status retrieved successfully"
}
```

### 3. PayFast Webhook Handler
```
POST /api/payfast/webhook
```

**Purpose:** Receive PayFast payment notifications and update ticket status

**PayFast Webhook Data:**
```
m_payment_id=12345
pf_payment_id=67890
payment_status=COMPLETE
item_name=Event Ticket
amount_gross=150.00
amount_fee=3.45
amount_net=146.55
custom_str1=ticket-123
signature=generated_signature
```

**Response:**
```
HTTP 200 OK
```

## 🔧 Implementation Details

### PayFast Configuration
```python
# PayFast Settings (Sandbox)
PAYFAST_MERCHANT_ID = "10000100"
PAYFAST_MERCHANT_KEY = "46f0cd694581a"
PAYFAST_PASSPHRASE = "your_passphrase"  # Optional but recommended
PAYFAST_SANDBOX = True  # Set to False for production

# URLs
PAYFAST_SANDBOX_URL = "https://sandbox.payfast.co.za/eng/process"
PAYFAST_PRODUCTION_URL = "https://www.payfast.co.za/eng/process"
```

### Payment Link Generation
```python
def create_payment_link(ticket_id, user_id):
    # 1. Validate ticket exists and is available
    ticket = get_ticket_by_id(ticket_id)
    if not ticket:
        raise TicketNotFoundError()
    
    # 2. Check if ticket is free
    if ticket.price == 0:
        # Create confirmed booking for free ticket
        booking = create_free_ticket_booking(ticket, user_id)
        return {
            "success": True,
            "payment_required": False,
            "status": "confirmed",
            "ticket_id": ticket_id,
            "amount": 0
        }
    
    # 3. Generate PayFast payment data
    payment_data = {
        "merchant_id": PAYFAST_MERCHANT_ID,
        "merchant_key": PAYFAST_MERCHANT_KEY,
        "return_url": f"{FRONTEND_URL}/payment/success?ticket_id={ticket_id}",
        "cancel_url": f"{FRONTEND_URL}/payment/cancel?ticket_id={ticket_id}",
        "notify_url": f"{BACKEND_URL}/api/payfast/webhook",
        "amount": f"{ticket.price:.2f}",
        "item_name": ticket.name,
        "custom_str1": ticket_id,  # For tracking
        "custom_str2": user_id     # For user identification
    }
    
    # 4. Generate signature
    signature = generate_payfast_signature(payment_data)
    payment_data["signature"] = signature
    
    # 5. Create payment URL
    payment_url = f"{PAYFAST_URL}?" + urlencode(payment_data)
    
    # 6. Create pending booking record
    booking = create_pending_booking(ticket, user_id)
    
    return {
        "success": True,
        "payment_required": True,
        "payment_url": payment_url,
        "ticket_id": ticket_id,
        "amount": ticket.price
    }
```

### Webhook Handler
```python
def handle_payfast_webhook(request):
    # 1. Validate PayFast signature
    if not validate_payfast_signature(request.data):
        return {"error": "Invalid signature"}, 400
    
    # 2. Extract payment data
    payment_data = request.data
    ticket_id = payment_data.get("custom_str1")
    user_id = payment_data.get("custom_str2")
    payment_status = payment_data.get("payment_status")
    
    # 3. Update booking status
    booking = get_booking_by_ticket_and_user(ticket_id, user_id)
    if not booking:
        return {"error": "Booking not found"}, 404
    
    if payment_status == "COMPLETE":
        booking.status = "confirmed"
        booking.payment_reference = payment_data.get("pf_payment_id")
        booking.payment_date = datetime.now()
        
        # Send confirmation email
        send_ticket_confirmation_email(booking)
        
    else:
        booking.status = "failed"
        booking.failure_reason = f"Payment status: {payment_status}"
    
    # 4. Save booking
    save_booking(booking)
    
    return {"status": "success"}, 200
```

## 🗄️ Database Schema Requirements

### Bookings Table
```sql
CREATE TABLE bookings (
    id UUID PRIMARY KEY,
    ticket_id VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL,
    status VARCHAR(50) NOT NULL, -- pending, confirmed, failed, cancelled
    payment_reference VARCHAR(255),
    payment_date TIMESTAMP,
    failure_reason TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Tickets Table (if not exists)
```sql
CREATE TABLE tickets (
    id VARCHAR(255) PRIMARY KEY,
    event_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    available_quantity INTEGER,
    max_quantity_per_user INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active', -- active, sold_out, inactive
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔐 Security Considerations

### 1. Signature Validation
```python
def validate_payfast_signature(data):
    # Remove signature from data
    signature = data.pop('signature', None)
    
    # Create parameter string
    param_string = '&'.join([f"{k}={v}" for k, v in sorted(data.items())])
    
    # Add passphrase if configured
    if PAYFAST_PASSPHRASE:
        param_string += f"&passphrase={PAYFAST_PASSPHRASE}"
    
    # Generate MD5 hash
    expected_signature = hashlib.md5(param_string.encode()).hexdigest()
    
    return signature == expected_signature
```

### 2. Authentication
- All endpoints require valid JWT token
- Validate user permissions for ticket purchase
- Rate limiting on payment creation

### 3. Data Validation
- Validate ticket availability before payment
- Check user purchase limits
- Prevent duplicate bookings

## 🧪 Testing

### Test Cases
1. **Free Ticket Purchase**
   - Should create confirmed booking immediately
   - No PayFast redirect required

2. **Paid Ticket Purchase**
   - Should generate valid PayFast URL
   - Should create pending booking

3. **Payment Success Webhook**
   - Should confirm booking
   - Should send confirmation email

4. **Payment Failure Webhook**
   - Should mark booking as failed
   - Should allow retry

5. **Status Check**
   - Should return current booking status
   - Should include event and payment details

### Mock PayFast Webhook for Testing
```bash
curl -X POST http://localhost:8000/api/payfast/webhook \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "m_payment_id=12345&pf_payment_id=67890&payment_status=COMPLETE&item_name=Test+Ticket&amount_gross=150.00&custom_str1=ticket-123&custom_str2=user-456&signature=mock_signature"
```

## 🚀 Deployment Checklist

### Environment Variables
```env
PAYFAST_MERCHANT_ID=10000100
PAYFAST_MERCHANT_KEY=46f0cd694581a
PAYFAST_PASSPHRASE=your_secure_passphrase
PAYFAST_SANDBOX=true
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000
```

### Production Settings
- Set `PAYFAST_SANDBOX=false`
- Use production PayFast credentials
- Configure proper SSL certificates
- Set up monitoring for webhook failures

## 📞 Frontend Integration

Once backend endpoints are implemented:

1. **Remove Mock Implementation**
   - Frontend will automatically detect real endpoints
   - Mock responses will be bypassed

2. **Test Integration**
   - Use frontend test page: `/test/mvp-payment`
   - Verify payment flow end-to-end

3. **Error Handling**
   - Frontend already handles all expected error codes
   - Proper user feedback for all scenarios

## 🎯 Success Criteria

✅ **Backend Implementation Complete When:**
- All three endpoints return expected responses
- PayFast webhook properly updates booking status
- Free tickets work without payment
- Paid tickets redirect to PayFast correctly
- Status checking returns accurate information
- Frontend test page works end-to-end

The frontend MVP implementation is ready and waiting for these backend endpoints!
