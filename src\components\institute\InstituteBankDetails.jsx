/**
 * InstituteBankDetails Component
 * 
 * Interface for institutes to manage bank account details for payouts
 */

import React, { useState, useEffect } from 'react';
import {
  FiCreditCard,
  FiSave,
  FiEdit,
  FiEye,
  FiEyeOff,
  FiShield,
  FiCheck,
  FiAlertCircle,
  FiRefreshCw
} from 'react-icons/fi';
import { InstitutePayoutService } from '../../services/institutePayoutService';
import { LoadingSpinner, ErrorMessage } from '../ui';
import { useNotification } from '../../contexts/NotificationContext';

const InstituteBankDetails = () => {
  const { showSuccess, showError } = useNotification();
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [bankDetails, setBankDetails] = useState(null);
  const [formData, setFormData] = useState({
    bank_account_number: '',
    bank_name: '',
    account_holder_name: '',
    jazzcash_number: ''
  });
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [showAccountNumber, setShowAccountNumber] = useState(false);

  useEffect(() => {
    loadBankDetails();
  }, []);

  const loadBankDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await InstitutePayoutService.getBankDetails();
      setBankDetails(data);
      
      // Initialize form data with existing details
      if (data) {
        setFormData({
          bank_account_number: data.bank_account_number || '',
          bank_name: data.bank_name || '',
          account_holder_name: data.account_holder_name || '',
          jazzcash_number: data.jazzcash_number || ''
        });
      }
    } catch (err) {
      setError(err.message || 'Failed to load bank details');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const validation = InstitutePayoutService.validateBankDetails(formData);
    
    if (!validation.isValid) {
      const errors = {};
      validation.errors.forEach(error => {
        if (error.includes('account number')) {
          errors.bank_account_number = error;
        } else if (error.includes('bank name')) {
          errors.bank_name = error;
        } else if (error.includes('account holder')) {
          errors.account_holder_name = error;
        } else if (error.includes('JazzCash')) {
          errors.jazzcash_number = error;
        }
      });
      setValidationErrors(errors);
      return false;
    }
    
    setValidationErrors({});
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showError('Please fix the validation errors');
      return;
    }

    setSaving(true);
    
    try {
      await InstitutePayoutService.updateBankDetails(formData);
      await loadBankDetails(); // Reload to get updated data
      setEditing(false);
      showSuccess('Bank details updated successfully');
    } catch (err) {
      showError(err.message || 'Failed to update bank details');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset form data to original values
    if (bankDetails) {
      setFormData({
        bank_account_number: bankDetails.bank_account_number || '',
        bank_name: bankDetails.bank_name || '',
        account_holder_name: bankDetails.account_holder_name || '',
        jazzcash_number: bankDetails.jazzcash_number || ''
      });
    }
    setEditing(false);
    setValidationErrors({});
  };

  const formatAccountNumber = (accountNumber) => {
    if (!accountNumber) return '';
    return InstitutePayoutService.maskAccountNumber(accountNumber);
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadBankDetails} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Bank Account Details</h1>
          <p className="text-gray-600">Manage your bank account information for payouts</p>
        </div>
        {!editing && (
          <button
            onClick={() => setEditing(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiEdit className="w-4 h-4 mr-2" />
            Edit Details
          </button>
        )}
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <FiShield className="h-5 w-5 text-blue-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Secure Information
            </h3>
            <p className="mt-1 text-sm text-blue-700">
              Your bank account details are encrypted and securely stored. Only authorized personnel can access this information for payout processing.
            </p>
          </div>
        </div>
      </div>

      {/* Bank Details Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Bank Account Information</h2>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Account Holder Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Account Holder Name *
              </label>
              {editing ? (
                <input
                  type="text"
                  value={formData.account_holder_name}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.account_holder_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter account holder name"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                  {bankDetails?.account_holder_name || 'Not provided'}
                </div>
              )}
              {validationErrors.account_holder_name && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.account_holder_name}</p>
              )}
            </div>

            {/* Bank Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Name *
              </label>
              {editing ? (
                <input
                  type="text"
                  value={formData.bank_name}
                  onChange={(e) => handleInputChange('bank_name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.bank_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter bank name"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                  {bankDetails?.bank_name || 'Not provided'}
                </div>
              )}
              {validationErrors.bank_name && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.bank_name}</p>
              )}
            </div>

            {/* Account Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Account Number *
              </label>
              {editing ? (
                <input
                  type="text"
                  value={formData.bank_account_number}
                  onChange={(e) => handleInputChange('bank_account_number', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.bank_account_number ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter account number"
                />
              ) : (
                <div className="flex items-center">
                  <div className="flex-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                    {showAccountNumber 
                      ? bankDetails?.bank_account_number || 'Not provided'
                      : formatAccountNumber(bankDetails?.bank_account_number) || 'Not provided'
                    }
                  </div>
                  {bankDetails?.bank_account_number && (
                    <button
                      onClick={() => setShowAccountNumber(!showAccountNumber)}
                      className="ml-2 p-2 text-gray-400 hover:text-gray-600"
                    >
                      {showAccountNumber ? <FiEyeOff className="w-4 h-4" /> : <FiEye className="w-4 h-4" />}
                    </button>
                  )}
                </div>
              )}
              {validationErrors.bank_account_number && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.bank_account_number}</p>
              )}
            </div>

            {/* JazzCash Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                JazzCash Number (Optional)
              </label>
              {editing ? (
                <input
                  type="text"
                  value={formData.jazzcash_number}
                  onChange={(e) => handleInputChange('jazzcash_number', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.jazzcash_number ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="+92XXXXXXXXXX"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                  {bankDetails?.jazzcash_number || 'Not provided'}
                </div>
              )}
              {validationErrors.jazzcash_number && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.jazzcash_number}</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          {editing && (
            <div className="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={saving}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {saving ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <FiSave className="w-4 h-4 mr-2" />
                )}
                Save Changes
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Payout Method Preference */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Payout Preferences</h2>
        </div>
        
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Preferred Payout Method</h3>
              <p className="text-sm text-gray-600">
                {bankDetails?.preferred_payout_method === 'BANK_TRANSFER' ? 'Bank Transfer' : 'Not set'}
              </p>
            </div>
            <div className="flex items-center">
              {bankDetails?.has_bank_details ? (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <FiCheck className="w-3 h-3 mr-1" />
                  Configured
                </span>
              ) : (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <FiAlertCircle className="w-3 h-3 mr-1" />
                  Incomplete
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstituteBankDetails;
