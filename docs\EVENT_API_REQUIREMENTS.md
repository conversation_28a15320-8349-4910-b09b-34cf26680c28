# 🎫 Event Ticketing API Requirements

## 🚨 Current Status
**Frontend updated to use real API endpoints based on MVP specification.**

Frontend components now call:
- ✅ `GET /api/events` - Browse events
- ✅ `POST /api/buy-ticket` - Purchase tickets
- ✅ `GET /api/booking-status/{id}` - Check booking status
- ✅ `GET /api/my-bookings` - User's bookings

**Backend team needs to implement these endpoints.**

## 🎯 Required API Endpoints

### **1. Browse Events (Public)**
```http
GET /api/events
```
**Response:**
```json
{
  "events": [
    {
      "id": "uuid",
      "title": "Workshop Title",
      "description": "Event description",
      "short_description": "Brief description",
      "banner_image_url": "https://...",
      "start_datetime": "2025-09-09T22:34:06+05:00",
      "end_datetime": "2025-09-10T00:34:06+05:00",
      "location": "Main Conference Room",
      "category": "WORKSHOP",
      "status": "PUBLISHED",
      "max_attendees": 30,
      "total_registrations": 5,
      "tickets": [
        {
          "id": "uuid",
          "name": "VIP Pass",
          "description": "Premium access",
          "price": 399.00,
          "currency": "PKR",
          "available_quantity": 25,
          "status": "ACTIVE"
        }
      ]
    }
  ]
}
```

### **2. Buy Ticket (Authenticated)**
```http
POST /api/events/register
Authorization: Bearer {token}
```
**Request:**
```json
{
  "event_id": "uuid",
  "ticket_id": "uuid", 
  "quantity": 1,
  "attendee_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+************",
    "special_requirements": "Vegetarian meal"
  }
}
```
**Response:**
```json
{
  "success": true,
  "registration_id": "reg_123456",
  "event_id": "uuid",
  "ticket_id": "uuid",
  "quantity": 1,
  "total_amount": 399.00,
  "currency": "PKR",
  "status": "confirmed",
  "requires_payment": true,
  "payment_url": "https://sandbox.payfast.co.za/eng/process",
  "payment_data": {
    "merchant_id": "10000100",
    "merchant_key": "test_key",
    "amount": "399.00",
    "item_name": "Workshop - VIP Pass",
    "return_url": "http://localhost:5173/payment/success?registration_id=reg_123456",
    "cancel_url": "http://localhost:5173/payment/cancel",
    "notify_url": "http://localhost:5173/api/payments/payfast/notify"
  }
}
```

### **3. Check Booking Status (Public)**
```http
GET /api/bookings/{registration_id}/status
```
**Response:**
```json
{
  "registration_id": "reg_123456",
  "status": "confirmed", // confirmed|pending|failed|cancelled
  "payment_status": "completed", // pending|completed|failed
  "event": {
    "title": "Workshop Title",
    "start_datetime": "2025-09-09T22:34:06+05:00",
    "location": "Main Conference Room"
  },
  "ticket": {
    "name": "VIP Pass",
    "quantity": 1,
    "total_amount": 399.00
  },
  "qr_code": "base64_encoded_qr_code",
  "check_in_code": "ABC123"
}
```

### **4. My Bookings (Authenticated)**
```http
GET /api/my-bookings
Authorization: Bearer {token}
```
**Response:**
```json
{
  "bookings": [
    {
      "registration_id": "reg_123456",
      "event": {
        "id": "uuid",
        "title": "Workshop Title",
        "start_datetime": "2025-09-09T22:34:06+05:00",
        "location": "Main Conference Room"
      },
      "ticket": {
        "name": "VIP Pass",
        "quantity": 1,
        "total_amount": 399.00
      },
      "status": "confirmed",
      "payment_status": "completed",
      "registered_at": "2025-09-02T22:34:06+05:00",
      "qr_code": "base64_encoded_qr_code"
    }
  ]
}
```

## 🏢 Institute Dashboard APIs

### **5. Institute Sales Overview**
```http
GET /api/institute/sales
Authorization: Bearer {token}
```
**Response:**
```json
{
  "total_revenue": 15000.00,
  "pending_payout": 13500.00,
  "commission_rate": 10,
  "events": [
    {
      "event_id": "uuid",
      "title": "Workshop Title",
      "total_sales": 5000.00,
      "tickets_sold": 15,
      "payout_status": "pending", // pending|processing|completed
      "payout_amount": 4500.00
    }
  ]
}
```

## 👑 Admin Dashboard APIs

### **6. Admin Overview**
```http
GET /api/admin/events/overview
Authorization: Bearer {token}
```
**Response:**
```json
{
  "total_revenue": 50000.00,
  "total_commission": 5000.00,
  "pending_payouts": 45000.00,
  "events_needing_payout": [
    {
      "event_id": "uuid",
      "institute_name": "ABC Institute",
      "total_sales": 15000.00,
      "commission": 1500.00,
      "payout_due": 13500.00
    }
  ]
}
```

### **7. Create Payout**
```http
POST /api/admin/payouts
Authorization: Bearer {token}
```
**Request:**
```json
{
  "event_id": "uuid",
  "commission_percentage": 10
}
```

### **8. Complete Payout**
```http
POST /api/admin/payouts/{payout_id}/complete
Authorization: Bearer {token}
```
**Request:**
```json
{
  "transaction_reference": "TXN123456",
  "notes": "Bank transfer completed"
}
```

## 🔧 Implementation Notes

1. **Currency**: Always use PKR
2. **Authentication**: Bearer token in Authorization header
3. **PayFast Integration**: Return payment form data for frontend redirect
4. **Status Polling**: Frontend will poll booking status after payment
5. **QR Codes**: Generate for confirmed bookings
6. **Commission**: Admin takes 10% commission by default

## 📱 Frontend Integration Ready

Frontend components are already built and waiting for these APIs:
- ✅ Event listing page
- ✅ Ticket selection component  
- ✅ Payment integration
- ✅ Booking status checking
- ✅ User dashboard
- ✅ Institute sales dashboard

**Mock data is currently being used for testing.**
