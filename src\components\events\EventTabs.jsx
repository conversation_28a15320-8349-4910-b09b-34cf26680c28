import React from 'react';
import { FiCalendar, FiStar, FiAward } from 'react-icons/fi';
import { Card } from '../ui/layout';

const EventTabs = ({ activeTab, onTabChange, eventCounts = {} }) => {
  const tabs = [
    { 
      id: 'all', 
      name: 'All Events', 
      icon: FiCalendar, 
      count: eventCounts.all || 0 
    },
    { 
      id: 'featured', 
      name: 'Featured', 
      icon: FiStar, 
      count: eventCounts.featured || 0 
    },
    { 
      id: 'competitions', 
      name: 'Competitions', 
      icon: FiAward, 
      count: eventCounts.competitions || 0 
    }
  ];

  return (
    <Card padding="sm">
      <nav className="flex space-x-1">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`${
                activeTab === tab.id
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              } flex-1 flex items-center justify-center px-4 py-3 rounded font-medium text-sm transition-colors duration-200`}
            >
              <Icon className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">{tab.name}</span>
              <span className="sm:hidden">{tab.name.split(' ')[0]}</span>
              <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                activeTab === tab.id
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-200 text-gray-700'
              }`}>
                {tab.count}
              </span>
            </button>
          );
        })}
      </nav>
    </Card>
  );
};

export default EventTabs;
