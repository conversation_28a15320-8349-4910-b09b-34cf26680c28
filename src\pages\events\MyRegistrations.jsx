/**
 * My Registrations Page
 * 
 * Shows user's event registrations and allows payment for unpaid tickets.
 * This is where users complete payment for paid events after registering.
 */

import React, { useState, useEffect } from 'react';
import {
  FiCalendar,
  FiMapPin,
  FiCreditCard,
  FiCheck,
  FiClock,
  FiX,
  FiRefreshCw,
  FiEye,
  FiDownload,
  FiAlertCircle,
  FiDollarSign
} from 'react-icons/fi';
import { format } from 'date-fns';
import newEventService from '../../services/newEventService';
import { useNotification } from '../../contexts/NotificationContext';
import { LoadingSpinner } from '../../components/ui';

const MyRegistrations = () => {
  const [registrations, setRegistrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [payingFor, setPayingFor] = useState(null);

  const { showSuccess, showError, showInfo } = useNotification();

  // Load user registrations
  const loadRegistrations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await newEventService.getMyRegistrations();
      // API returns array directly, not wrapped in object
      setRegistrations(Array.isArray(response) ? response : response.registrations || []);

    } catch (error) {
      console.error('Failed to load registrations:', error);
      setError(error.message || 'Failed to load registrations');
      showError(error.message || 'Failed to load registrations');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadRegistrations();
  }, []);

  // Handle payment
  const handlePayment = async (registration) => {
    try {
      setPayingFor(registration.registration_id);
      showInfo('Creating payment link...');

      const paymentResult = await newEventService.payForRegistration(registration.registration_id);

      if (paymentResult.success) {
        showInfo('Redirecting to payment...');
        // The service will automatically handle the PayFast redirect
      }

    } catch (error) {
      console.error('Payment failed:', error);
      showError(error.message || 'Failed to create payment link');
    } finally {
      setPayingFor(null);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy \'at\' h:mm a');
    } catch (error) {
      return dateString;
    }
  };

  // Get status badge
  const getStatusBadge = (registration) => {
    const { status, payment } = registration;
    
    if (status === 'CONFIRMED') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <FiCheck className="w-3 h-3 mr-1" />
          Confirmed
        </span>
      );
    }
    
    if (status === 'PENDING_PAYMENT') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          <FiClock className="w-3 h-3 mr-1" />
          Payment Required
        </span>
      );
    }
    
    if (status === 'CANCELLED') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <FiX className="w-3 h-3 mr-1" />
          Cancelled
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {status}
      </span>
    );
  };

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Registrations</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadRegistrations}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Event Registrations</h1>
            <p className="text-gray-600 mt-2">
              Manage your event registrations and complete payments
            </p>
          </div>
          <button
            onClick={loadRegistrations}
            disabled={loading}
            className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
          >
            <FiRefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>

        {/* Registrations List */}
        {registrations.length === 0 ? (
          <div className="text-center py-12">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Registrations Found</h3>
            <p className="text-gray-600 mb-4">
              You haven't registered for any events yet.
            </p>
            <a
              href="/events/new"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Browse Events
            </a>
          </div>
        ) : (
          <div className="space-y-6">
            {registrations.map((registration) => (
              <div
                key={registration.registration_id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    {/* Event Info */}
                    <div className="flex-1">
                      <div className="flex items-start space-x-4">
                        {registration.event.banner_image_url && (
                          <img
                            src={registration.event.banner_image_url}
                            alt={registration.event.title}
                            className="w-20 h-20 object-cover rounded-lg"
                          />
                        )}
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {registration.event.title}
                          </h3>
                          
                          <div className="space-y-2 text-sm text-gray-600">
                            <div className="flex items-center">
                              <FiCalendar className="w-4 h-4 mr-2" />
                              {formatDate(registration.event.start_datetime)}
                            </div>
                            <div className="flex items-center">
                              <FiMapPin className="w-4 h-4 mr-2" />
                              {registration.event.location}
                            </div>
                          </div>

                          {/* Registration Details */}
                          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-900">Registration #:</span>
                                <p className="text-gray-600">{registration.registration_number}</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Ticket:</span>
                                <p className="text-gray-600">{registration.ticket.name}</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Quantity:</span>
                                <p className="text-gray-600">{registration.ticket.quantity}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Status and Actions */}
                    <div className="ml-6 text-right">
                      <div className="mb-4">
                        {getStatusBadge(registration)}
                      </div>

                      {/* Price */}
                      <div className="mb-4">
                        {registration.ticket.total_amount === 0 ? (
                          <span className="text-lg font-bold text-green-600">FREE</span>
                        ) : (
                          <div className="text-right">
                            <div className="flex items-center text-lg font-bold text-gray-900">
                              <FiDollarSign className="w-4 h-4" />
                              {registration.ticket.total_amount.toFixed(2)} {registration.ticket.currency || 'ZAR'}
                            </div>
                            {registration.status === 'PENDING_PAYMENT' && (
                              <div className="text-xs text-orange-600 mt-1">
                                Payment required
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="space-y-2">
                        {registration.status === 'PENDING_PAYMENT' && (
                          <button
                            onClick={() => handlePayment(registration)}
                            disabled={payingFor === registration.registration_id}
                            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                          >
                            {payingFor === registration.registration_id ? (
                              <>
                                <LoadingSpinner size="sm" className="mr-2" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <FiCreditCard className="w-4 h-4 mr-2" />
                                Pay Now
                              </>
                            )}
                          </button>
                        )}

                        {registration.status === 'CONFIRMED' && registration.qr_code && (
                          <button
                            className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                          >
                            <FiDownload className="w-4 h-4 mr-2" />
                            Download Ticket
                          </button>
                        )}

                        <button
                          className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                        >
                          <FiEye className="w-4 h-4 mr-2" />
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Backend Notice */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <FiAlertCircle className="w-5 h-5 text-green-600 mr-2 flex-shrink-0" />
            <div className="text-sm text-green-800">
              <strong>✅ Registration APIs Working:</strong> Both registration (<code>POST /api/events/registrations/</code>) and
              user registrations (<code>GET /api/events/registrations/my-registrations</code>) are connected!
              Payment endpoints coming soon.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyRegistrations;
