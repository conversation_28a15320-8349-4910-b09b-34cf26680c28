/**
 * EnhancedEventDetailsPage Component
 * 
 * Enhanced event details page with integrated ticket selection and booking
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiArrowLeft,
  FiShare2,
  FiHeart,
  FiUser,
  FiCheck,
  FiInfo
} from 'react-icons/fi';
import { format } from 'date-fns';
import {
  fetchEventDetails,
  selectCurrentEvent,
  selectEventDetailsLoading,
  selectEventDetailsError,
  selectIsRegisteredForEvent
} from '../../store/slices/EventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import TicketSelection from '../../components/events/TicketSelection';
import { useNotification } from '../../contexts/NotificationContext';

const EnhancedEventDetailsPage = () => {
  const { eventId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSuccess } = useNotification();
  
  // Local state
  const [activeTab, setActiveTab] = useState('overview');
  const [showTicketSelection, setShowTicketSelection] = useState(false);

  // Redux state
  const event = useSelector(selectCurrentEvent);
  const loading = useSelector(selectEventDetailsLoading);
  const error = useSelector(selectEventDetailsError);
  const isRegistered = useSelector(selectIsRegisteredForEvent(eventId));

  // Load event details on mount
  useEffect(() => {
    if (eventId) {
      dispatch(fetchEventDetails(eventId));
    }
  }, [dispatch, eventId]);

  // Handle booking success
  const handleBookingSuccess = (booking) => {
    showSuccess('Event registration successful!');
    setShowTicketSelection(false);
    // Refresh event details to update registration status
    dispatch(fetchEventDetails(eventId));
  };

  // Handle booking error
  const handleBookingError = (error) => {
    console.error('Booking error:', error);
  };

  // Format date and time with validation
  const formatDate = (dateString) => {
    if (!dateString) return 'Date not available';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid date';
    return format(date, 'EEEE, MMMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    if (!dateString) return 'Time not available';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid time';
    return format(date, 'h:mm a');
  };

  // Check registration status with date validation
  const registrationEnd = event?.registration_end || event?.registration_deadline;
  const isRegistrationOpen = event && (
    // If no registration deadline, registration is open
    !registrationEnd ||
    // If there is a deadline, check if it's valid and not passed
    (!isNaN(new Date(registrationEnd).getTime()) && new Date() < new Date(registrationEnd))
  );
  const isFull = event && event.total_registrations >= event.max_attendees;
  const attendancePercentage = event ? (event.total_registrations / event.max_attendees) * 100 : 0;

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!event) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Event not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The event you're looking for doesn't exist or has been removed.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <button
        onClick={() => navigate(-1)}
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-6"
      >
        <FiArrowLeft className="h-4 w-4 mr-2" />
        Back to Events
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Event Header */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
            {/* Banner Image */}
            {event.banner_image_url && (
              <div className="relative h-64 bg-gray-200">
                <img
                  src={event.banner_image_url}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 left-4 flex space-x-2">
                  {event.is_featured && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      <FiStar className="h-4 w-4 mr-1 fill-current" />
                      Featured
                    </span>
                  )}
                  {event.is_competition && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                      <FiAward className="h-4 w-4 mr-1" />
                      Competition
                    </span>
                  )}
                </div>
                <div className="absolute top-4 right-4 flex space-x-2">
                  <button className="p-2 bg-white rounded-full shadow-sm hover:bg-gray-50">
                    <FiShare2 className="h-5 w-5 text-gray-600" />
                  </button>
                  <button className="p-2 bg-white rounded-full shadow-sm hover:bg-gray-50">
                    <FiHeart className="h-5 w-5 text-gray-600" />
                  </button>
                </div>
              </div>
            )}

            <div className="p-6">
              {/* Category */}
              {event.category && (
                <div className="mb-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {event.category.name}
                  </span>
                </div>
              )}

              {/* Title and Description */}
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{event.title}</h1>
              <p className="text-gray-600 text-lg mb-6">{event.description}</p>

              {/* Event Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center text-gray-600">
                    <FiCalendar className="h-5 w-5 mr-3" />
                    <div>
                      <p className="font-medium">{formatDate(event.start_datetime)}</p>
                      <p className="text-sm">{formatTime(event.start_datetime)} - {formatTime(event.end_datetime)}</p>
                    </div>
                  </div>
                  
                  {event.location && (
                    <div className="flex items-center text-gray-600">
                      <FiMapPin className="h-5 w-5 mr-3" />
                      <div>
                        <p className="font-medium">{event.location}</p>
                        {event.venue_address && (
                          <p className="text-sm">{event.venue_address}</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center text-gray-600">
                    <FiUsers className="h-5 w-5 mr-3" />
                    <div>
                      <p className="font-medium">{event.total_registrations} / {event.max_attendees} attendees</p>
                      <div className="w-32 bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className={`h-2 rounded-full ${
                            attendancePercentage >= 90 ? 'bg-red-500' :
                            attendancePercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${Math.min(attendancePercentage, 100)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center text-gray-600">
                    <FiClock className="h-5 w-5 mr-3" />
                    <div>
                      <p className="font-medium">Registration</p>
                      <p className="text-sm">
                        {registrationEnd ? `Ends ${formatDate(registrationEnd)}` : 'Open registration'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'overview', label: 'Overview' },
                  { id: 'speakers', label: 'Speakers' },
                  { id: 'agenda', label: 'Agenda' },
                  { id: 'location', label: 'Location' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'overview' && (
                <div className="prose max-w-none">
                  <p className="text-gray-600">{event.description}</p>
                  {event.long_description && (
                    <div className="mt-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">About this event</h3>
                      <p className="text-gray-600">{event.long_description}</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'speakers' && (
                <div>
                  {event.speakers && event.speakers.length > 0 ? (
                    <div className="space-y-6">
                      {event.speakers.map((speaker) => (
                        <div key={speaker.id} className="flex items-start space-x-4">
                          <img
                            src={speaker.profile_image_url || '/default-avatar.png'}
                            alt={speaker.name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                          <div>
                            <h3 className="font-semibold text-gray-900">{speaker.name}</h3>
                            <p className="text-sm text-blue-600">{speaker.title}</p>
                            <p className="text-gray-600 mt-2">{speaker.bio}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FiUser className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No speakers announced yet</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'agenda' && (
                <div className="text-center py-8">
                  <FiInfo className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Agenda will be available soon</p>
                </div>
              )}

              {activeTab === 'location' && (
                <div>
                  {event.location ? (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{event.location}</h3>
                      {event.venue_address && (
                        <p className="text-gray-600">{event.venue_address}</p>
                      )}
                      {/* Add map integration here if needed */}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FiMapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Location details will be provided</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Registration Status */}
          {isRegistered ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-center">
                <FiCheck className="h-6 w-6 text-green-600 mr-3" />
                <div>
                  <h3 className="font-semibold text-green-900">You're registered!</h3>
                  <p className="text-sm text-green-700">Check your email for ticket details</p>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Quick Registration */}
              {isRegistrationOpen && !isFull && !showTicketSelection && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Registration</h3>
                  <button
                    onClick={() => setShowTicketSelection(true)}
                    className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Select Tickets
                  </button>
                </div>
              )}

              {/* Ticket Selection */}
              {showTicketSelection && (
                <TicketSelection
                  event={event}
                  onBookingSuccess={handleBookingSuccess}
                  onBookingError={handleBookingError}
                />
              )}

              {/* Registration Closed */}
              {(!isRegistrationOpen || isFull) && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Registration Unavailable</h3>
                  <p className="text-sm text-gray-600">
                    {!isRegistrationOpen ? 'Registration has closed' : 'Event is full'}
                  </p>
                </div>
              )}
            </>
          )}

          {/* Organizer */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Organizer</h3>
            <div className="flex items-center space-x-3">
              <FiUser className="h-8 w-8 text-gray-400" />
              <div>
                <p className="font-medium text-gray-900">Event Organizer</p>
                <p className="text-sm text-gray-500">Verified organizer</p>
              </div>
            </div>
          </div>

          {/* Event Tags */}
          {event.tags && event.tags.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {event.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedEventDetailsPage;
