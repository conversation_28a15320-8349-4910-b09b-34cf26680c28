import axios from 'axios';
import { getAuthToken } from '../utils/helpers/authHelpers';
import { BASE_API } from '../utils/api/API_URL';

const API_BASE = `${BASE_API}/api/payments/payfast`;

// PayFast Payment Service
export class PayFastService {
  
  /**
   * Initialize PayFast payment for event ticket
   * @param {Object} paymentData - Payment details
   * @returns {Promise} Payment initialization response
   */
  static async initializePayment(paymentData) {
    try {
      const response = await axios.post(`${API_BASE}/initialize`, paymentData, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to initialize payment');
    }
  }

  /**
   * Verify PayFast payment status
   * @param {string} paymentId - Payment ID
   * @returns {Promise} Payment verification response
   */
  static async verifyPayment(paymentId) {
    try {
      const response = await axios.get(`${API_BASE}/verify/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to verify payment');
    }
  }

  /**
   * Get payment history for user
   * @param {Object} params - Query parameters
   * @returns {Promise} Payment history
   */
  static async getPaymentHistory(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await axios.get(`${API_BASE}/history?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch payment history');
    }
  }

  /**
   * Cancel payment
   * @param {string} paymentId - Payment ID
   * @returns {Promise} Cancellation response
   */
  static async cancelPayment(paymentId) {
    try {
      const response = await axios.post(`${API_BASE}/cancel/${paymentId}`, {}, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to cancel payment');
    }
  }

  /**
   * Refund payment (Admin only)
   * @param {string} paymentId - Payment ID
   * @param {Object} refundData - Refund details
   * @returns {Promise} Refund response
   */
  static async refundPayment(paymentId, refundData) {
    try {
      const response = await axios.post(`${API_BASE}/refund/${paymentId}`, refundData, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to process refund');
    }
  }

  /**
   * Get payment analytics (Admin only)
   * @param {Object} params - Query parameters
   * @returns {Promise} Payment analytics
   */
  static async getPaymentAnalytics(params = {}) {
    try {
      const queryParams = new URLSearchParams(params);
      const response = await axios.get(`${API_BASE}/analytics?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch payment analytics');
    }
  }

  /**
   * Generate PayFast payment form HTML
   * @param {Object} paymentData - Payment form data
   * @returns {string} PayFast form HTML
   */
  static generatePaymentForm(paymentData) {
    const {
      merchant_id,
      merchant_key,
      return_url,
      cancel_url,
      notify_url,
      amount,
      item_name,
      item_description,
      email_address,
      payment_id,
      signature
    } = paymentData;

    return `
      <form action="https://www.payfast.co.za/eng/process" method="post" id="payfast-form">
        <input type="hidden" name="merchant_id" value="${merchant_id}">
        <input type="hidden" name="merchant_key" value="${merchant_key}">
        <input type="hidden" name="return_url" value="${return_url}">
        <input type="hidden" name="cancel_url" value="${cancel_url}">
        <input type="hidden" name="notify_url" value="${notify_url}">
        <input type="hidden" name="amount" value="${amount}">
        <input type="hidden" name="item_name" value="${item_name}">
        <input type="hidden" name="item_description" value="${item_description}">
        <input type="hidden" name="email_address" value="${email_address}">
        <input type="hidden" name="m_payment_id" value="${payment_id}">
        <input type="hidden" name="signature" value="${signature}">
        <input type="submit" value="Pay Now" class="hidden">
      </form>
    `;
  }

  /**
   * Redirect to PayFast payment
   * @param {Object} paymentData - Payment data
   */
  static redirectToPayment(paymentData) {
    const form = this.generatePaymentForm(paymentData);
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = form;
    document.body.appendChild(tempDiv);
    
    const paymentForm = document.getElementById('payfast-form');
    paymentForm.submit();
    
    // Clean up
    document.body.removeChild(tempDiv);
  }
}

export default PayFastService;
