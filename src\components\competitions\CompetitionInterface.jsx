import React, { useState, useEffect, useRef } from 'react';
import {
  Fi<PERSON>lock,
  FiShield,
  FiAlertTriangle,
  FiEye,
  FiMonitor,
  FiSend,
  FiFlag,
  FiHelpCircle
} from 'react-icons/fi';
import { monitorCompetitionSession } from '../../services/competitionService';

const CompetitionInterface = ({ 
  competition, 
  session, 
  questions = [], 
  onSubmitAnswer, 
  onFinalSubmit,
  securitySettings = {}
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState(session?.time_remaining || 0);
  const [securityViolations, setSecurityViolations] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
  
  const interfaceRef = useRef(null);
  const lastActiveTime = useRef(Date.now());

  // Timer effect
  useEffect(() => {
    if (timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          handleAutoSubmit();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining]);

  // Security monitoring effects
  useEffect(() => {
    if (!securitySettings.tab_switching_detection) return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        addSecurityViolation('Tab switching detected');
        monitorSession('tab_switch');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [securitySettings.tab_switching_detection]);

  useEffect(() => {
    if (!securitySettings.copy_paste_disabled) return;

    const preventCopyPaste = (e) => {
      if (e.ctrlKey && (e.key === 'c' || e.key === 'v' || e.key === 'x')) {
        e.preventDefault();
        addSecurityViolation('Copy/paste attempt detected');
      }
    };

    document.addEventListener('keydown', preventCopyPaste);
    return () => document.removeEventListener('keydown', preventCopyPaste);
  }, [securitySettings.copy_paste_disabled]);

  useEffect(() => {
    if (!securitySettings.screenshot_detection) return;

    const preventScreenshot = (e) => {
      if (e.key === 'PrintScreen' || (e.ctrlKey && e.shiftKey && e.key === 'S')) {
        e.preventDefault();
        addSecurityViolation('Screenshot attempt detected');
      }
    };

    document.addEventListener('keydown', preventScreenshot);
    return () => document.removeEventListener('keydown', preventScreenshot);
  }, [securitySettings.screenshot_detection]);

  const addSecurityViolation = (violation) => {
    setSecurityViolations(prev => [...prev, {
      type: violation,
      timestamp: new Date().toISOString()
    }]);
  };

  const monitorSession = async (eventType, data = {}) => {
    try {
      await monitorCompetitionSession(competition.id, {
        session_id: session.session_id,
        event_type: eventType,
        timestamp: new Date().toISOString(),
        ...data
      });
    } catch (error) {
      console.error('Failed to monitor session:', error);
    }
  };

  const handleAnswerChange = (questionId, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
    
    onSubmitAnswer && onSubmitAnswer(questionId, answer);
    lastActiveTime.current = Date.now();
  };

  const handleAutoSubmit = async () => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    try {
      await onFinalSubmit(answers);
      await monitorSession('auto_submit', { reason: 'time_expired' });
    } catch (error) {
      console.error('Auto-submit failed:', error);
    }
  };

  const handleManualSubmit = async () => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    try {
      await onFinalSubmit(answers);
      await monitorSession('manual_submit');
    } catch (error) {
      console.error('Manual submit failed:', error);
    } finally {
      setIsSubmitting(false);
      setShowSubmitConfirm(false);
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    if (timeRemaining <= 300) return 'text-red-600'; // Last 5 minutes
    if (timeRemaining <= 900) return 'text-yellow-600'; // Last 15 minutes
    return 'text-green-600';
  };

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  return (
    <div ref={interfaceRef} className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Competition Info */}
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-semibold text-gray-900">{competition.title}</h1>
              <span className="text-sm text-gray-500">
                Question {currentQuestionIndex + 1} of {questions.length}
              </span>
            </div>

            {/* Timer and Security Status */}
            <div className="flex items-center space-x-6">
              {/* Security Status */}
              {securitySettings.proctoring_enabled && (
                <div className="flex items-center space-x-2">
                  <FiEye className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">Monitored</span>
                </div>
              )}

              {securityViolations.length > 0 && (
                <div className="flex items-center space-x-2">
                  <FiAlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-600">
                    {securityViolations.length} violation(s)
                  </span>
                </div>
              )}

              {/* Timer */}
              <div className="flex items-center space-x-2">
                <FiClock className={`h-4 w-4 ${getTimeColor()}`} />
                <span className={`text-sm font-mono ${getTimeColor()}`}>
                  {formatTime(timeRemaining)}
                </span>
              </div>

              {/* Submit Button */}
              <button
                onClick={() => setShowSubmitConfirm(true)}
                disabled={isSubmitting}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <FiSend className="h-4 w-4 mr-2" />
                Submit
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Question Content */}
          <div className="lg:col-span-3">
            {currentQuestion && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-medium text-gray-900">
                      Question {currentQuestionIndex + 1}
                    </h2>
                    <div className="flex items-center space-x-2">
                      <FiFlag className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-500">
                        {currentQuestion.marks} marks
                      </span>
                    </div>
                  </div>
                  
                  <div className="prose max-w-none">
                    <p className="text-gray-900">{currentQuestion.question_text}</p>
                  </div>
                </div>

                {/* Answer Input */}
                <div className="space-y-4">
                  {currentQuestion.question_type === 'multiple_choice' && (
                    <div className="space-y-3">
                      {currentQuestion.options?.map((option, index) => (
                        <label key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                          <input
                            type="radio"
                            name={`question_${currentQuestion.id}`}
                            value={option.id}
                            checked={answers[currentQuestion.id] === option.id}
                            onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-gray-900">{option.text}</span>
                        </label>
                      ))}
                    </div>
                  )}

                  {currentQuestion.question_type === 'text' && (
                    <textarea
                      value={answers[currentQuestion.id] || ''}
                      onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your answer here..."
                    />
                  )}
                </div>

                {/* Navigation */}
                <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
                  <button
                    onClick={() => setCurrentQuestionIndex(prev => Math.max(0, prev - 1))}
                    disabled={currentQuestionIndex === 0}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  
                  <button
                    onClick={() => setCurrentQuestionIndex(prev => Math.min(questions.length - 1, prev + 1))}
                    disabled={currentQuestionIndex === questions.length - 1}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Question Navigator */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Questions</h3>
              <div className="grid grid-cols-5 gap-2">
                {questions.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestionIndex(index)}
                    className={`w-8 h-8 text-xs font-medium rounded ${
                      index === currentQuestionIndex
                        ? 'bg-blue-600 text-white'
                        : answers[questions[index]?.id]
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}
              </div>
            </div>

            {/* Security Monitor */}
            {Object.values(securitySettings).some(Boolean) && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div className="flex items-center space-x-2 mb-4">
                  <FiShield className="h-4 w-4 text-blue-600" />
                  <h3 className="text-sm font-medium text-gray-900">Security Status</h3>
                </div>
                
                <div className="space-y-2 text-xs">
                  {securitySettings.proctoring_enabled && (
                    <div className="flex items-center space-x-2 text-green-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Proctoring Active</span>
                    </div>
                  )}
                  
                  {securitySettings.tab_switching_detection && (
                    <div className="flex items-center space-x-2 text-blue-600">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Tab Monitoring</span>
                    </div>
                  )}
                  
                  {securityViolations.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <p className="text-red-600 font-medium mb-2">Violations:</p>
                      {securityViolations.slice(-3).map((violation, index) => (
                        <div key={index} className="text-red-600">
                          {violation.type}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Help */}
            <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
              <div className="flex items-center space-x-2 mb-2">
                <FiHelpCircle className="h-4 w-4 text-blue-600" />
                <h3 className="text-sm font-medium text-blue-900">Need Help?</h3>
              </div>
              <p className="text-xs text-blue-800">
                Contact support if you experience technical issues during the competition.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Submit Competition</h3>
            <p className="text-sm text-gray-600 mb-6">
              Are you sure you want to submit your competition? You cannot make changes after submission.
            </p>
            
            <div className="flex items-center justify-end space-x-4">
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleManualSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Competition'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompetitionInterface;
