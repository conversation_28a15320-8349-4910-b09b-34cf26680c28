/**
 * New Events System Test Page
 * 
 * Test page for the redesigned event registration and ticket buying system.
 * Demonstrates the new API structure and components.
 */

import React, { useState } from 'react';
import {
  FiInfo,
  FiCheck,
  FiX,
  FiCalendar,
  FiUsers,
  FiDollarSign,
  FiRefreshCw,
  FiExternalLink,
  FiCode,
  FiDatabase
} from 'react-icons/fi';
import NewEventsList from '../../components/events/NewEventsList';
import NewEventCard from '../../components/events/NewEventCard';
import NewEventRegistration from '../../components/events/NewEventRegistration';
import newEventService from '../../services/newEventService';
import { useNotification } from '../../contexts/NotificationContext';

const NewEventsTest = () => {
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [isTestingAPI, setIsTestingAPI] = useState(false);

  const { showSuccess, showError, showInfo } = useNotification();

  // Mock event for testing
  const mockEvent = {
    id: 'test-event-1',
    title: 'Test Event - Web Development Workshop',
    description: 'A comprehensive workshop covering modern web development techniques using React, Node.js, and best practices.',
    short_description: 'Learn modern web development in this hands-on workshop',
    category: 'WORKSHOP',
    status: 'PUBLISHED',
    start_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    end_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),
    location: 'Tech Hub, Cape Town',
    banner_image_url: 'https://via.placeholder.com/800x400?text=Web+Development+Workshop',
    is_featured: true,
    is_public: true,
    max_attendees: 50,
    current_registrations: 25,
    registration_start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    registration_end: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
    organizer: {
      id: 'org-1',
      name: 'Tech Academy',
      email: '<EMAIL>'
    },
    tickets: [
      {
        id: 'ticket-free',
        name: 'Free Entry',
        description: 'Free access to all sessions',
        price: 0.00,
        currency: 'ZAR',
        available_quantity: 30,
        max_per_user: 1,
        status: 'ACTIVE',
        features: ['Access to all sessions', 'Lunch included', 'Certificate of completion']
      },
      {
        id: 'ticket-premium',
        name: 'Premium Access',
        description: 'Premium access with extras',
        price: 99.00,
        currency: 'ZAR',
        available_quantity: 20,
        max_per_user: 1,
        status: 'ACTIVE',
        features: ['All Free features', 'Take-home materials', 'One-on-one mentoring session']
      }
    ],
    user_registration: {
      is_registered: false,
      registration_id: null,
      ticket_name: null,
      quantity: 0,
      status: null
    }
  };

  // Test API endpoints
  const testAPIEndpoints = async () => {
    setIsTestingAPI(true);
    const results = {};

    try {
      // Test backend availability
      showInfo('Testing backend availability...');
      const backendAvailable = await newEventService.checkBackendAvailability();
      results.backendAvailable = backendAvailable;

      // Test events listing
      showInfo('Testing events listing...');
      const eventsResponse = await newEventService.getEvents({ limit: 5 });
      results.eventsList = {
        success: true,
        count: eventsResponse.events?.length || 0,
        total: eventsResponse.total || 0
      };

      // Test event details
      showInfo('Testing event details...');
      const eventDetails = await newEventService.getEventDetails('test-event-1');
      results.eventDetails = {
        success: true,
        hasTickets: eventDetails.tickets?.length > 0
      };

      // Test registration (free ticket)
      showInfo('Testing free ticket registration...');
      const freeRegistration = await newEventService.registerForEvent('test-event-1', {
        ticket_id: 'ticket-free',
        quantity: 1,
        attendee_info: {
          name: 'Test User',
          email: '<EMAIL>'
        }
      });
      results.freeRegistration = {
        success: freeRegistration.success,
        status: freeRegistration.status
      };

      // Test registration (paid ticket)
      showInfo('Testing paid ticket registration...');
      const paidRegistration = await newEventService.registerForEvent('test-event-1', {
        ticket_id: 'ticket-premium',
        quantity: 1,
        attendee_info: {
          name: 'Test User',
          email: '<EMAIL>'
        }
      });
      results.paidRegistration = {
        success: paidRegistration.success,
        status: paidRegistration.status,
        paymentRequired: paidRegistration.status === 'PENDING_PAYMENT'
      };

      // Test payment for registration
      showInfo('Testing payment for registration...');
      const paymentResult = await newEventService.payForRegistration('reg-123');
      results.paymentCreation = {
        success: paymentResult.success,
        hasPaymentUrl: !!paymentResult.payment_url
      };

      // Test user registrations
      showInfo('Testing user registrations...');
      const userRegistrations = await newEventService.getMyRegistrations({ limit: 5 });
      results.userRegistrations = {
        success: true,
        count: userRegistrations.registrations?.length || 0
      };

      setTestResults(results);
      showSuccess('API testing completed!');

    } catch (error) {
      console.error('API test failed:', error);
      results.error = error.message;
      setTestResults(results);
      showError('API testing failed: ' + error.message);
    } finally {
      setIsTestingAPI(false);
    }
  };

  // Handle event selection
  const handleEventSelect = (event) => {
    setSelectedEvent(event);
    showInfo(`Selected event: ${event.title}`);
  };

  // Handle registration success
  const handleRegistrationSuccess = (result) => {
    showSuccess('Registration test successful!');
    console.log('Registration result:', result);
  };

  // Handle test registration
  const handleTestRegistration = () => {
    setSelectedEvent(mockEvent);
    setShowRegistrationModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            New Events System Test Page
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Test the redesigned event registration and ticket buying system. 
            This page demonstrates the new API structure and components.
          </p>
        </div>

        {/* Backend Status */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <div className="flex items-start">
            <FiInfo className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h2 className="text-lg font-semibold text-green-900 mb-2">✅ Backend Event Registration Available!</h2>
              <div className="text-sm text-green-800 space-y-2">
                <p><strong>Event registration endpoint is now implemented in the backend!</strong></p>
                <p>Working endpoint: <code>POST /api/events/registrations/</code></p>
                <p>Frontend will now use real backend API instead of mock data.</p>
                <p className="font-medium text-green-900">Test the registration flow below to see real API integration!</p>
              </div>
            </div>
          </div>
        </div>

        {/* API Testing Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">API Endpoint Testing</h2>
            <button
              onClick={testAPIEndpoints}
              disabled={isTestingAPI}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isTestingAPI ? (
                <>
                  <FiRefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <FiRefreshCw className="w-4 h-4 mr-2" />
                  Test All APIs
                </>
              )}
            </button>
          </div>

          {/* Test Results */}
          {Object.keys(testResults).length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Test Results:</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Backend Availability */}
                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Backend Available</span>
                    {testResults.backendAvailable ? (
                      <FiCheck className="w-4 h-4 text-green-600" />
                    ) : (
                      <FiX className="w-4 h-4 text-red-600" />
                    )}
                  </div>
                  <p className="text-xs text-gray-600">
                    {testResults.backendAvailable ? 'Real API detected' : 'Using mock implementation'}
                  </p>
                </div>

                {/* Events List */}
                {testResults.eventsList && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Events List</span>
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <p className="text-xs text-gray-600">
                      {testResults.eventsList.count} events loaded
                    </p>
                  </div>
                )}

                {/* Event Details */}
                {testResults.eventDetails && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Event Details</span>
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <p className="text-xs text-gray-600">
                      {testResults.eventDetails.hasTickets ? 'With tickets' : 'No tickets'}
                    </p>
                  </div>
                )}

                {/* Free Registration */}
                {testResults.freeRegistration && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Free Registration</span>
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <p className="text-xs text-gray-600">
                      Status: {testResults.freeRegistration.status}
                    </p>
                  </div>
                )}

                {/* Paid Registration */}
                {testResults.paidRegistration && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Paid Registration</span>
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <p className="text-xs text-gray-600">
                      Status: {testResults.paidRegistration.status}
                    </p>
                  </div>
                )}

                {/* Payment Creation */}
                {testResults.paymentCreation && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Payment Creation</span>
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <p className="text-xs text-gray-600">
                      {testResults.paymentCreation.hasPaymentUrl ? 'Payment URL generated' : 'No payment URL'}
                    </p>
                  </div>
                )}

                {/* User Registrations */}
                {testResults.userRegistrations && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">User Registrations</span>
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <p className="text-xs text-gray-600">
                      {testResults.userRegistrations.count} registrations found
                    </p>
                  </div>
                )}
              </div>

              {testResults.error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 text-sm">
                    <strong>Error:</strong> {testResults.error}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Component Testing Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Component Testing</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Event Card Test */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Event Card Component</h3>
              <NewEventCard
                event={mockEvent}
                onViewDetails={handleEventSelect}
                onRegistrationSuccess={handleRegistrationSuccess}
              />
            </div>

            {/* Registration Modal Test */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Registration Modal</h3>
              <div className="space-y-4">
                <button
                  onClick={handleTestRegistration}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Test Registration Modal
                </button>
                
                <div className="text-sm text-gray-600 space-y-2">
                  <p><strong>Features to test:</strong></p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Real backend API registration</li>
                    <li>Event registration creation</li>
                    <li>Form validation</li>
                    <li>Backend response handling</li>
                    <li>Error handling and fallbacks</li>
                  </ul>
                  <p className="text-green-600 font-medium mt-2">
                    ✅ Now using real backend: POST /api/events/registrations/
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Events List Component */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Events List Component</h2>
          <NewEventsList
            onEventSelect={handleEventSelect}
            showFilters={true}
            showSearch={true}
            showPagination={true}
            defaultView="grid"
            itemsPerPage={6}
          />
        </div>

        {/* API Documentation */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Required Backend Endpoints</h2>
          
          <div className="space-y-4 text-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Event Management</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• GET /api/events - List events</li>
                  <li>• GET /api/events/:id - Event details</li>
                  <li>• POST /api/events - Create event</li>
                  <li>• PUT /api/events/:id - Update event</li>
                </ul>
              </div>
              
              <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                <h4 className="font-medium text-green-900 mb-2">✅ Working Registration API</h4>
                <ul className="space-y-1 text-green-700">
                  <li>• POST /api/events/registrations/ - ✅ WORKING</li>
                  <li>• POST /api/events/registrations/:id/pay - Coming soon</li>
                  <li>• GET /api/events/my-registrations - Coming soon</li>
                  <li>• DELETE /api/events/registrations/:id - Coming soon</li>
                </ul>
              </div>
            </div>
            
            <div className="flex items-center justify-center pt-4">
              <a
                href="/EVENT_API_DESIGN.md"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiExternalLink className="w-4 h-4 mr-2" />
                View Complete API Documentation
              </a>
            </div>
          </div>
        </div>

        {/* Selected Event Display */}
        {selectedEvent && (
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Selected Event:</h3>
            <p className="text-blue-800">{selectedEvent.title}</p>
            <p className="text-blue-700 text-sm">ID: {selectedEvent.id}</p>
          </div>
        )}
      </div>

      {/* Registration Modal */}
      {showRegistrationModal && selectedEvent && (
        <NewEventRegistration
          event={selectedEvent}
          isOpen={showRegistrationModal}
          onClose={() => setShowRegistrationModal(false)}
          onSuccess={handleRegistrationSuccess}
        />
      )}
    </div>
  );
};

export default NewEventsTest;
