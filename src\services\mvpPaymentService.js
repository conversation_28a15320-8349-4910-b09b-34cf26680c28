/**
 * MVP Payment Service
 * 
 * Simple payment service that acts as a cashier:
 * - Passes ticket ID to backend
 * - Gets PayFast payment link
 * - <PERSON><PERSON> redirects and status checks
 * 
 * Frontend never decides prices, only passes ticket ID.
 */

import axios from 'axios';
import { BASE_API } from '../utils/api/API_URL';
import { getAuthToken } from '../utils/helpers/authHelpers';
import logger from '../utils/helpers/logger';

class MVPPaymentService {
  constructor() {
    this.baseUrl = BASE_API;
  }

  /**
   * Get auth headers for API requests
   */
  getAuthHeaders() {
    const token = getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Create payment link for a ticket
   * Frontend acts as cashier - just passes ticket ID, backend decides everything else
   *
   * NOTE: Backend PayFast endpoints are not yet implemented.
   * This is a mock implementation that will be updated once backend is ready.
   *
   * @param {string} ticketId - The ticket ID to purchase
   * @returns {Promise<Object>} Payment response with PayFast link
   */
  async createPaymentLink(ticketId) {
    try {
      logger.info('Creating payment link for ticket:', ticketId);

      // Try to call the real PayFast endpoint first

      const response = await axios.post(
        `${this.baseUrl}/api/payfast/create-payment`,
        { ticket_id: ticketId },
        { headers: this.getAuthHeaders() }
      );

      logger.info('Payment link created successfully:', response.data);
      return response.data;

    } catch (error) {
      logger.error('Failed to create payment link:', error);

      // Handle different error types
      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case 400:
            throw new Error(data.message || 'Invalid ticket ID');
          case 401:
            throw new Error('Please log in to purchase tickets');
          case 403:
            throw new Error('You are not authorized to purchase this ticket');
          case 404:
            // If endpoint doesn't exist, use mock implementation
            logger.warn('PayFast endpoint not found, using mock implementation');
            return this.createMockPaymentLink(ticketId);
          case 422:
            throw new Error(data.message || 'Validation error');
          default:
            throw new Error(data.message || 'Failed to create payment link');
        }
      } else if (error.request) {
        // If network error, try mock implementation
        logger.warn('Network error, using mock implementation');
        return this.createMockPaymentLink(ticketId);
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }



  /**
   * Create mock payment link for testing when backend is not ready
   * @param {string} ticketId - The ticket ID
   * @returns {Object} Mock payment response
   */
  createMockPaymentLink(ticketId) {
    logger.warn('Using mock payment implementation - backend PayFast endpoints not available');

    // Simulate different ticket types for testing
    const mockTickets = {
      'ticket-free': { price: 0, name: 'Free Entry' },
      'ticket-basic': { price: 50, name: 'Basic Ticket' },
      'ticket-premium': { price: 150, name: 'Premium Ticket' },
      'ticket-1': { price: 100, name: 'General Admission' },
      'ticket-2': { price: 250, name: 'VIP Pass' },
      'ticket-3': { price: 0, name: 'Free Entry' }
    };

    const ticket = mockTickets[ticketId] || { price: 99, name: 'Mock Ticket' };

    if (ticket.price === 0) {
      // Free ticket - no payment required
      return {
        success: true,
        payment_required: false,
        ticket_id: ticketId,
        ticket_name: ticket.name,
        amount: 0,
        currency: 'ZAR',
        status: 'confirmed',
        message: 'Free ticket confirmed successfully'
      };
    } else {
      // Paid ticket - return mock PayFast URL
      return {
        success: true,
        payment_required: true,
        payment_url: `https://sandbox.payfast.co.za/eng/process?merchant_id=10000100&merchant_key=46f0cd694581a&amount=${ticket.price}&item_name=${encodeURIComponent(ticket.name)}&return_url=${encodeURIComponent(window.location.origin + '/payment/success?ticket_id=' + ticketId)}&cancel_url=${encodeURIComponent(window.location.origin + '/payment/cancel?ticket_id=' + ticketId)}`,
        ticket_id: ticketId,
        ticket_name: ticket.name,
        amount: ticket.price,
        currency: 'ZAR',
        merchant_id: '10000100',
        merchant_key: '46f0cd694581a',
        message: 'Mock payment link created - this will redirect to PayFast sandbox'
      };
    }
  }

  /**
   * Check ticket purchase status
   * Useful for users who return later or close browser during payment
   *
   * @param {string} ticketId - The ticket ID to check status for
   * @returns {Promise<Object>} Ticket status information
   */
  async checkTicketStatus(ticketId) {
    try {
      logger.info('Checking ticket status:', ticketId);

      const response = await axios.get(
        `${this.baseUrl}/api/tickets/${ticketId}/status`,
        { headers: this.getAuthHeaders() }
      );

      logger.info('Ticket status retrieved:', response.data);
      return response.data;

    } catch (error) {
      logger.error('Failed to check ticket status:', error);

      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case 401:
            throw new Error('Please log in to check ticket status');
          case 403:
            throw new Error('You are not authorized to view this ticket');
          case 404:
            // If 404, try mock implementation
            logger.warn('Ticket status endpoint not found, using mock implementation');
            return this.createMockTicketStatus(ticketId);
          default:
            throw new Error(data.message || 'Failed to check ticket status');
        }
      } else if (error.request) {
        // If network error, try mock implementation
        logger.warn('Network error, using mock ticket status');
        return this.createMockTicketStatus(ticketId);
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }

  /**
   * Create mock ticket status for testing
   * @param {string} ticketId - The ticket ID
   * @returns {Object} Mock ticket status
   */
  createMockTicketStatus(ticketId) {
    logger.warn('Using mock ticket status - backend endpoints not available');

    // Simulate different statuses for testing
    const mockStatuses = ['confirmed', 'pending', 'failed'];
    const randomStatus = mockStatuses[Math.floor(Math.random() * mockStatuses.length)];

    return {
      ticket_id: ticketId,
      status: randomStatus,
      ticket: {
        id: ticketId,
        name: 'Mock Ticket',
        price: 99,
        currency: 'ZAR'
      },
      event: {
        id: 'mock-event-123',
        title: 'Mock Event for Testing',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        location: 'Mock Venue, Test City'
      },
      payment: randomStatus === 'confirmed' ? {
        payment_id: 'mock-payment-' + Date.now(),
        amount: 99,
        currency: 'ZAR',
        date: new Date().toISOString()
      } : null,
      message: `Mock ticket status: ${randomStatus}`
    };
  }

  /**
   * Redirect to PayFast payment page
   *
   * @param {string} paymentUrl - PayFast payment URL from backend
   */
  redirectToPayFast(paymentUrl) {
    try {
      logger.info('Redirecting to PayFast:', paymentUrl);

      // Validate URL before redirect
      if (!paymentUrl || !paymentUrl.startsWith('http')) {
        throw new Error('Invalid payment URL received from server');
      }

      // Redirect to PayFast
      window.location.href = paymentUrl;

    } catch (error) {
      logger.error('Failed to redirect to PayFast:', error);
      throw error;
    }
  }

  /**
   * Generate return URLs for PayFast
   * These are the URLs PayFast will redirect to after payment
   * 
   * @param {string} ticketId - Ticket ID for reference
   * @returns {Object} Return and cancel URLs
   */
  generateReturnUrls(ticketId) {
    const baseUrl = window.location.origin;
    
    return {
      return_url: `${baseUrl}/payment/success?ticket_id=${ticketId}`,
      cancel_url: `${baseUrl}/payment/cancel?ticket_id=${ticketId}`,
      notify_url: `${this.baseUrl}/payfast/webhook` // Backend webhook URL
    };
  }

  /**
   * Parse URL parameters from PayFast return
   * 
   * @param {string} url - Current URL with parameters
   * @returns {Object} Parsed parameters
   */
  parseReturnParams(url = window.location.href) {
    try {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      
      return {
        ticket_id: params.get('ticket_id'),
        payment_status: params.get('payment_status'),
        pf_payment_id: params.get('pf_payment_id'),
        payment_id: params.get('payment_id'),
        item_name: params.get('item_name'),
        amount_gross: params.get('amount_gross')
      };
    } catch (error) {
      logger.error('Failed to parse return parameters:', error);
      return {};
    }
  }

  /**
   * Validate if user is authenticated
   * 
   * @returns {boolean} True if user is logged in
   */
  isAuthenticated() {
    const token = getAuthToken();
    return !!token;
  }

  /**
   * Get user info for payment context
   * 
   * @returns {Object|null} User information or null if not logged in
   */
  getUserInfo() {
    try {
      const userdata = localStorage.getItem('userdata');
      return userdata ? JSON.parse(userdata) : null;
    } catch (error) {
      logger.error('Failed to get user info:', error);
      return null;
    }
  }

  /**
   * Log payment attempt for analytics/debugging
   * 
   * @param {string} ticketId - Ticket ID
   * @param {string} action - Action taken (create_link, redirect, success, cancel, error)
   * @param {Object} data - Additional data to log
   */
  logPaymentEvent(ticketId, action, data = {}) {
    try {
      const user = this.getUserInfo();
      const logData = {
        ticket_id: ticketId,
        action,
        user_id: user?.id || 'anonymous',
        timestamp: new Date().toISOString(),
        ...data
      };
      
      logger.info('Payment event:', logData);
      
      // Could send to analytics service here
      // analytics.track('payment_event', logData);
      
    } catch (error) {
      logger.error('Failed to log payment event:', error);
    }
  }
}

// Export singleton instance
const mvpPaymentService = new MVPPaymentService();
export default mvpPaymentService;

// Export class for testing
export { MVPPaymentService };
