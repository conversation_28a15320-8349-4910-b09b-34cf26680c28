/**
 * New Events Page
 * 
 * Redesigned events page using the new event API structure.
 * Displays public events with filtering, search, and registration capabilities.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiTrendingUp,
  FiUsers,
  FiMapPin,
  FiStar,
  FiInfo,
  FiExternalLink
} from 'react-icons/fi';
import NewEventsList from '../../components/events/NewEventsList';
import NewEventCard from '../../components/events/NewEventCard';
import { useNotification } from '../../contexts/NotificationContext';
import newEventService from '../../services/newEventService';

const NewEventsPage = () => {
  const navigate = useNavigate();
  const { showInfo } = useNotification();
  
  // State for featured events
  const [featuredEvents, setFeaturedEvents] = useState([]);
  const [featuredLoading, setFeaturedLoading] = useState(true);

  // Load featured events
  React.useEffect(() => {
    const loadFeaturedEvents = async () => {
      try {
        setFeaturedLoading(true);
        const response = await newEventService.getEvents({
          featured: true,
          limit: 3
        });
        setFeaturedEvents(response.events || []);
      } catch (error) {
        console.error('Failed to load featured events:', error);
      } finally {
        setFeaturedLoading(false);
      }
    };

    loadFeaturedEvents();
  }, []);

  // Handle event selection
  const handleEventSelect = (event) => {
    // Navigate to event details page
    navigate(`/events/${event.id}`);
  };

  // Handle registration success
  const handleRegistrationSuccess = (result) => {
    showInfo('Registration successful! Check your email for confirmation.');
    
    // Log the successful registration
    newEventService.logEventAction('registration_completed', {
      registration_id: result.registration_id,
      event_id: result.event?.id
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Discover Amazing Events
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Join workshops, conferences, and networking events that will advance your career and expand your knowledge.
            </p>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <FiCalendar className="w-6 h-6 mr-2" />
                  <span className="text-2xl font-bold">50+</span>
                </div>
                <p className="text-blue-100">Events This Month</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <FiUsers className="w-6 h-6 mr-2" />
                  <span className="text-2xl font-bold">2,500+</span>
                </div>
                <p className="text-blue-100">Active Participants</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <FiTrendingUp className="w-6 h-6 mr-2" />
                  <span className="text-2xl font-bold">95%</span>
                </div>
                <p className="text-blue-100">Satisfaction Rate</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Events Section */}
      {featuredEvents.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <FiStar className="w-6 h-6 text-yellow-500 mr-2" />
              <h2 className="text-3xl font-bold text-gray-900">Featured Events</h2>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Don't miss these specially curated events that offer exceptional learning and networking opportunities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredEvents.map((event) => (
              <NewEventCard
                key={event.id}
                event={event}
                variant="featured"
                onViewDetails={handleEventSelect}
                onRegistrationSuccess={handleRegistrationSuccess}
              />
            ))}
          </div>
        </div>
      )}

      {/* All Events Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">All Events</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Browse our complete collection of events. Use filters to find exactly what you're looking for.
          </p>
        </div>

        <NewEventsList
          onEventSelect={handleEventSelect}
          showFilters={true}
          showSearch={true}
          showPagination={true}
          defaultView="grid"
          itemsPerPage={12}
        />
      </div>

      {/* Call to Action Section */}
      <div className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of professionals who are advancing their careers through our events.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => navigate('/auth/signup')}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Create Account
              </button>
              <button
                onClick={() => navigate('/events')}
                className="px-8 py-3 border border-gray-600 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium"
              >
                Browse Events
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Backend Status Notice */}
      <div className="bg-green-50 border-t border-green-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-center text-center">
            <FiInfo className="w-5 h-5 text-green-600 mr-2 flex-shrink-0" />
            <div className="text-sm text-green-800">
              <strong>✅ Registration Flow:</strong> Click "Register" → Select tickets on details page → Complete registration!
              <a
                href="/test/new-events"
                className="underline hover:no-underline ml-1"
              >
                Test flow
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewEventsPage;
