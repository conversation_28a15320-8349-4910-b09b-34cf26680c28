import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiCopy,
  FiEye,
  FiBookOpen,
  FiClock,
  FiUsers,
  FiSearch,
  FiFilter,
  FiInfo
} from 'react-icons/fi';
import { 
  getMyExams, 
  deleteExam, 
  copyExam, 
  getExamUsageInfo 
} from '../../services/examService';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const ExamManagementPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [selectedExam, setSelectedExam] = useState(null);
  const [showUsageInfo, setShowUsageInfo] = useState(false);
  const [usageInfo, setUsageInfo] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    difficulty: 'all',
    status: 'all'
  });

  useEffect(() => {
    loadExams();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [exams, filters]);

  const loadExams = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getMyExams();
      setExams(response.exams || []);
    } catch (err) {
      console.error('Error loading exams:', err);
      setError('Failed to load exams');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...exams];

    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(exam =>
        exam.title.toLowerCase().includes(searchTerm) ||
        exam.description.toLowerCase().includes(searchTerm) ||
        exam.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Difficulty filter
    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(exam => exam.difficulty_level === filters.difficulty);
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(exam => exam.status === filters.status);
    }

    setFilteredExams(filtered);
  };

  const handleCreateExam = () => {
    navigate('/exams/create');
  };

  const handleEditExam = (exam) => {
    navigate(`/exams/${exam.id}/edit`);
  };

  const handleViewExam = (exam) => {
    navigate(`/exams/${exam.id}`);
  };

  const handleDeleteExam = async (exam) => {
    if (!window.confirm(`Are you sure you want to delete "${exam.title}"?`)) {
      return;
    }

    try {
      await deleteExam(exam.id);
      await loadExams();
    } catch (err) {
      console.error('Error deleting exam:', err);
      setError('Failed to delete exam');
    }
  };

  const handleCopyExam = async (exam) => {
    try {
      const copiedExam = await copyExam(exam.id, {
        title: `${exam.title} (Copy)`,
        description: `Copy of ${exam.description}`
      });
      
      await loadExams();
      navigate(`/exams/${copiedExam.id}/edit`);
    } catch (err) {
      console.error('Error copying exam:', err);
      setError('Failed to copy exam');
    }
  };

  const handleViewUsage = async (exam) => {
    try {
      setSelectedExam(exam);
      const usage = await getExamUsageInfo(exam.id);
      setUsageInfo(usage);
      setShowUsageInfo(true);
    } catch (err) {
      console.error('Error fetching usage info:', err);
      setError('Failed to fetch usage information');
    }
  };

  const getDifficultyColor = (level) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      draft: 'bg-gray-100 text-gray-800',
      archived: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Exams</h1>
          <p className="text-gray-600 mt-1">Create and manage your exam library</p>
        </div>
        
        <button
          onClick={handleCreateExam}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Create Exam
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage message={error} />
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                placeholder="Search exams..."
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <select
            value={filters.difficulty}
            onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {/* Exams Grid */}
      {filteredExams.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredExams.map((exam) => (
            <div key={exam.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{exam.title}</h3>
                    <p className="text-sm text-gray-600 line-clamp-2">{exam.description}</p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <FiBookOpen className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">{exam.questions_count} questions</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FiClock className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">{formatDuration(exam.duration_minutes)}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <FiUsers className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">Used {exam.usage_count} times</span>
                    </div>
                    <span className="text-gray-500">
                      {new Date(exam.updated_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exam.difficulty_level)}`}>
                      {exam.difficulty_level}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(exam.status)}`}>
                      {exam.status}
                    </span>
                  </div>
                </div>

                {exam.tags && exam.tags.length > 0 && (
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-1">
                      {exam.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                      {exam.tags.length > 3 && (
                        <span className="text-xs text-gray-500">+{exam.tags.length - 3} more</span>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleViewExam(exam)}
                    className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <FiEye className="h-4 w-4 inline mr-1" />
                    View
                  </button>
                  
                  <button
                    onClick={() => handleEditExam(exam)}
                    className="flex-1 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                  >
                    <FiEdit className="h-4 w-4 inline mr-1" />
                    Edit
                  </button>
                  
                  <div className="relative">
                    <button
                      onClick={() => handleViewUsage(exam)}
                      className="p-2 text-gray-400 hover:text-gray-600"
                      title="View Usage"
                    >
                      <FiInfo className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <button
                    onClick={() => handleCopyExam(exam)}
                    className="p-2 text-gray-400 hover:text-gray-600"
                    title="Copy Exam"
                  >
                    <FiCopy className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDeleteExam(exam)}
                    className="p-2 text-red-400 hover:text-red-600"
                    title="Delete Exam"
                  >
                    <FiTrash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Exams Found</h3>
          <p className="text-gray-600 mb-6">
            {filters.search || filters.difficulty !== 'all' || filters.status !== 'all'
              ? 'No exams match your current filters.'
              : 'Create your first exam to get started.'}
          </p>
          <button
            onClick={handleCreateExam}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Your First Exam
          </button>
        </div>
      )}

      {/* Usage Info Modal */}
      {showUsageInfo && selectedExam && usageInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Usage Information: {selectedExam.title}
            </h3>
            
            <div className="space-y-4">
              <div>
                <span className="text-sm font-medium text-gray-700">Total Usage:</span>
                <p className="text-sm text-gray-600">{usageInfo.usage_count} times</p>
              </div>
              
              {usageInfo.active_competitions?.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-gray-700">Active Competitions:</span>
                  <ul className="text-sm text-gray-600 mt-1">
                    {usageInfo.active_competitions.map((comp, index) => (
                      <li key={index}>• {comp.title} ({comp.participants} participants)</li>
                    ))}
                  </ul>
                </div>
              )}
              
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <p className="text-sm text-blue-800">
                  <strong>Recommendation:</strong> {usageInfo.reason}
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-end space-x-4 mt-6">
              <button
                onClick={() => setShowUsageInfo(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamManagementPage;
