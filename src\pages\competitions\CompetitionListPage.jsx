import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiUsers,
  FiAward,
  <PERSON><PERSON>lock,
  FiFilter,
  FiSearch,
  FiTrendingUp
} from 'react-icons/fi';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import CompetitionCard from '../../components/competitions/CompetitionCard';
import CompetitionRegistration from '../../components/competitions/CompetitionRegistration';

const CompetitionListPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [competitions, setCompetitions] = useState([]);
  const [filteredCompetitions, setFilteredCompetitions] = useState([]);
  const [selectedCompetition, setSelectedCompetition] = useState(null);
  const [showRegistration, setShowRegistration] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all',
    search: ''
  });

  // Mock data for development - replace with actual API call
  useEffect(() => {
    loadCompetitions();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [competitions, filters]);

  const loadCompetitions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock data - replace with actual API call
      const mockCompetitions = [
        {
          id: '1',
          title: 'Programming Challenge 2024',
          description: 'Test your coding skills in this comprehensive programming competition covering algorithms, data structures, and problem-solving.',
          start_datetime: '2024-12-15T10:00:00Z',
          end_datetime: '2024-12-15T13:00:00Z',
          max_attendees: 100,
          current_participants: 45,
          status: 'upcoming',
          category: 'programming',
          prize_details: {
            first_place: '$500',
            second_place: '$300',
            third_place: '$100'
          },
          institute_name: 'Tech University',
          difficulty_level: 'intermediate'
        },
        {
          id: '2',
          title: 'Mathematics Olympiad',
          description: 'Challenge yourself with advanced mathematical problems and compete with the best minds.',
          start_datetime: '2024-12-20T09:00:00Z',
          end_datetime: '2024-12-20T12:00:00Z',
          max_attendees: 50,
          current_participants: 32,
          status: 'upcoming',
          category: 'mathematics',
          prize_details: {
            first_place: 'Gold Medal',
            second_place: 'Silver Medal',
            third_place: 'Bronze Medal'
          },
          institute_name: 'Science Academy',
          difficulty_level: 'advanced'
        },
        {
          id: '3',
          title: 'Web Development Sprint',
          description: 'Build a complete web application in 4 hours using modern technologies.',
          start_datetime: '2024-12-10T14:00:00Z',
          end_datetime: '2024-12-10T18:00:00Z',
          max_attendees: 75,
          current_participants: 75,
          status: 'active',
          category: 'programming',
          prize_details: {
            first_place: 'Internship Opportunity',
            second_place: '$200',
            third_place: '$100'
          },
          institute_name: 'Code Institute',
          difficulty_level: 'beginner'
        }
      ];
      
      setCompetitions(mockCompetitions);
    } catch (err) {
      console.error('Error loading competitions:', err);
      setError('Failed to load competitions');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...competitions];

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(comp => comp.status === filters.status);
    }

    // Category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(comp => comp.category === filters.category);
    }

    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(comp =>
        comp.title.toLowerCase().includes(searchTerm) ||
        comp.description.toLowerCase().includes(searchTerm) ||
        comp.institute_name.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredCompetitions(filtered);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleRegister = (competition) => {
    setSelectedCompetition(competition);
    setShowRegistration(true);
  };

  const handleRegistrationSuccess = () => {
    setShowRegistration(false);
    setSelectedCompetition(null);
    // Refresh competitions to update participant count
    loadCompetitions();
  };

  const handleViewDetails = (competition) => {
    navigate(`/competitions/${competition.id}`);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      upcoming: { color: 'blue', label: 'Upcoming' },
      active: { color: 'green', label: 'Active' },
      completed: { color: 'gray', label: 'Completed' }
    };

    const config = statusConfig[status] || { color: 'gray', label: status };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${config.color}-100 text-${config.color}-800`}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Competitions</h1>
          <p className="text-gray-600 mt-1">Discover and participate in exciting competitions</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">
            {filteredCompetitions.length} competition(s) available
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="md:col-span-2">
            <div className="relative">
              <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search competitions..."
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="upcoming">Upcoming</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          {/* Category Filter */}
          <div>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="programming">Programming</option>
              <option value="mathematics">Mathematics</option>
              <option value="science">Science</option>
              <option value="general">General Knowledge</option>
            </select>
          </div>
        </div>
      </div>

      {/* Competition Grid */}
      {filteredCompetitions.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCompetitions.map((competition) => (
            <div key={competition.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              {/* Competition Header */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {competition.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {competition.description}
                    </p>
                  </div>
                  {getStatusBadge(competition.status)}
                </div>

                {/* Competition Details */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <FiCalendar className="h-4 w-4" />
                    <span>
                      {new Date(competition.start_datetime).toLocaleDateString()} at{' '}
                      {new Date(competition.start_datetime).toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <FiClock className="h-4 w-4" />
                    <span>
                      Duration: {Math.round(
                        (new Date(competition.end_datetime) - new Date(competition.start_datetime)) / (1000 * 60)
                      )} minutes
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <FiUsers className="h-4 w-4" />
                    <span>
                      {competition.current_participants} / {competition.max_attendees} participants
                    </span>
                  </div>

                  {competition.prize_details?.first_place && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <FiAward className="h-4 w-4" />
                      <span>Prize: {competition.prize_details.first_place}</span>
                    </div>
                  )}
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Participants</span>
                    <span>{Math.round((competition.current_participants / competition.max_attendees) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ 
                        width: `${Math.min((competition.current_participants / competition.max_attendees) * 100, 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleViewDetails(competition)}
                    className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    View Details
                  </button>
                  
                  {competition.status === 'upcoming' && competition.current_participants < competition.max_attendees && (
                    <button
                      onClick={() => handleRegister(competition)}
                      className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                    >
                      Register
                    </button>
                  )}
                  
                  {competition.status === 'active' && (
                    <button
                      onClick={() => navigate(`/competitions/${competition.id}/participate`)}
                      className="flex-1 px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
                    >
                      Join Now
                    </button>
                  )}
                  
                  {competition.current_participants >= competition.max_attendees && (
                    <button
                      disabled
                      className="flex-1 px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded-md cursor-not-allowed"
                    >
                      Full
                    </button>
                  )}
                </div>
              </div>

              {/* Institute Info */}
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Organized by {competition.institute_name}</span>
                  <span className="capitalize">{competition.difficulty_level} level</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FiTrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Competitions Found</h3>
          <p className="text-gray-600">
            {filters.status !== 'all' || filters.category !== 'all' || filters.search
              ? 'No competitions match your current filters. Try adjusting your search criteria.'
              : 'No competitions are currently available. Check back later for new opportunities!'}
          </p>
        </div>
      )}

      {/* Registration Modal */}
      {showRegistration && selectedCompetition && (
        <CompetitionRegistration
          competition={selectedCompetition}
          isOpen={showRegistration}
          onClose={() => setShowRegistration(false)}
          onSuccess={handleRegistrationSuccess}
          currentUser={{ username: 'John Doe', email: '<EMAIL>' }} // Replace with actual user data
        />
      )}
    </div>
  );
};

export default CompetitionListPage;
