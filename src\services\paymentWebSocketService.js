/**
 * Payment WebSocket Service
 * 
 * Handles real-time payment tracking via WebSocket connections
 * with automatic fallback to HTTP polling for reliability.
 * Based on the backend WebSocket payment pipeline implementation.
 */

import { BASE_API } from '../utils/api/API_URL';
import { getAuthToken } from '../utils/helpers/authHelpers';
import logger from '../utils/helpers/logger';

class PaymentWebSocketService {
  constructor() {
    this.ws = null;
    this.bookingId = null;
    this.token = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.heartbeatInterval = null;
    this.fallbackInterval = null;
    this.listeners = new Map();
    this.connectionTimeout = 15 * 60 * 1000; // 15 minutes
    this.connectionTimer = null;
  }

  /**
   * Connect to payment WebSocket
   * @param {string} bookingId - Booking ID for payment tracking
   * @param {Object} options - Connection options
   */
  async connect(bookingId, options = {}) {
    if (this.isConnecting || this.isConnected) {
      logger.warn('WebSocket already connecting or connected');
      return;
    }

    this.bookingId = bookingId;
    this.token = getAuthToken();
    this.isConnecting = true;

    if (!this.token) {
      this.emit('error', new Error('Authentication token not found'));
      return;
    }

    try {
      // Convert HTTP URL to WebSocket URL
      const wsBaseUrl = BASE_API.replace('https://', 'wss://').replace('http://', 'ws://');
      const wsUrl = `${wsBaseUrl}/api/events/ws/payment/${bookingId}?token=${this.token}`;
      
      logger.info('Connecting to payment WebSocket:', wsUrl);
      
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
      
      // Set connection timeout
      this.connectionTimer = setTimeout(() => {
        if (!this.isConnected) {
          this.emit('timeout', { message: 'Payment session timed out' });
          this.disconnect();
        }
      }, this.connectionTimeout);

    } catch (error) {
      logger.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.emit('error', error);
      this.startFallbackPolling();
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    if (!this.ws) return;

    this.ws.onopen = () => {
      logger.info('Payment WebSocket connected successfully');
      this.isConnecting = false;
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      
      this.startHeartbeat();
      this.emit('connected');
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        logger.info('Received payment WebSocket message:', data);
        this.handleMessage(data);
      } catch (error) {
        logger.error('Failed to parse WebSocket message:', error);
        this.emit('error', error);
      }
    };

    this.ws.onclose = (event) => {
      logger.info('Payment WebSocket connection closed:', event.code, event.reason);
      this.isConnected = false;
      this.isConnecting = false;
      this.stopHeartbeat();
      
      if (event.code !== 1000) { // Not a normal closure
        this.emit('disconnected', event);
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      logger.error('Payment WebSocket error:', error);
      this.isConnecting = false;
      this.emit('error', error);
      this.startFallbackPolling();
    };
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(data) {
    switch (data.type) {
      case 'payment_success':
        this.emit('payment_success', data);
        this.clearConnectionTimer();
        break;
        
      case 'payment_failure':
        this.emit('payment_failure', data);
        this.clearConnectionTimer();
        break;
        
      case 'payment_pending':
        this.emit('payment_pending', data);
        break;
        
      case 'payment_timeout':
        this.emit('payment_timeout', data);
        this.clearConnectionTimer();
        break;
        
      case 'pong':
        // Heartbeat response
        break;
        
      default:
        logger.warn('Unknown message type:', data.type);
        this.emit('message', data);
    }
  }

  /**
   * Send message through WebSocket
   */
  sendMessage(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        logger.error('Failed to send WebSocket message:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage({ type: 'ping' });
    }, 30000); // 30 seconds
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, starting fallback polling');
      this.startFallbackPolling();
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isConnected && this.bookingId) {
        this.connect(this.bookingId);
      }
    }, delay);
  }

  /**
   * Start fallback HTTP polling
   */
  startFallbackPolling() {
    if (this.fallbackInterval) return;
    
    logger.info('Starting fallback HTTP polling for payment status');
    this.emit('fallback_started');
    
    this.fallbackInterval = setInterval(async () => {
      try {
        const response = await fetch(`${BASE_API}/api/events/booking-status/${this.bookingId}`, {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const status = await response.json();
          
          if (status.status !== 'pending') {
            this.handleFallbackStatus(status);
            this.stopFallbackPolling();
          }
        }
      } catch (error) {
        logger.error('Fallback polling error:', error);
      }
    }, 5000); // Poll every 5 seconds
  }

  /**
   * Handle fallback status response
   */
  handleFallbackStatus(status) {
    const eventType = status.status === 'completed' ? 'payment_success' : 'payment_failure';
    this.emit(eventType, {
      type: eventType,
      booking_id: this.bookingId,
      status: status.status,
      ticket_data: status.ticket_data,
      reason: status.reason || status.message,
      fallback: true
    });
  }

  /**
   * Stop fallback polling
   */
  stopFallbackPolling() {
    if (this.fallbackInterval) {
      clearInterval(this.fallbackInterval);
      this.fallbackInterval = null;
      logger.info('Stopped fallback HTTP polling');
    }
  }

  /**
   * Clear connection timer
   */
  clearConnectionTimer() {
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    this.clearConnectionTimer();
    this.stopHeartbeat();
    this.stopFallbackPolling();
    
    if (this.ws) {
      this.ws.close(1000, 'Normal closure');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.bookingId = null;
    this.token = null;
    this.reconnectAttempts = 0;
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          logger.error('Error in event listener:', error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      bookingId: this.bookingId,
      hasFallback: !!this.fallbackInterval
    };
  }
}

// Export singleton instance
export default new PaymentWebSocketService();
