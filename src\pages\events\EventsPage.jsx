import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  fetchPublicEvents,
  fetchFeaturedEvents,
  selectPublicEvents,
  selectPublicEventsLoading,
  selectFeaturedEvents,
  selectFeaturedEventsLoading
} from '../../store/slices/EventsSlice';
import EventFilters from '../../components/events/EventFilters';
import EventTabs from '../../components/events/EventTabs';
import PublicEventList from '../../components/events/PublicEventList';

import { PageContainer, PageHeader, SectionDivider } from '../../components/ui/layout';

const EventsPage = ({ viewMode = 'grid', embedded = false }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  
  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [filters, setFilters] = useState({
    category: null,
    status: null,
    type: null
  });

  // Redux state with robust error handling
  const rawPublicEvents = useSelector(selectPublicEvents);
  const publicEvents = Array.isArray(rawPublicEvents) ? rawPublicEvents : [];
  const publicEventsLoading = useSelector(selectPublicEventsLoading);
  const rawFeaturedEvents = useSelector(selectFeaturedEvents);
  const featuredEvents = Array.isArray(rawFeaturedEvents) ? rawFeaturedEvents : [];
  const featuredEventsLoading = useSelector(selectFeaturedEventsLoading);

  // Load initial data
  useEffect(() => {
    dispatch(fetchPublicEvents());
    dispatch(fetchFeaturedEvents({ limit: 6 }));
  }, [dispatch]);



  // Filter and search logic
  const getFilteredEvents = () => {
    let events = [];
    
    switch (activeTab) {
      case 'featured':
        events = featuredEvents;
        break;
      case 'competitions':
        events = publicEvents.filter(event => event.is_competition);
        break;
      default:
        events = publicEvents;
        break;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      events = events.filter(event =>
        event.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply other filters
    if (filters.category) {
      events = events.filter(event => event.category === filters.category);
    }

    if (filters.status) {
      events = events.filter(event => event.status === filters.status);
    }

    if (filters.type) {
      if (filters.type === 'featured') {
        events = events.filter(event => event.is_featured);
      } else if (filters.type === 'competition') {
        events = events.filter(event => event.is_competition);
      }
    }

    return Array.isArray(events) ? events : [];
  };

  // Event handlers
  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      category: null,
      status: null,
      type: null
    });
    setSearchQuery('');
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleToggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const handleViewEventDetails = (event) => {
    navigate(`/events/${event.id}`);
  };

  const handleRegisterForEvent = (event) => {
    // Navigate to event details page where user can select tickets
    console.log('Navigating to event details for registration:', event);
    navigate(`/events/${event.id}`);
  };

  const handleReload = () => {
    dispatch(fetchPublicEvents());
    dispatch(fetchFeaturedEvents({ limit: 6 }));
  };

  // Calculate event counts for tabs
  const eventCounts = {
    all: publicEvents.length,
    featured: featuredEvents.length,
    competitions: publicEvents.filter(e => e.is_competition).length
  };

  const filteredEvents = getFilteredEvents();
  const isLoading = publicEventsLoading || featuredEventsLoading;

  const content = (
    <>
      {/* Search and Filters */}
      <div className="mb-6">
        <EventFilters
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          filters={filters}
          onFilterChange={handleFilterChange}
          showFilters={showFilters}
          onToggleFilters={handleToggleFilters}
          onClearFilters={handleClearFilters}
          onReload={handleReload}
          isLoading={isLoading}
        />
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <EventTabs
          activeTab={activeTab}
          onTabChange={handleTabChange}
          eventCounts={eventCounts}
        />
      </div>

      {/* Events List */}
      <PublicEventList
        events={filteredEvents}
        loading={isLoading}
        title={`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Events`}
        onViewDetails={handleViewEventDetails}
        onRegister={handleRegisterForEvent}
        viewMode={viewMode}
      />
    </>
  );

  if (embedded) {
    return content;
  }

  return (
    <PageContainer maxWidth="2xl" padding="lg">
      {/* Page Header */}
      <PageHeader
        title="Events"
        description="Browse workshops, conferences, competitions and networking events"
        variant="centered"
        size="large"
      />

      <SectionDivider />

      {content}
    </PageContainer>
  );
};

export default EventsPage;
