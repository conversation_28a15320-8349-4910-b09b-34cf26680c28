/**
 * MyBookings Component - Student's Event Bookings
 * Shows all user's event tickets and bookings
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiMapPin,
  FiClock,
  FiDownload,
  FiQrCode,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiAlertCircle
} from 'react-icons/fi';
import { format } from 'date-fns';
import BookingService from '../../services/bookingService';
import { LoadingSpinner } from '../../components/ui';
import { useNotification } from '../../contexts/NotificationContext';

const MyBookings = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [bookings, setBookings] = useState([]);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await BookingService.getUserBookings();
      setBookings(data.bookings || []);
    } catch (err) {
      setError(err.message);
      showError('Failed to load your bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
    showSuccess('Bookings refreshed');
  };

  const handleDownloadTicket = async (bookingId) => {
    try {
      const blob = await BookingService.downloadTicket(bookingId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ticket-${bookingId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showSuccess('Ticket downloaded successfully');
    } catch (err) {
      showError('Failed to download ticket');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'confirmed':
        return <FiCheck className="w-5 h-5 text-green-600" />;
      case 'pending':
        return <FiClock className="w-5 h-5 text-yellow-600" />;
      case 'failed':
      case 'cancelled':
        return <FiX className="w-5 h-5 text-red-600" />;
      default:
        return <FiAlertCircle className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600 mt-4">Loading your bookings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Event Bookings</h1>
            <p className="text-gray-600 mt-2">View and manage your event tickets</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <FiAlertCircle className="w-5 h-5 text-red-600 mr-3" />
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        )}

        {/* Bookings List */}
        {bookings.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
            <p className="text-gray-600 mb-6">You haven't booked any events yet.</p>
            <button
              onClick={() => navigate('/student/events')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Browse Events
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {bookings.map((booking) => (
              <div key={booking.booking_id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {booking.event_title}
                        </h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <FiCalendar className="w-4 h-4 mr-2" />
                          {format(new Date(booking.event_date), 'MMM dd, yyyy')}
                        </div>
                        <div className="flex items-center">
                          <FiMapPin className="w-4 h-4 mr-2" />
                          Event Location
                        </div>
                        <div className="flex items-center">
                          <FiClock className="w-4 h-4 mr-2" />
                          Booking ID: {booking.booking_id}
                        </div>
                      </div>
                      
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-gray-900">{booking.ticket_type}</p>
                            <p className="text-sm text-gray-600">Quantity: {booking.quantity}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">PKR {booking.total_paid}</p>
                            <p className="text-sm text-gray-600">Total Paid</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="ml-6 flex flex-col space-y-2">
                      {getStatusIcon(booking.status)}
                      
                      {booking.status === 'confirmed' && (
                        <>
                          {booking.qr_code && (
                            <button
                              onClick={() => window.open(booking.qr_code, '_blank')}
                              className="flex items-center space-x-2 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            >
                              <FiQrCode className="w-4 h-4" />
                              <span className="text-sm">QR Code</span>
                            </button>
                          )}
                          
                          {booking.ticket_pdf && (
                            <button
                              onClick={() => handleDownloadTicket(booking.booking_id)}
                              className="flex items-center space-x-2 px-3 py-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                            >
                              <FiDownload className="w-4 h-4" />
                              <span className="text-sm">Download</span>
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyBookings;
