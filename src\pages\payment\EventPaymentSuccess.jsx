/**
 * EventPaymentSuccess Component
 * 
 * Handles the success return flow from PayFast payments for event bookings
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  FiCheck,
  FiArrowLeft,
  FiDownload,
  FiCalendar,
  FiMapPin,
  FiClock,
  FiMail,
  FiRefreshCw,
  FiAlertCircle
} from 'react-icons/fi';
import { format } from 'date-fns';
import { EventBookingService } from '../../services/eventBookingService';
import BookingService from '../../services/bookingService';
import { LoadingSpinner } from '../../components/ui';
import { useNotification } from '../../contexts/NotificationContext';

const EventPaymentSuccess = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showSuccess, showError } = useNotification();
  
  // Get URL parameters
  const registrationId = searchParams.get('registration_id');
  const paymentId = searchParams.get('payment_id');
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [registration, setRegistration] = useState(null);
  const [error, setError] = useState(null);
  const [pollingStatus, setPollingStatus] = useState('checking');

  useEffect(() => {
    if (registrationId) {
      checkRegistrationStatus();
    } else {
      setError('No registration ID provided');
      setLoading(false);
    }
  }, [registrationId]);

  const checkRegistrationStatus = async () => {
    setLoading(true);
    setError(null);
    setPollingStatus('checking');

    try {
      // Use the new BookingService to poll for status
      const finalStatus = await BookingService.pollBookingStatus(
        registrationId,
        (status) => {
          // Status update callback
          setPollingStatus('polling');
          console.log('Booking status update:', status);
        },
        30, // max attempts
        2000 // 2 second interval
      );

      if (finalStatus.status === 'confirmed') {
        setRegistration(finalStatus);
        setPollingStatus('confirmed');
        showSuccess('Payment confirmed! Your event registration is complete.');
      } else if (finalStatus.status === 'failed') {
        setPollingStatus('failed');
        setError('Payment verification failed. Please contact support if you believe this is an error.');
      } else {
        setPollingStatus('timeout');
        setError('Payment verification is taking longer than expected. Please check your booking status later.');
      }
    } catch (err) {
      setError(err.message || 'Failed to verify payment status');
      setPollingStatus('error');
    } finally {
      setLoading(false);
    }
  };



  const handleRetryVerification = () => {
    checkRegistrationStatus();
  };

  const handleGoToEvents = () => {
    navigate('/student/events');
  };

  const handleGoToDashboard = () => {
    navigate('/student/dashboard');
  };

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'EEEE, MMMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  if (loading && pollingStatus === 'checking') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Verifying Payment
            </h2>
            <p className="text-gray-600">
              Please wait while we confirm your payment...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (pollingStatus === 'polling') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Processing Payment
            </h2>
            <p className="text-gray-600 mb-4">
              Your payment is being processed. This may take a few moments...
            </p>
            <div className="text-sm text-gray-500">
              Registration ID: {registrationId}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || pollingStatus === 'failed' || pollingStatus === 'timeout') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {pollingStatus === 'timeout' ? 'Verification Timeout' : 'Payment Issue'}
            </h2>
            <p className="text-gray-600 mb-6">
              {error}
            </p>
            <div className="space-y-3">
              <button
                onClick={handleRetryVerification}
                className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiRefreshCw className="w-4 h-4 mr-2" />
                Retry Verification
              </button>
              <button
                onClick={handleGoToEvents}
                className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FiArrowLeft className="w-4 h-4 mr-2" />
                Back to Events
              </button>
            </div>
            {registrationId && (
              <div className="mt-4 text-sm text-gray-500">
                Reference: {registrationId}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (pollingStatus === 'confirmed' && registration) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleGoToEvents}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Registration Confirmed
                </h1>
                <p className="text-sm text-gray-500">
                  Your event registration is complete
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Success Message */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div className="flex items-center">
              <FiCheck className="w-8 h-8 text-green-600 mr-4" />
              <div>
                <h2 className="text-lg font-semibold text-green-900">
                  Payment Successful!
                </h2>
                <p className="text-green-700">
                  Your registration for "{registration.event?.title}" has been confirmed.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Registration Details */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Registration Details
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Registration ID</label>
                  <p className="text-gray-900 font-mono">{registration.registration_id}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Ticket Type</label>
                  <p className="text-gray-900">{registration.ticket?.name}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Quantity</label>
                  <p className="text-gray-900">{registration.quantity} ticket(s)</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Total Amount</label>
                  <p className="text-gray-900 font-semibold">
                    {EventBookingService.formatCurrency(registration.total_amount)}
                  </p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Registration Date</label>
                  <p className="text-gray-900">
                    {format(new Date(registration.registered_at), 'PPP p')}
                  </p>
                </div>
              </div>
            </div>

            {/* Event Details */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Event Details
              </h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">{registration.event?.title}</h4>
                  <p className="text-sm text-gray-600">{registration.event?.description}</p>
                </div>
                
                <div className="flex items-center text-gray-600">
                  <FiCalendar className="w-4 h-4 mr-2" />
                  <div>
                    <p className="text-sm">{formatDate(registration.event?.start_date)}</p>
                    <p className="text-xs">
                      {formatTime(registration.event?.start_date)} - {formatTime(registration.event?.end_date)}
                    </p>
                  </div>
                </div>
                
                {registration.event?.location && (
                  <div className="flex items-center text-gray-600">
                    <FiMapPin className="w-4 h-4 mr-2" />
                    <div>
                      <p className="text-sm">{registration.event.location}</p>
                      <p className="text-xs">{registration.event.venue_address}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">
              What's Next?
            </h3>
            <div className="space-y-3 text-blue-800">
              <div className="flex items-center">
                <FiMail className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  A confirmation email with your ticket has been sent to your email address
                </span>
              </div>
              <div className="flex items-center">
                <FiDownload className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  You can download your ticket from your dashboard
                </span>
              </div>
              <div className="flex items-center">
                <FiClock className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  Please arrive 15 minutes before the event starts
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <button
              onClick={handleGoToDashboard}
              className="flex-1 flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiCalendar className="w-5 h-5 mr-2" />
              Go to Dashboard
            </button>
            <button
              onClick={handleGoToEvents}
              className="flex-1 flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FiArrowLeft className="w-5 h-5 mr-2" />
              Browse More Events
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default EventPaymentSuccess;
