import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>ch,
  FiFilter,
  FiBookOpen,
  FiClock,
  FiUsers,
  FiStar,
  FiCopy,
  FiInfo,
  FiCheck,
  FiX
} from 'react-icons/fi';
import { getAvailableExamsForCompetition, getExamUsageInfo } from '../../services/examService';
import { LoadingSpinner } from '../ui';

const ExamLibraryBrowser = ({ 
  isOpen, 
  onClose, 
  onSelectExam, 
  selectedExamId = null,
  showCopyOption = true 
}) => {
  const [loading, setLoading] = useState(false);
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [selectedExam, setSelectedExam] = useState(null);
  const [usageInfo, setUsageInfo] = useState(null);
  const [copyExam, setCopyExam] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    difficulty: 'all',
    duration: 'all',
    tags: []
  });

  useEffect(() => {
    if (isOpen) {
      loadExams();
    }
  }, [isOpen]);

  useEffect(() => {
    applyFilters();
  }, [exams, filters]);

  const loadExams = async () => {
    try {
      setLoading(true);
      const response = await getAvailableExamsForCompetition();
      setExams(response.exams || []);
    } catch (error) {
      console.error('Error loading exams:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...exams];

    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(exam =>
        exam.title.toLowerCase().includes(searchTerm) ||
        exam.description.toLowerCase().includes(searchTerm) ||
        exam.created_by.toLowerCase().includes(searchTerm) ||
        exam.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Difficulty filter
    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(exam => exam.difficulty_level === filters.difficulty);
    }

    // Duration filter
    if (filters.duration !== 'all') {
      const [min, max] = filters.duration.split('-').map(Number);
      filtered = filtered.filter(exam => {
        const duration = exam.duration_minutes;
        if (max) {
          return duration >= min && duration <= max;
        } else {
          return duration >= min;
        }
      });
    }

    setFilteredExams(filtered);
  };

  const handleExamSelect = async (exam) => {
    setSelectedExam(exam);
    
    if (showCopyOption) {
      try {
        const usage = await getExamUsageInfo(exam.id);
        setUsageInfo(usage);
        setCopyExam(usage.recommended_action === 'copy');
      } catch (error) {
        console.error('Error fetching usage info:', error);
        setUsageInfo(null);
        setCopyExam(false);
      }
    }
  };

  const handleConfirmSelection = () => {
    if (selectedExam) {
      onSelectExam({
        exam: selectedExam,
        copyExam: showCopyOption ? copyExam : false
      });
      onClose();
    }
  };

  const getDifficultyColor = (level) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const ExamCard = ({ exam, isSelected, onClick }) => (
    <div
      onClick={() => onClick(exam)}
      className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
        isSelected 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 mb-1">{exam.title}</h3>
          <p className="text-sm text-gray-600 line-clamp-2">{exam.description}</p>
        </div>
        {isSelected && (
          <FiCheck className="h-5 w-5 text-blue-600 ml-2 flex-shrink-0" />
        )}
      </div>

      <div className="flex items-center space-x-4 text-xs text-gray-500 mb-3">
        <div className="flex items-center space-x-1">
          <FiBookOpen className="h-3 w-3" />
          <span>{exam.questions_count} questions</span>
        </div>
        <div className="flex items-center space-x-1">
          <FiClock className="h-3 w-3" />
          <span>{formatDuration(exam.duration_minutes)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <FiUsers className="h-3 w-3" />
          <span>Used {exam.usage_count} times</span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exam.difficulty_level)}`}>
            {exam.difficulty_level}
          </span>
          {exam.rating && (
            <div className="flex items-center space-x-1">
              <FiStar className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-gray-600">{exam.rating}</span>
            </div>
          )}
        </div>
        <span className="text-xs text-gray-500">by {exam.created_by}</span>
      </div>

      {exam.tags && exam.tags.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {exam.tags.slice(0, 3).map((tag, index) => (
            <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
              {tag}
            </span>
          ))}
          {exam.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{exam.tags.length - 3} more</span>
          )}
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Exam Library</h2>
            <p className="text-gray-600 mt-1">Select an exam for your competition</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-200px)]">
          {/* Exam List */}
          <div className="flex-1 flex flex-col">
            {/* Filters */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-2">
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                      placeholder="Search exams..."
                      className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <select
                  value={filters.difficulty}
                  onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Levels</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>

                <select
                  value={filters.duration}
                  onChange={(e) => setFilters(prev => ({ ...prev, duration: e.target.value }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">Any Duration</option>
                  <option value="0-30">Under 30 min</option>
                  <option value="30-60">30-60 min</option>
                  <option value="60-120">1-2 hours</option>
                  <option value="120">2+ hours</option>
                </select>
              </div>
            </div>

            {/* Exam Grid */}
            <div className="flex-1 overflow-y-auto p-4">
              {loading ? (
                <div className="flex justify-center py-12">
                  <LoadingSpinner size="lg" />
                </div>
              ) : filteredExams.length > 0 ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {filteredExams.map((exam) => (
                    <ExamCard
                      key={exam.id}
                      exam={exam}
                      isSelected={selectedExam?.id === exam.id}
                      onClick={handleExamSelect}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Exams Found</h3>
                  <p className="text-gray-600">
                    {filters.search || filters.difficulty !== 'all' || filters.duration !== 'all'
                      ? 'No exams match your current filters.'
                      : 'No exams are available in the library.'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Selection Panel */}
          {selectedExam && (
            <div className="w-80 border-l border-gray-200 bg-gray-50 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Selected Exam</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">{selectedExam.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{selectedExam.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Questions:</span>
                    <p className="font-medium">{selectedExam.questions_count}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Duration:</span>
                    <p className="font-medium">{formatDuration(selectedExam.duration_minutes)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Level:</span>
                    <p className="font-medium capitalize">{selectedExam.difficulty_level}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Used:</span>
                    <p className="font-medium">{selectedExam.usage_count} times</p>
                  </div>
                </div>

                {showCopyOption && usageInfo && (
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center space-x-2 mb-3">
                      <FiInfo className="h-4 w-4 text-blue-600" />
                      <h4 className="font-medium text-gray-900">Copy Recommendation</h4>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{usageInfo.reason}</p>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={copyExam}
                        onChange={(e) => setCopyExam(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-700">Create a copy of this exam</span>
                    </label>
                    
                    {copyExam && (
                      <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-800">
                        <FiCopy className="h-3 w-3 inline mr-1" />
                        A copy will be created that you can modify without affecting the original
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirmSelection}
            disabled={!selectedExam}
            className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Select Exam
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExamLibraryBrowser;
