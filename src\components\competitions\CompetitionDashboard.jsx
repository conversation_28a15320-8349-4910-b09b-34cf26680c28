import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  FiUsers,
  FiTrendingUp,
  FiCalendar,
  FiClock,
  FiBarChart3,
  FiShield,
  FiEye
} from 'react-icons/fi';
import { 
  getCompetitionStatistics,
  getCompetitionAnalytics,
  getCompetitionMonitoringDashboard 
} from '../../services/competitionService';
import { LoadingSpinner, ErrorMessage } from '../ui';

const CompetitionDashboard = ({ competitionId, userRole = 'student' }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  const [monitoring, setMonitoring] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (competitionId) {
      loadDashboardData();
    }
  }, [competitionId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load statistics for all users
      const statsData = await getCompetitionStatistics(competitionId);
      setStatistics(statsData);

      // Load analytics for organizers and mentors
      if (['institute', 'mentor', 'admin'].includes(userRole)) {
        const analyticsData = await getCompetitionAnalytics(competitionId);
        setAnalytics(analyticsData);
      }

      // Load monitoring for organizers only
      if (['institute', 'admin'].includes(userRole)) {
        const monitoringData = await getCompetitionMonitoringDashboard(competitionId);
        setMonitoring(monitoringData);
      }

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600`} />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-semibold text-gray-900">{value}</h3>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
      </div>
    </div>
  );

  const TabButton = ({ id, label, isActive, onClick }) => (
    <button
      onClick={() => onClick(id)}
      className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      {label}
    </button>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Competition Dashboard</h2>
        <button
          onClick={loadDashboardData}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Refresh
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-2 border-b border-gray-200 pb-4">
        <TabButton
          id="overview"
          label="Overview"
          isActive={activeTab === 'overview'}
          onClick={setActiveTab}
        />
        {['institute', 'mentor', 'admin'].includes(userRole) && (
          <TabButton
            id="analytics"
            label="Analytics"
            isActive={activeTab === 'analytics'}
            onClick={setActiveTab}
          />
        )}
        {['institute', 'admin'].includes(userRole) && (
          <TabButton
            id="monitoring"
            label="Monitoring"
            isActive={activeTab === 'monitoring'}
            onClick={setActiveTab}
          />
        )}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && statistics && (
        <div className="space-y-6">
          {/* Statistics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              icon={FiUsers}
              title="Total Participants"
              value={statistics.total_participants || 0}
              subtitle="Registered students"
              color="blue"
            />
            <StatCard
              icon={FiAward}
              title="Submissions"
              value={statistics.total_submissions || 0}
              subtitle="Completed attempts"
              color="green"
            />
            <StatCard
              icon={FiClock}
              title="Average Score"
              value={`${statistics.average_score || 0}%`}
              subtitle="Overall performance"
              color="yellow"
            />
            <StatCard
              icon={FiTrendingUp}
              title="Completion Rate"
              value={`${statistics.completion_rate || 0}%`}
              subtitle="Finished vs started"
              color="purple"
            />
          </div>

          {/* Recent Activity */}
          {statistics.recent_activity && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {statistics.recent_activity.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-600">{activity.message}</span>
                    <span className="text-gray-400">{activity.timestamp}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && analytics && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Analytics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Score Distribution */}
              {analytics.score_distribution && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Score Distribution</h4>
                  <div className="space-y-2">
                    {Object.entries(analytics.score_distribution).map(([range, count]) => (
                      <div key={range} className="flex justify-between text-sm">
                        <span className="text-gray-600">{range}</span>
                        <span className="font-medium">{count} students</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Top Performers */}
              {analytics.top_performers && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Top Performers</h4>
                  <div className="space-y-2">
                    {analytics.top_performers.slice(0, 5).map((performer, index) => (
                      <div key={performer.id} className="flex justify-between text-sm">
                        <span className="text-gray-600">
                          #{index + 1} {performer.name}
                        </span>
                        <span className="font-medium">{performer.score}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Monitoring Tab */}
      {activeTab === 'monitoring' && monitoring && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              icon={FiEye}
              title="Active Sessions"
              value={monitoring.active_sessions || 0}
              subtitle="Currently taking exam"
              color="green"
            />
            <StatCard
              icon={FiShield}
              title="Security Alerts"
              value={monitoring.security_alerts || 0}
              subtitle="Potential violations"
              color="red"
            />
            <StatCard
              icon={FiBarChart3}
              title="System Load"
              value={`${monitoring.system_load || 0}%`}
              subtitle="Server performance"
              color="blue"
            />
          </div>

          {/* Live Activity Feed */}
          {monitoring.live_activity && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Activity</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {monitoring.live_activity.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3 text-sm py-2 border-b border-gray-100">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'start' ? 'bg-green-500' :
                      activity.type === 'submit' ? 'bg-blue-500' :
                      activity.type === 'violation' ? 'bg-red-500' : 'bg-gray-500'
                    }`}></div>
                    <span className="text-gray-600">{activity.message}</span>
                    <span className="text-gray-400 ml-auto">{activity.timestamp}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CompetitionDashboard;
